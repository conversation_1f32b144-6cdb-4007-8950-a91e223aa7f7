# Node.js 安装指南

## 问题说明

您遇到的"不是内部或外部命令"错误是因为系统中没有安装Node.js或Node.js没有正确配置到系统环境变量中。

## 解决方案

### 方案1：使用简化版系统（推荐）

我已经为您创建了一个不需要Node.js的纯HTML版本：

1. **直接运行**：双击 `启动系统.bat`
2. **手动打开**：直接双击 `erp-system.html` 文件
3. **功能完整**：包含完整的耗材管理功能
4. **数据保存**：数据保存在浏览器本地存储中

### 方案2：安装Node.js（完整版）

如果您需要使用完整的Node.js版本，请按以下步骤安装：

#### 步骤1：下载Node.js
1. 访问官网：https://nodejs.org/
2. 下载LTS版本（推荐）
3. 选择Windows Installer (.msi)

#### 步骤2：安装Node.js
1. 双击下载的.msi文件
2. 按照安装向导进行安装
3. 确保勾选"Add to PATH"选项
4. 完成安装后重启命令提示符

#### 步骤3：验证安装
打开命令提示符，输入：
```bash
node --version
npm --version
```

如果显示版本号，说明安装成功。

#### 步骤4：启动完整系统
```bash
cd backend
node src/app.js
```

## 两个版本的对比

### 简化版 (erp-system.html)
✅ **优点**：
- 无需安装任何软件
- 双击即可使用
- 界面美观，功能完整
- 耗材管理功能齐全

❌ **限制**：
- 数据只保存在浏览器中
- 无法连接外部数据库
- 无法提供API接口

### 完整版 (Node.js + Express)
✅ **优点**：
- 真实的数据库存储
- 完整的API接口
- 支持多用户访问
- 可扩展性强

❌ **要求**：
- 需要安装Node.js
- 需要一定的技术基础

## 推荐使用方案

### 立即使用（推荐）
运行 `启动系统.bat` 使用简化版，可以立即体验完整的财务管理功能。

### 长期使用
如果需要在企业环境中长期使用，建议安装Node.js使用完整版。

## 常见问题

### Q: 简化版的数据会丢失吗？
A: 数据保存在浏览器本地存储中，除非清除浏览器数据，否则不会丢失。

### Q: 可以导出数据吗？
A: 简化版暂不支持导出，完整版支持数据导出功能。

### Q: 多人可以同时使用吗？
A: 简化版只能单人使用，完整版支持多人同时访问。

### Q: 如何备份数据？
A: 简化版建议定期截图保存重要数据，完整版会自动保存到数据库文件。

## 技术支持

如果您在安装或使用过程中遇到问题，可以：

1. 查看 `README.md` 文件
2. 使用 `test-frontend.html` 测试功能
3. 参考 `项目总结.md` 了解系统详情

---

**建议**：先使用简化版体验系统功能，如果满意再考虑安装Node.js使用完整版。
