# 企业财务管理系统 (ERP)

基于您的2025年5月账单表格开发的现代化企业财务管理系统。

## 系统功能

### 核心模块
1. **耗材管理** - 物料库存、采购、消耗记录管理
2. **员工支出管理** - 员工费用申请、审批、报销流程
3. **财务统计** - 收支统计、利润分析、财务报表生成
4. **客户结算** - 客户账单管理、收款记录、应收账款
5. **平方结算** - 按面积计费的项目结算管理
6. **付款管理** - 付款申请、审批流程、付款记录
7. **日报管理** - 每日业务数据录入和统计查看
8. **基础数据管理** - 客户、供应商、产品、员工等信息
9. **价格管理** - 产品定价、服务单价设置

### 技术架构
- **前端**: React 18 + Vite + Ant Design 5
- **后端**: Node.js + Express + SQLite
- **状态管理**: React Context API
- **图表**: Ant Design Charts
- **样式**: CSS Modules + Ant Design

## 项目结构

```
erp/
├── frontend/                 # 前端应用
│   ├── src/
│   │   ├── components/       # 通用组件
│   │   ├── pages/           # 页面组件
│   │   ├── context/         # 状态管理
│   │   ├── services/        # API服务
│   │   ├── utils/           # 工具函数
│   │   └── styles/          # 样式文件
│   ├── public/              # 静态资源
│   └── package.json
├── backend/                 # 后端API
│   ├── src/
│   │   ├── routes/          # 路由
│   │   ├── models/          # 数据模型
│   │   ├── controllers/     # 控制器
│   │   ├── middleware/      # 中间件
│   │   └── database/        # 数据库配置
│   ├── database.sqlite      # SQLite数据库
│   └── package.json
└── README.md
```

## 页面结构

### 1. 主导航
- 仪表板 (Dashboard)
- 耗材管理
- 员工支出
- 客户结算
- 平方结算
- 付款管理
- 财务统计
- 日报管理
- 基础设置

### 2. 仪表板
- 今日收支概览
- 待处理事项
- 财务趋势图表
- 快捷操作入口

### 3. 各功能模块
每个模块包含：
- 数据列表页 (支持搜索、筛选、排序)
- 新增/编辑表单页
- 详情查看页
- 统计报表页

## 数据库设计

### 主要数据表
1. **materials** - 耗材表
2. **employee_expenses** - 员工支出表
3. **customers** - 客户表
4. **customer_settlements** - 客户结算表
5. **area_settlements** - 平方结算表
6. **payments** - 付款记录表
7. **daily_reports** - 日报表
8. **price_settings** - 价格设置表
9. **employees** - 员工表
10. **suppliers** - 供应商表

## 开发计划

### 第一阶段：基础框架搭建 ✅
- [x] 项目初始化
- [x] 前端React应用搭建
- [x] 后端Express服务搭建
- [x] 数据库设计和初始化

### 第二阶段：核心功能开发 🚧
- [x] 用户界面设计
- [x] 基础数据管理
- [x] 耗材管理模块（完整功能）
- [ ] 员工支出模块

### 第三阶段：业务功能完善 📋
- [ ] 客户结算模块
- [ ] 平方结算模块
- [ ] 付款管理模块
- [ ] 财务统计模块

### 第四阶段：报表和优化 📋
- [ ] 日报管理
- [ ] 财务报表生成
- [ ] 系统优化
- [ ] 测试和部署

## 当前完成状态

### ✅ 已完成功能
1. **项目架构搭建**
   - React + Vite 前端框架
   - Express + SQLite 后端API
   - 完整的项目目录结构

2. **数据库设计**
   - 9个核心数据表
   - 示例数据初始化
   - 数据库连接和操作封装

3. **后端API服务**
   - RESTful API设计
   - 耗材管理完整CRUD
   - 员工、客户基础接口
   - 仪表板统计接口

4. **前端界面**
   - 现代化响应式布局
   - Ant Design组件库
   - 路由导航系统
   - 耗材管理完整功能

5. **耗材管理模块**
   - 耗材列表展示
   - 添加/编辑/删除功能
   - 搜索和筛选
   - 库存状态管理

### 🚧 开发中功能
- 员工支出管理
- 客户结算管理
- 平方结算管理
- 付款管理
- 财务统计报表

## 🚀 快速启动

### 📋 启动脚本说明（已优化）

| 脚本名称 | 用途 | 推荐度 | 说明 |
|---------|------|--------|------|
| `简单启动.bat` | 智能启动器 | ⭐⭐⭐⭐⭐ | **推荐使用**，自动选择最佳启动方式 |
| `启动系统.bat` | HTML版本 | ⭐⭐⭐⭐ | 纯HTML版本，无需Node.js |
| `测试启动.bat` | 环境检测 | ⭐⭐⭐ | 检测系统环境并启动 |
| `start.bat` | 完整版本 | ⭐⭐ | 需要Node.js，功能最全 |

### 🎯 推荐启动方式

**最简单**：双击 `简单启动.bat` → 选择 "1"

**备选方案**：直接双击 `erp-system.html`

### 🔧 启动脚本优化内容

✅ **已解决的问题**：
- 修复了编码显示问题（中文乱码）
- 解决了路径解析错误
- 添加了详细的错误处理
- 提供了清晰的状态反馈
- 支持智能环境检测

✅ **优化内容**：
- 使用正确的中文编码 (chcp 936)
- 添加完整路径处理 (%~dp0)
- 隐藏不必要的错误输出 (>nul 2>&1)
- 提供详细的执行步骤
- 智能的环境检测和错误处理

### 📋 手动启动步骤

#### 1. 启动后端服务
```bash
cd backend
node src/app.js
```
后端服务将在 http://localhost:3001 启动

#### 2. 测试系统
打开浏览器访问：`test-frontend.html` 文件进行API测试

#### 3. 启动完整前端（可选）
```bash
cd frontend
npm install  # 首次运行需要安装依赖
npm run dev
```
前端应用将在 http://localhost:5173 启动

### 🔧 系统测试
1. **后端API测试**: 打开 `test-frontend.html` 文件
2. **健康检查**: 访问 http://localhost:3001/api/health
3. **耗材API**: 访问 http://localhost:3001/api/materials

### 🔍 故障排除

| 问题 | 解决方案 |
|------|----------|
| 启动脚本显示乱码 | 使用优化后的启动脚本 |
| 路径错误 | 确保在项目根目录运行 |
| 文件找不到 | 检查文件是否存在 |
| Node.js错误 | 使用HTML版本或安装Node.js |

**推荐解决步骤**：
1. **首选**：使用 `简单启动.bat`
2. **备选**：使用 `测试启动.bat` 检测环境
3. **最简**：直接双击 `erp-system.html`

### 生产环境部署
```bash
# 构建前端
cd frontend && npm run build

# 启动生产服务
cd backend && npm start
```

## 特色功能

1. **响应式设计** - 支持桌面和移动端访问
2. **实时数据** - 数据实时更新和同步
3. **权限管理** - 基于角色的访问控制
4. **数据导出** - 支持Excel、PDF格式导出
5. **图表分析** - 丰富的数据可视化图表
6. **操作日志** - 完整的操作记录追踪

## 系统优势

- **简单易用** - 直观的用户界面，操作简单
- **功能完整** - 覆盖企业财务管理全流程
- **数据安全** - 本地数据库，数据安全可控
- **扩展性强** - 模块化设计，易于扩展新功能
- **部署简单** - 单机部署，无需复杂配置
