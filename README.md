# 白墨打印统计系统

一个专为白墨打印业务设计的统计管理系统，提供订单管理、客户管理、图案数据库、财务分析等核心功能。

## 功能特性

### 📊 仪表板
- 实时业务数据概览（今日/本月订单和收入）
- 快捷操作入口
- 最近订单展示
- 关键指标统计

### 📋 订单管理
- 新建和编辑打印订单
- 自动计算清洗出品和客户方数量
- 价格自动计算（基于客户单价）
- 付款状态跟踪
- 订单搜索和筛选（按客户、色号、付款状态）

### 🤝 客户管理
- 客户基础信息管理
- 客户单价设置
- 微信联系方式管理
- 联系时间记录

### 🎨 图案数据库
- 图案基础信息管理
- 色号分类（单色、双色、三色）
- 自动计算清洗出品和客户方数量
- 图案参数标准化

### 💰 财务管理
- 收入统计分析（今日/本月/本年）
- 财务数据可视化
- 客户充值管理（开发中）

### 📦 耗材管理
- 打印耗材库存管理
- 分类管理（打印纸张、墨水耗材、设备配件、清洗用品）
- 库存状态监控
- 供应商信息管理

### ⚙️ 基础设置
- 数据导入导出功能
- 系统信息查看
- 打印参数设置
- 数据统计概览

## 核心计算公式

### 打印计算
- **添加出血** = 实际高度 + 20mm (自动计算)
- **每平方数量** = (1600mm ÷ 添加出血) × 行数量 (取整数，直接舍去小数)

### 价格计算
- **按个数计费**：个数单价 = 客户单价 ÷ 每平方数量，总价 = 个数单价 × 数量(个)
- **按平方计费**：平方单价 = 16元/平方，总价 = 16 × 面积(平方)

### 技术架构
- **纯前端实现**：基于HTML、CSS、JavaScript
- **响应式设计**：适配各种屏幕尺寸
- **本地存储**：数据保存在浏览器本地 + 文件备份
- **实时计算**：自动计算打印参数和价格

## 数据结构

### 订单数据
```javascript
{
  id: 1,
  customerName: '张三',
  patternName: '标准花纹A',
  colorType: '单色',
  rowCount: 10,
  actualHeight: 50.0,
  bleedHeight: 70.0,
  customerSquare: 228,
  billingType: 'pieces',
  piecesCount: 100,
  squareCount: 0,
  unitPrice: 10.96,
  totalPrice: 1096.0,
  createTime: '2025-01-28 10:30'
}
```

### 客户数据
```javascript
{
  id: 1,
  name: '张三',
  unitPrice: 2.5,
  wechat: 'zhangsan123',
  wechatNote: '老客户',
  contactTime: '2025-01-28T10:00'
}
```

### 图案数据
```javascript
{
  id: 1,
  name: '标准花纹A',
  colorType: '单色',
  rowCount: 10,
  actualHeight: 50.0,
  bleedHeight: 70.0,
  customerSquare: 228
}
```

### 耗材数据
```javascript
{
  id: 1,
  name: '白墨打印纸',
  category: '打印纸张',
  unit: '张',
  price: 0.5,
  stock: 5000,
  supplier: '纸张供应商A',
  status: '正常'
}
```

## 页面结构

### 1. 主导航
- 📊 仪表板
- 📋 订单管理
- 🤝 客户管理
- 🎨 图案数据库
- 💰 财务管理
- 📦 耗材管理
- ⚙️ 基础设置

### 2. 仪表板
- 今日/本月订单和收入统计
- 快捷操作入口
- 最近订单展示
- 关键指标卡片

### 3. 各功能模块
每个模块包含：
- 数据列表页 (支持搜索、筛选)
- 新增/编辑模态框
- 删除确认功能
- 实时数据更新

## 业务流程

### 订单处理流程
1. **客户管理**：在客户管理中添加客户信息和单价
2. **图案库建设**：在图案数据库中添加图案参数（实际高度、行数量等）
3. **订单创建**：在订单管理中新建订单
4. **选择客户**：从下拉列表选择客户
5. **选择图案**：从下拉列表选择图案，系统自动填充所有参数
6. **选择计费方式**：单选按个数计费或按平方计费
7. **输入数量**：根据计费方式输入对应数量
8. **自动计算**：系统自动计算单价和总价格
9. **订单保存**：确认信息后保存订单

### 财务分析流程
1. **数据统计**：系统自动统计各时间段收入
2. **财务查看**：在财务管理页面查看收入分析
3. **指标监控**：在仪表板查看关键指标
4. **数据备份**：定期导出数据进行备份

## 使用方法

### 快速开始
1. 直接在浏览器中打开 `erp-system.html` 文件
2. 系统会自动加载示例数据
3. 可以添加、编辑、删除各类业务数据
4. 数据会自动保存到浏览器本地存储
5. 在基础设置中可以导出和导入数据

### 数据管理
- **数据存储**：使用浏览器localStorage进行本地存储
- **实时保存**：数据实时保存，无需手动保存
- **数据导出**：支持数据导出为JSON文件
- **数据备份**：在"基础设置"页面可以导出数据
- **数据恢复**：支持从备份文件恢复数据

## 当前完成状态

### ✅ 已完成功能
1. **系统架构**
   - 纯前端HTML/CSS/JavaScript实现
   - 响应式设计，适配各种设备
   - 现代化用户界面

2. **核心功能模块**
   - 📊 仪表板：数据概览和快捷操作
   - 📋 订单管理：完整的订单CRUD功能
   - 🤝 客户管理：客户信息和单价管理
   - 🎨 图案数据库：图案参数管理
   - 💰 财务管理：收入统计和分析
   - 📦 耗材管理：库存和供应商管理
   - ⚙️ 基础设置：数据导入导出

3. **计算功能**
   - 自动计算清洗出品和客户方数量
   - 基于客户单价的价格自动计算
   - 实时财务统计和分析

4. **数据功能**
   - 本地数据存储
   - 数据导入导出
   - 搜索和筛选功能
   - 实时数据更新

### 🚧 开发中功能
- 客户充值管理
- 更多打印参数设置
- 高级财务报表
- 数据可视化图表

## 🚀 快速启动

### 📋 启动方式

| 启动方式 | 推荐度 | 说明 |
|---------|--------|------|
| 直接打开 `erp-system.html` | ⭐⭐⭐⭐⭐ | **推荐使用**，双击即可使用 |

### 🎯 推荐启动方式

**最简单**：直接双击 `erp-system.html` 文件

### 📋 使用步骤

1. **打开系统**：双击 `erp-system.html` 文件
2. **查看仪表板**：系统自动显示业务概览
3. **管理客户**：在客户管理中添加客户信息
4. **创建订单**：在订单管理中新建打印订单
5. **查看财务**：在财务管理中查看收入统计
6. **备份数据**：在基础设置中导出数据备份

### 🔧 系统要求

- **浏览器**：Chrome、Firefox、Safari、Edge等现代浏览器
- **JavaScript**：需要启用JavaScript支持
- **本地存储**：需要支持localStorage
- **屏幕分辨率**：建议1024x768以上

### 🔍 故障排除

| 问题 | 解决方案 |
|------|----------|
| 页面无法打开 | 检查浏览器是否支持JavaScript |
| 数据无法保存 | 检查浏览器是否启用localStorage |
| 计算结果错误 | 检查输入数据是否正确 |
| 数据丢失 | 从备份文件恢复数据 |

## 特色功能

1. **简化订单** - 只需选择客户、图案、计费方式和数量即可创建订单
2. **智能计算** - 自动计算添加出血和每平方数量，支持双重计费模式
3. **精确取整** - 每平方数量直接舍去小数部分，确保计算准确
4. **数据安全** - 本地存储 + 文件备份双重保障
5. **操作简单** - 直观的用户界面，易于上手
6. **实时统计** - 自动统计财务数据和业务指标
7. **响应式设计** - 支持桌面和移动端访问

## 系统优势

- **专业性强** - 专为白墨打印业务设计
- **计算准确** - 内置专业打印计算公式
- **操作简单** - 无需安装，双击即用
- **数据安全** - 本地存储，数据可控
- **功能完整** - 覆盖订单、客户、财务全流程
- **维护简单** - 纯前端实现，无需服务器

## 注意事项

1. **数据存储**：数据仅存储在当前浏览器中
2. **数据备份**：建议定期使用导出功能备份数据
3. **浏览器清理**：清除浏览器数据会导致系统数据丢失
4. **单用户使用**：系统为单用户版本，不支持多用户同时使用
5. **价格计算**：价格计算基于客户设置的单价

## 更新日志

### v1.2.0 (2025-01-28)
- ✅ 优化订单创建流程：只需选择客户、图案、计费方式和数量
- ✅ 更新计算公式：添加出血 = 实际高度 + 20mm (自动计算)
- ✅ 更新计算公式：每平方数量 = (1600mm ÷ 添加出血) × 行数量 (取整数)
- ✅ 支持双重计费模式：按个数计费和按平方计费
- ✅ 改进数据验证和错误提示
- ✅ 更新所有相关数据结构和显示

### v1.0.0 (2025-01-28)
- ✅ 初始版本发布
- ✅ 实现白墨打印业务核心功能
- ✅ 支持订单、客户、图案、耗材管理
- ✅ 提供财务分析和数据导入导出功能
- ✅ 自动计算打印参数和价格
- ✅ 响应式设计，支持多设备访问
