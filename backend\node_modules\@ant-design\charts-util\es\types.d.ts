export interface ContainerConfig {
    /**
     * @title 图表样式
     * @description 配置容器样式
     * @title.en_US Chart containerStyle
     * @description.en_US Configure chart container styles
     */
    containerStyle?: React.CSSProperties;
    /**
     * @title 容器自定义属性
     * @description 配置容器自定义属性
     * @title.en_US Chart containerAttr
     * @description.en_US Configure chart container attributes
     */
    containerAttributes?: Record<string, any>;
    /**
     * @title 容器class
     * @description 类名添加
     * @title.en_US Container class name
     * @description.en_US Class name addition
     */
    className?: string;
    /**
     * @title 加载状态
     * @description 是否加载中
     * @default false
     * @title.en_US Loading status
     * @description.en_US Is it loading
     * @default.en_US false
     */
    loading?: boolean;
    /**
     * @title 加载模板
     * @description 加载模板
     * @title.en_US Load template
     * @description.en_US Load template
     */
    loadingTemplate?: React.ReactElement;
    /**
     * @title 出错模板
     * @description 出错时占位模板
     * @title.en_US error template
     * @description.en_US Error placeholder template
     */
    errorTemplate?: (e: Error) => React.ReactNode;
}
