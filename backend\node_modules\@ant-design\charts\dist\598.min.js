/*! For license information please see 598.min.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Charts=e():t.Charts=e()}(self,(()=>(()=>{var t={8:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>o});var n=r(1869);function o(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(0,n.isAnyArray)(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");if(void 0!==r.output){if(!(0,n.isAnyArray)(r.output))throw new TypeError("output option must be an array if specified");e=r.output}else e=new Array(t.length);var o=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(0,n.isAnyArray)(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");var r=e.fromIndex,o=void 0===r?0:r,i=e.toIndex,a=void 0===i?t.length:i;if(o<0||o>=t.length||!Number.isInteger(o))throw new Error("fromIndex must be a positive integer smaller than length");if(a<=o||a>t.length||!Number.isInteger(a))throw new Error("toIndex must be an integer greater than fromIndex and at most equal to length");for(var s=t[o],u=o+1;u<a;u++)t[u]<s&&(s=t[u]);return s}(t),i=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(0,n.isAnyArray)(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");var r=e.fromIndex,o=void 0===r?0:r,i=e.toIndex,a=void 0===i?t.length:i;if(o<0||o>=t.length||!Number.isInteger(o))throw new Error("fromIndex must be a positive integer smaller than length");if(a<=o||a>t.length||!Number.isInteger(a))throw new Error("toIndex must be an integer greater than fromIndex and at most equal to length");for(var s=t[o],u=o+1;u<a;u++)t[u]>s&&(s=t[u]);return s}(t);if(o===i)throw new RangeError("minimum and maximum input values are equal. Cannot rescale a constant array");var a=r.min,s=void 0===a?r.autoMinMax?o:0:a,u=r.max,c=void 0===u?r.autoMinMax?i:1:u;if(s>=c)throw new RangeError("min option must be smaller than max option");for(var h=(c-s)/(i-o),f=0;f<t.length;f++)e[f]=(t[f]-o)*h+s;return e}},35:(t,e,r)=>{var n=r(6698).Uint8Array;t.exports=n},56:(t,e,r)=>{var n=r(5405);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},68:(t,e,r)=>{var n=r(957),o=r(8084),i=r(1263),a=r(4265),s=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)n(e,i(t)),t=o(t);return e}:a;t.exports=s},85:(t,e,r)=>{var n=r(4680);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},95:(t,e,r)=>{"use strict";var n=r(6036),o=r(1173),i=r(8478),a=r(5248),s=r(1882).normalizeRanks,u=r(9671),c=r(1882).removeEmptyRanks,h=r(1302),f=r(1152),d=r(5025),l=r(2194),v=r(6889),g=r(1882),p=r(4206).Graph;t.exports=function(t,e){var r=e&&e.debugTiming?g.time:g.notime;r("layout",(function(){var e=r("  buildLayoutGraph",(function(){return function(t){var e=new p({multigraph:!0,compound:!0}),r=_(t.graph());return e.setGraph(n.merge({},m,N(r,y),n.pick(r,w))),n.forEach(t.nodes(),(function(r){var o=_(t.node(r));e.setNode(r,n.defaults(N(o,x),b)),e.setParent(r,t.parent(r))})),n.forEach(t.edges(),(function(r){var o=_(t.edge(r));e.setEdge(r,n.merge({},k,N(o,E),n.pick(o,M)))})),e}(t)}));r("  runLayout",(function(){!function(t,e){e("    makeSpaceForEdgeLabels",(function(){!function(t){var e=t.graph();e.ranksep/=2,n.forEach(t.edges(),(function(r){var n=t.edge(r);n.minlen*=2,"c"!==n.labelpos.toLowerCase()&&("TB"===e.rankdir||"BT"===e.rankdir?n.width+=n.labeloffset:n.height+=n.labeloffset)}))}(t)})),e("    removeSelfEdges",(function(){!function(t){n.forEach(t.edges(),(function(e){if(e.v===e.w){var r=t.node(e.v);r.selfEdges||(r.selfEdges=[]),r.selfEdges.push({e,label:t.edge(e)}),t.removeEdge(e)}}))}(t)})),e("    acyclic",(function(){o.run(t)})),e("    nestingGraph.run",(function(){h.run(t)})),e("    rank",(function(){a(g.asNonCompoundGraph(t))})),e("    injectEdgeLabelProxies",(function(){!function(t){n.forEach(t.edges(),(function(e){var r=t.edge(e);if(r.width&&r.height){var n=t.node(e.v),o={rank:(t.node(e.w).rank-n.rank)/2+n.rank,e};g.addDummyNode(t,"edge-proxy",o,"_ep")}}))}(t)})),e("    removeEmptyRanks",(function(){c(t)})),e("    nestingGraph.cleanup",(function(){h.cleanup(t)})),e("    normalizeRanks",(function(){s(t)})),e("    assignRankMinMax",(function(){!function(t){var e=0;n.forEach(t.nodes(),(function(r){var o=t.node(r);o.borderTop&&(o.minRank=t.node(o.borderTop).rank,o.maxRank=t.node(o.borderBottom).rank,e=n.max(e,o.maxRank))})),t.graph().maxRank=e}(t)})),e("    removeEdgeLabelProxies",(function(){!function(t){n.forEach(t.nodes(),(function(e){var r=t.node(e);"edge-proxy"===r.dummy&&(t.edge(r.e).labelRank=r.rank,t.removeNode(e))}))}(t)})),e("    normalize.run",(function(){i.run(t)})),e("    parentDummyChains",(function(){u(t)})),e("    addBorderSegments",(function(){f(t)})),e("    order",(function(){l(t)})),e("    insertSelfEdges",(function(){!function(t){var e=g.buildLayerMatrix(t);n.forEach(e,(function(e){var r=0;n.forEach(e,(function(e,o){var i=t.node(e);i.order=o+r,n.forEach(i.selfEdges,(function(e){g.addDummyNode(t,"selfedge",{width:e.label.width,height:e.label.height,rank:i.rank,order:o+ ++r,e:e.e,label:e.label},"_se")})),delete i.selfEdges}))}))}(t)})),e("    adjustCoordinateSystem",(function(){d.adjust(t)})),e("    position",(function(){v(t)})),e("    positionSelfEdges",(function(){!function(t){n.forEach(t.nodes(),(function(e){var r=t.node(e);if("selfedge"===r.dummy){var n=t.node(r.e.v),o=n.x+n.width/2,i=n.y,a=r.x-o,s=n.height/2;t.setEdge(r.e,r.label),t.removeNode(e),r.label.points=[{x:o+2*a/3,y:i-s},{x:o+5*a/6,y:i-s},{x:o+a,y:i},{x:o+5*a/6,y:i+s},{x:o+2*a/3,y:i+s}],r.label.x=r.x,r.label.y=r.y}}))}(t)})),e("    removeBorderNodes",(function(){!function(t){n.forEach(t.nodes(),(function(e){if(t.children(e).length){var r=t.node(e),o=t.node(r.borderTop),i=t.node(r.borderBottom),a=t.node(n.last(r.borderLeft)),s=t.node(n.last(r.borderRight));r.width=Math.abs(s.x-a.x),r.height=Math.abs(i.y-o.y),r.x=a.x+r.width/2,r.y=o.y+r.height/2}})),n.forEach(t.nodes(),(function(e){"border"===t.node(e).dummy&&t.removeNode(e)}))}(t)})),e("    normalize.undo",(function(){i.undo(t)})),e("    fixupEdgeLabelCoords",(function(){!function(t){n.forEach(t.edges(),(function(e){var r=t.edge(e);if(n.has(r,"x"))switch("l"!==r.labelpos&&"r"!==r.labelpos||(r.width-=r.labeloffset),r.labelpos){case"l":r.x-=r.width/2+r.labeloffset;break;case"r":r.x+=r.width/2+r.labeloffset}}))}(t)})),e("    undoCoordinateSystem",(function(){d.undo(t)})),e("    translateGraph",(function(){!function(t){var e=Number.POSITIVE_INFINITY,r=0,o=Number.POSITIVE_INFINITY,i=0,a=t.graph(),s=a.marginx||0,u=a.marginy||0;function c(t){var n=t.x,a=t.y,s=t.width,u=t.height;e=Math.min(e,n-s/2),r=Math.max(r,n+s/2),o=Math.min(o,a-u/2),i=Math.max(i,a+u/2)}n.forEach(t.nodes(),(function(e){c(t.node(e))})),n.forEach(t.edges(),(function(e){var r=t.edge(e);n.has(r,"x")&&c(r)})),e-=s,o-=u,n.forEach(t.nodes(),(function(r){var n=t.node(r);n.x-=e,n.y-=o})),n.forEach(t.edges(),(function(r){var i=t.edge(r);n.forEach(i.points,(function(t){t.x-=e,t.y-=o})),n.has(i,"x")&&(i.x-=e),n.has(i,"y")&&(i.y-=o)})),a.width=r-e+s,a.height=i-o+u}(t)})),e("    assignNodeIntersects",(function(){!function(t){n.forEach(t.edges(),(function(e){var r,n,o=t.edge(e),i=t.node(e.v),a=t.node(e.w);o.points?(r=o.points[0],n=o.points[o.points.length-1]):(o.points=[],r=a,n=i),o.points.unshift(g.intersectRect(i,r)),o.points.push(g.intersectRect(a,n))}))}(t)})),e("    reversePoints",(function(){!function(t){n.forEach(t.edges(),(function(e){var r=t.edge(e);r.reversed&&r.points.reverse()}))}(t)})),e("    acyclic.undo",(function(){o.undo(t)}))}(e,r)})),r("  updateInputGraph",(function(){!function(t,e){n.forEach(t.nodes(),(function(r){var n=t.node(r),o=e.node(r);n&&(n.x=o.x,n.y=o.y,e.children(r).length&&(n.width=o.width,n.height=o.height))})),n.forEach(t.edges(),(function(r){var o=t.edge(r),i=e.edge(r);o.points=i.points,n.has(i,"x")&&(o.x=i.x,o.y=i.y)})),t.graph().width=e.graph().width,t.graph().height=e.graph().height}(t,e)}))}))};var y=["nodesep","edgesep","ranksep","marginx","marginy"],m={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},w=["acyclicer","ranker","rankdir","align"],x=["width","height"],b={width:0,height:0},E=["minlen","weight","width","height","labeloffset"],k={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},M=["labelpos"];function N(t,e){return n.mapValues(n.pick(t,e),Number)}function _(t){var e={};return n.forEach(t,(function(t,r){e[r.toLowerCase()]=t})),e}},117:(t,e,r)=>{var n=r(5405);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},199:t=>{function e(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(r){}return(t.exports=e=function(){return!!r},t.exports.__esModule=!0,t.exports.default=t.exports)()}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},223:(t,e,r)=>{var n=r(2613),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},270:(t,e,r)=>{var n=r(117),o=r(1413),i=r(6876),a=r(3448),s=r(56);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,t.exports=u},272:(t,e,r)=>{var n=r(7671),o=r(8084),i=r(1110);t.exports=function(t){return"function"!=typeof t.constructor||i(t)?{}:n(o(t))}},302:(t,e,r)=>{var n=r(2397),o=r(6354),i=r(9797),a=r(2707),s=i((function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])}));t.exports=s},319:(t,e,r)=>{var n=r(9780),o=r(519);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},334:(t,e,r)=>{var n=r(9942),o=r(4198)(n);t.exports=o},349:(t,e,r)=>{var n=r(2323),o=r(5434),i=r(477),a=r(8962);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},381:(t,e,r)=>{var n=r(2323);t.exports=function(t,e){return n(e,(function(e){return t[e]}))}},383:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},411:(t,e,r)=>{var n=r(7953);t.exports=function(t,e,r){return t.set(n(t,e),r),r},t.exports.__esModule=!0,t.exports.default=t.exports},459:(t,e,r)=>{var n=r(6036),o=r(9509),i=r(2120),a=r(3904);t.exports=function t(e,r,s,u){var c=e.children(r),h=e.node(r),f=h?h.borderLeft:void 0,d=h?h.borderRight:void 0,l={};f&&(c=n.filter(c,(function(t){return t!==f&&t!==d})));var v=o(e,c);n.forEach(v,(function(r){if(e.children(r.v).length){var o=t(e,r.v,s,u);l[r.v]=o,n.has(o,"barycenter")&&(i=r,a=o,n.isUndefined(i.barycenter)?(i.barycenter=a.barycenter,i.weight=a.weight):(i.barycenter=(i.barycenter*i.weight+a.barycenter*a.weight)/(i.weight+a.weight),i.weight+=a.weight))}var i,a}));var g=i(v,s);!function(t,e){n.forEach(t,(function(t){t.vs=n.flatten(t.vs.map((function(t){return e[t]?e[t].vs:t})),!0)}))}(g,l);var p=a(g,u);if(f&&(p.vs=n.flatten([f,p.vs,d],!0),e.predecessors(f).length)){var y=e.node(e.predecessors(f)[0]),m=e.node(e.predecessors(d)[0]);n.has(p,"barycenter")||(p.barycenter=0,p.weight=0),p.barycenter=(p.barycenter*p.weight+y.order+m.order)/(p.weight+2),p.weight+=2}return p}},477:(t,e,r)=>{var n=r(334),o=r(1693);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,(function(t,n,o){i[++r]=e(t,n,o)})),i}},486:t=>{t.exports=function(t){return this.__data__.get(t)}},498:(t,e,r)=>{var n=r(5675),o=r(6558),i=r(8873),a=r(4604),s=r(3818),u=r(4359),c=r(7950),h="[object Map]",f="[object Promise]",d="[object Set]",l="[object WeakMap]",v="[object DataView]",g=c(n),p=c(o),y=c(i),m=c(a),w=c(s),x=u;(n&&x(new n(new ArrayBuffer(1)))!=v||o&&x(new o)!=h||i&&x(i.resolve())!=f||a&&x(new a)!=d||s&&x(new s)!=l)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?c(r):"";if(n)switch(n){case g:return v;case p:return h;case y:return f;case m:return d;case w:return l}return e}),t.exports=x},504:t=>{var e=/\w*$/;t.exports=function(t){var r=new t.constructor(t.source,e.exec(t));return r.lastIndex=t.lastIndex,r}},519:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},546:(t,e,r)=>{var n=r(6036),o=r(1882),i=r(4206).Graph;t.exports={debugOrdering:function(t){var e=o.buildLayerMatrix(t),r=new i({compound:!0,multigraph:!0}).setGraph({});return n.forEach(t.nodes(),(function(e){r.setNode(e,{label:e}),r.setParent(e,"layer"+t.node(e).rank)})),n.forEach(t.edges(),(function(t){r.setEdge(t.v,t.w,{},t.name)})),n.forEach(e,(function(t,e){var o="layer"+e;r.setNode(o,{rank:"same"}),n.reduce(t,(function(t,e){return r.setEdge(t,e,{style:"invis"}),e}))})),r}}},558:(t,e,r)=>{t.exports={components:r(9360),dijkstra:r(2886),dijkstraAll:r(7880),findCycles:r(9327),floydWarshall:r(1993),isAcyclic:r(4185),postorder:r(3328),preorder:r(4071),prim:r(4248),tarjan:r(7968),topsort:r(4073)}},591:(t,e,r)=>{var n=r(5711);t.exports={Graph:n.Graph,json:r(4545),alg:r(558),version:n.version}},756:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},911:(t,e,r)=>{var n=r(9776),o=r(4542),i=r(2920),a=r(2705),s=r(7560),u=r(4620),c=r(6095);t.exports=function t(e,r,h,f,d){e!==r&&i(r,(function(i,u){if(d||(d=new n),s(i))a(e,r,u,h,t,f,d);else{var l=f?f(c(e,u),i,u+"",e,r,d):void 0;void 0===l&&(l=i),o(e,u,l)}}),u)}},943:(t,e,r)=>{var n=r(4359),o=r(6223);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},957:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},981:(t,e,r)=>{var n=r(4158),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0||(r==e.length-1?e.pop():o.call(e,r,1),--this.size,0))}},1001:(t,e,r)=>{"use strict";var n=r(6036),o=r(4668),i=r(4317).slack,a=r(4317).longestPath,s=r(4206).alg.preorder,u=r(4206).alg.postorder,c=r(1882).simplify;function h(t){t=c(t),a(t);var e,r=o(t);for(l(r),f(r,t);e=g(r);)y(r,t,e,p(r,t,e))}function f(t,e){var r=u(t,t.nodes());r=r.slice(0,r.length-1),n.forEach(r,(function(r){!function(t,e,r){var n=t.node(r).parent;t.edge(r,n).cutvalue=d(t,e,r)}(t,e,r)}))}function d(t,e,r){var o=t.node(r).parent,i=!0,a=e.edge(r,o),s=0;return a||(i=!1,a=e.edge(o,r)),s=a.weight,n.forEach(e.nodeEdges(r),(function(n){var a,u,c=n.v===r,h=c?n.w:n.v;if(h!==o){var f=c===i,d=e.edge(n).weight;if(s+=f?d:-d,a=r,u=h,t.hasEdge(a,u)){var l=t.edge(r,h).cutvalue;s+=f?-l:l}}})),s}function l(t,e){arguments.length<2&&(e=t.nodes()[0]),v(t,{},1,e)}function v(t,e,r,o,i){var a=r,s=t.node(o);return e[o]=!0,n.forEach(t.neighbors(o),(function(i){n.has(e,i)||(r=v(t,e,r,i,o))})),s.low=a,s.lim=r++,i?s.parent=i:delete s.parent,r}function g(t){return n.find(t.edges(),(function(e){return t.edge(e).cutvalue<0}))}function p(t,e,r){var o=r.v,a=r.w;e.hasEdge(o,a)||(o=r.w,a=r.v);var s=t.node(o),u=t.node(a),c=s,h=!1;s.lim>u.lim&&(c=u,h=!0);var f=n.filter(e.edges(),(function(e){return h===m(0,t.node(e.v),c)&&h!==m(0,t.node(e.w),c)}));return n.minBy(f,(function(t){return i(e,t)}))}function y(t,e,r,o){var i=r.v,a=r.w;t.removeEdge(i,a),t.setEdge(o.v,o.w,{}),l(t),f(t,e),function(t,e){var r=n.find(t.nodes(),(function(t){return!e.node(t).parent})),o=s(t,r);o=o.slice(1),n.forEach(o,(function(r){var n=t.node(r).parent,o=e.edge(r,n),i=!1;o||(o=e.edge(n,r),i=!0),e.node(r).rank=e.node(n).rank+(i?o.minlen:-o.minlen)}))}(t,e)}function m(t,e,r){return r.low<=e.lim&&e.lim<=r.lim}t.exports=h,h.initLowLimValues=l,h.initCutValues=f,h.calcCutValue=d,h.leaveEdge=g,h.enterEdge=p,h.exchangeEdges=y},1002:t=>{t.exports=function(t){return this.__data__.has(t)}},1011:t=>{t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports},1079:t=>{t.exports=function(t){return function(){return t}}},1110:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},1126:(t,e,r)=>{var n=r(35);t.exports=function(t){var e=new t.constructor(t.byteLength);return new n(e).set(new n(t)),e}},1152:(t,e,r)=>{var n=r(6036),o=r(1882);function i(t,e,r,n,i,a){var s={width:0,height:0,rank:a,borderType:e},u=i[e][a-1],c=o.addDummyNode(t,"border",s,r);i[e][a]=c,t.setParent(c,n),u&&t.setEdge(u,c,{weight:1})}t.exports=function(t){n.forEach(t.children(),(function e(r){var o=t.children(r),a=t.node(r);if(o.length&&n.forEach(o,e),n.has(a,"minRank")){a.borderLeft=[],a.borderRight=[];for(var s=a.minRank,u=a.maxRank+1;s<u;++s)i(t,"borderLeft","_bl",r,a,s),i(t,"borderRight","_br",r,a,s)}}))}},1167:t=>{t.exports=function(t,e,r){for(var n=-1,o=t.length,i=e.length,a={};++n<o;){var s=n<i?e[n]:void 0;r(a,t[n],s)}return a}},1173:(t,e,r)=>{"use strict";var n=r(6036),o=r(1318);t.exports={run:function(t){var e="greedy"===t.graph().acyclicer?o(t,function(t){return function(e){return t.edge(e).weight}}(t)):function(t){var e=[],r={},o={};return n.forEach(t.nodes(),(function i(a){n.has(o,a)||(o[a]=!0,r[a]=!0,n.forEach(t.outEdges(a),(function(t){n.has(r,t.w)?e.push(t):i(t.w)})),delete r[a])})),e}(t);n.forEach(e,(function(e){var r=t.edge(e);t.removeEdge(e),r.forwardName=e.name,r.reversed=!0,t.setEdge(e.w,e.v,r,n.uniqueId("rev"))}))},undo:function(t){n.forEach(t.edges(),(function(e){var r=t.edge(e);if(r.reversed){t.removeEdge(e);var n=r.forwardName;delete r.reversed,delete r.forwardName,t.setEdge(e.w,e.v,r,n)}}))}}},1251:(t,e,r)=>{var n=r(9791).default,o=r(7404);t.exports=function(t,e){if(e&&("object"==n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return o(t)},t.exports.__esModule=!0,t.exports.default=t.exports},1263:(t,e,r)=>{var n=r(3651),o=r(4265),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,s=a?function(t){return null==t?[]:(t=Object(t),n(a(t),(function(e){return i.call(t,e)})))}:o;t.exports=s},1266:(t,e,r)=>{var n=r(1693),o=r(6223);t.exports=function(t){return o(t)&&n(t)}},1302:(t,e,r)=>{var n=r(6036),o=r(1882);function i(t,e,r,a,s,u,c){var h=t.children(c);if(h.length){var f=o.addBorderNode(t,"_bt"),d=o.addBorderNode(t,"_bb"),l=t.node(c);t.setParent(f,c),l.borderTop=f,t.setParent(d,c),l.borderBottom=d,n.forEach(h,(function(n){i(t,e,r,a,s,u,n);var o=t.node(n),h=o.borderTop?o.borderTop:n,l=o.borderBottom?o.borderBottom:n,v=o.borderTop?a:2*a,g=h!==l?1:s-u[c]+1;t.setEdge(f,h,{weight:v,minlen:g,nestingEdge:!0}),t.setEdge(l,d,{weight:v,minlen:g,nestingEdge:!0})})),t.parent(c)||t.setEdge(e,f,{weight:0,minlen:s+u[c]})}else c!==e&&t.setEdge(e,c,{weight:0,minlen:r})}t.exports={run:function(t){var e=o.addDummyNode(t,"root",{},"_root"),r=function(t){var e={};function r(o,i){var a=t.children(o);a&&a.length&&n.forEach(a,(function(t){r(t,i+1)})),e[o]=i}return n.forEach(t.children(),(function(t){r(t,1)})),e}(t),a=n.max(n.values(r))-1,s=2*a+1;t.graph().nestingRoot=e,n.forEach(t.edges(),(function(e){t.edge(e).minlen*=s}));var u=function(t){return n.reduce(t.edges(),(function(e,r){return e+t.edge(r).weight}),0)}(t)+1;n.forEach(t.children(),(function(n){i(t,e,s,u,a,r,n)})),t.graph().nodeRankFactor=s},cleanup:function(t){var e=t.graph();t.removeNode(e.nestingRoot),delete e.nestingRoot,n.forEach(t.edges(),(function(e){t.edge(e).nestingEdge&&t.removeEdge(e)}))}}},1318:(t,e,r)=>{var n=r(6036),o=r(4206).Graph,i=r(1656);t.exports=function(t,e){if(t.nodeCount()<=1)return[];var r=function(t,e){var r=new o,a=0,s=0;n.forEach(t.nodes(),(function(t){r.setNode(t,{v:t,in:0,out:0})})),n.forEach(t.edges(),(function(t){var n=r.edge(t.v,t.w)||0,o=e(t),i=n+o;r.setEdge(t.v,t.w,i),s=Math.max(s,r.node(t.v).out+=o),a=Math.max(a,r.node(t.w).in+=o)}));var c=n.range(s+a+3).map((function(){return new i})),h=a+1;return n.forEach(r.nodes(),(function(t){u(c,h,r.node(t))})),{graph:r,buckets:c,zeroIdx:h}}(t,e||a),c=function(t,e,r){for(var n,o=[],i=e[e.length-1],a=e[0];t.nodeCount();){for(;n=a.dequeue();)s(t,e,r,n);for(;n=i.dequeue();)s(t,e,r,n);if(t.nodeCount())for(var u=e.length-2;u>0;--u)if(n=e[u].dequeue()){o=o.concat(s(t,e,r,n,!0));break}}return o}(r.graph,r.buckets,r.zeroIdx);return n.flatten(n.map(c,(function(e){return t.outEdges(e.v,e.w)})),!0)};var a=n.constant(1);function s(t,e,r,o,i){var a=i?[]:void 0;return n.forEach(t.inEdges(o.v),(function(n){var o=t.edge(n),s=t.node(n.v);i&&a.push({v:n.v,w:n.w}),s.out-=o,u(e,r,s)})),n.forEach(t.outEdges(o.v),(function(n){var o=t.edge(n),i=n.w,a=t.node(i);a.in-=o,u(e,r,a)})),t.removeNode(o.v),a}function u(t,e,r){r.out?r.in?t[r.out-r.in+e].enqueue(r):t[t.length-1].enqueue(r):t[0].enqueue(r)}},1326:(t,e,r)=>{var n=r(1758),o=r(7788),i=r(1751),a=r(8812);t.exports=function(t){return i(t)?n(a(t)):o(t)}},1340:(t,e,r)=>{var n=r(4359),o=r(8084),i=r(6223),a=Function.prototype,s=Object.prototype,u=a.toString,c=s.hasOwnProperty,h=u.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=c.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==h}},1362:(t,e,r)=>{var n=r(2681)(r(2150));t.exports=n},1413:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},1417:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},1543:(t,e,r)=>{var n=r(3130),o=r(2823),i=r(1693);t.exports=function(t){return i(t)?n(t):o(t)}},1614:(t,e,r)=>{var n=r(8979),o=r(2432);t.exports=function(t,e){return n(t,e,(function(e,r){return o(t,r)}))}},1643:(t,e,r)=>{var n=r(5543),o=r(1543);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},1656:t=>{function e(){var t={};t._next=t._prev=t,this._sentinel=t}function r(t){t._prev._next=t._next,t._next._prev=t._prev,delete t._next,delete t._prev}function n(t,e){if("_next"!==t&&"_prev"!==t)return e}t.exports=e,e.prototype.dequeue=function(){var t=this._sentinel,e=t._prev;if(e!==t)return r(e),e},e.prototype.enqueue=function(t){var e=this._sentinel;t._prev&&t._next&&r(t),t._next=e._next,e._next._prev=t,e._next=t,t._prev=e},e.prototype.toString=function(){for(var t=[],e=this._sentinel,r=e._prev;r!==e;)t.push(JSON.stringify(r,n)),r=r._prev;return"["+t.join(", ")+"]"}},1693:(t,e,r)=>{var n=r(5179),o=r(9703);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},1710:(t,e,r)=>{t=r.nmd(t);var n=r(9107),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,s=function(){try{return i&&i.require&&i.require("util").types||a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=s},1734:(t,e,r)=>{var n=r(3342),o=r(6757),i=r(4814);t.exports=function(t){return o(t)?i(t):n(t)}},1742:(t,e,r)=>{var n;try{n={clone:r(3722),constant:r(1079),each:r(9054),filter:r(7937),has:r(1763),isArray:r(8962),isEmpty:r(4350),isFunction:r(5179),isUndefined:r(7791),keys:r(1543),map:r(349),reduce:r(6309),size:r(9558),transform:r(7951),union:r(3216),values:r(5181)}}catch(t){}n||(n=window._),t.exports=n},1751:(t,e,r)=>{var n=r(8962),o=r(943),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},1758:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},1763:(t,e,r)=>{var n=r(1891),o=r(3919);t.exports=function(t,e){return null!=t&&o(t,e,n)}},1846:(t,e,r)=>{var n=r(4359),o=r(9703),i=r(6223),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},1863:(t,e,r)=>{var n=r(223),o=r(7560),i=r(943),a=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,u=/^0o[0-7]+$/i,c=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return NaN;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=s.test(t);return r||u.test(t)?c(t.slice(2),r?2:8):a.test(t)?NaN:+t}},1869:(t,e,r)=>{"use strict";r.r(e),r.d(e,{isAnyArray:()=>o});var n=Object.prototype.toString;function o(t){var e=n.call(t);return e.endsWith("Array]")&&!e.includes("Big")}},1882:(t,e,r)=>{"use strict";var n=r(6036),o=r(4206).Graph;function i(t,e,r,o){var i;do{i=n.uniqueId(o)}while(t.hasNode(i));return r.dummy=e,t.setNode(i,r),i}function a(t){return n.max(n.map(t.nodes(),(function(e){var r=t.node(e).rank;if(!n.isUndefined(r))return r})))}t.exports={addDummyNode:i,simplify:function(t){var e=(new o).setGraph(t.graph());return n.forEach(t.nodes(),(function(r){e.setNode(r,t.node(r))})),n.forEach(t.edges(),(function(r){var n=e.edge(r.v,r.w)||{weight:0,minlen:1},o=t.edge(r);e.setEdge(r.v,r.w,{weight:n.weight+o.weight,minlen:Math.max(n.minlen,o.minlen)})})),e},asNonCompoundGraph:function(t){var e=new o({multigraph:t.isMultigraph()}).setGraph(t.graph());return n.forEach(t.nodes(),(function(r){t.children(r).length||e.setNode(r,t.node(r))})),n.forEach(t.edges(),(function(r){e.setEdge(r,t.edge(r))})),e},successorWeights:function(t){var e=n.map(t.nodes(),(function(e){var r={};return n.forEach(t.outEdges(e),(function(e){r[e.w]=(r[e.w]||0)+t.edge(e).weight})),r}));return n.zipObject(t.nodes(),e)},predecessorWeights:function(t){var e=n.map(t.nodes(),(function(e){var r={};return n.forEach(t.inEdges(e),(function(e){r[e.v]=(r[e.v]||0)+t.edge(e).weight})),r}));return n.zipObject(t.nodes(),e)},intersectRect:function(t,e){var r,n,o=t.x,i=t.y,a=e.x-o,s=e.y-i,u=t.width/2,c=t.height/2;if(!a&&!s)throw new Error("Not possible to find intersection inside of the rectangle");return Math.abs(s)*u>Math.abs(a)*c?(s<0&&(c=-c),r=c*a/s,n=c):(a<0&&(u=-u),r=u,n=u*s/a),{x:o+r,y:i+n}},buildLayerMatrix:function(t){var e=n.map(n.range(a(t)+1),(function(){return[]}));return n.forEach(t.nodes(),(function(r){var o=t.node(r),i=o.rank;n.isUndefined(i)||(e[i][o.order]=r)})),e},normalizeRanks:function(t){var e=n.min(n.map(t.nodes(),(function(e){return t.node(e).rank})));n.forEach(t.nodes(),(function(r){var o=t.node(r);n.has(o,"rank")&&(o.rank-=e)}))},removeEmptyRanks:function(t){var e=n.min(n.map(t.nodes(),(function(e){return t.node(e).rank}))),r=[];n.forEach(t.nodes(),(function(n){var o=t.node(n).rank-e;r[o]||(r[o]=[]),r[o].push(n)}));var o=0,i=t.graph().nodeRankFactor;n.forEach(r,(function(e,r){n.isUndefined(e)&&r%i!==0?--o:o&&n.forEach(e,(function(e){t.node(e).rank+=o}))}))},addBorderNode:function(t,e,r,n){var o={width:0,height:0};return arguments.length>=4&&(o.rank=r,o.order=n),i(t,"border",o,e)},maxRank:a,partition:function(t,e){var r={lhs:[],rhs:[]};return n.forEach(t,(function(t){e(t)?r.lhs.push(t):r.rhs.push(t)})),r},time:function(t,e){var r=n.now();try{return e()}finally{console.log(t+" time: "+(n.now()-r)+"ms")}},notime:function(t,e){return e()}}},1888:(t,e,r)=>{var n=r(4666);t.exports=function(t,e){return!(null==t||!t.length)&&n(t,e,0)>-1}},1889:(t,e,r)=>{var n=r(9948),o=r(68);t.exports=function(t,e){return n(t,o(t),e)}},1891:t=>{var e=Object.prototype.hasOwnProperty;t.exports=function(t,r){return null!=t&&e.call(t,r)}},1896:t=>{t.exports=function(t){return function(e){return t(e)}}},1906:t=>{t.exports=function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")},t.exports.__esModule=!0,t.exports.default=t.exports},1993:(t,e,r)=>{var n=r(1742);t.exports=function(t,e,r){return function(t,e,r){var n={},o=t.nodes();return o.forEach((function(t){n[t]={},n[t][t]={distance:0},o.forEach((function(e){t!==e&&(n[t][e]={distance:Number.POSITIVE_INFINITY})})),r(t).forEach((function(r){var o=r.v===t?r.w:r.v,i=e(r);n[t][o]={distance:i,predecessor:t}}))})),o.forEach((function(t){var e=n[t];o.forEach((function(r){var i=n[r];o.forEach((function(r){var n=i[t],o=e[r],a=i[r],s=n.distance+o.distance;s<a.distance&&(a.distance=s,a.predecessor=o.predecessor)}))}))})),n}(t,e||o,r||function(e){return t.outEdges(e)})};var o=n.constant(1)},2027:(t,e,r)=>{var n=r(6853),o=r(6223);t.exports=function t(e,r,i,a,s){return e===r||(null==e||null==r||!o(e)&&!o(r)?e!=e&&r!=r:n(e,r,i,a,t,s))}},2050:(t,e,r)=>{var n=r(6698)["__core-js_shared__"];t.exports=n},2099:(t,e,r)=>{var n=r(6682);t.exports=function(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}},t.exports.__esModule=!0,t.exports.default=t.exports},2120:(t,e,r)=>{"use strict";var n=r(6036);t.exports=function(t,e){var r={};return n.forEach(t,(function(t,e){var o=r[t.v]={indegree:0,in:[],out:[],vs:[t.v],i:e};n.isUndefined(t.barycenter)||(o.barycenter=t.barycenter,o.weight=t.weight)})),n.forEach(e.edges(),(function(t){var e=r[t.v],o=r[t.w];n.isUndefined(e)||n.isUndefined(o)||(o.indegree++,e.out.push(r[t.w]))})),function(t){var e=[];function r(t){return function(e){var r,o,i,a;e.merged||(n.isUndefined(e.barycenter)||n.isUndefined(t.barycenter)||e.barycenter>=t.barycenter)&&(o=e,i=0,a=0,(r=t).weight&&(i+=r.barycenter*r.weight,a+=r.weight),o.weight&&(i+=o.barycenter*o.weight,a+=o.weight),r.vs=o.vs.concat(r.vs),r.barycenter=i/a,r.weight=a,r.i=Math.min(o.i,r.i),o.merged=!0)}}function o(e){return function(r){r.in.push(e),0===--r.indegree&&t.push(r)}}for(;t.length;){var i=t.pop();e.push(i),n.forEach(i.in.reverse(),r(i)),n.forEach(i.out,o(i))}return n.map(n.filter(e,(function(t){return!t.merged})),(function(t){return n.pick(t,["vs","i","barycenter","weight"])}))}(n.filter(r,(function(t){return!t.indegree})))}},2150:(t,e,r)=>{var n=r(2646),o=r(5434),i=r(7358),a=Math.max;t.exports=function(t,e,r){var s=null==t?0:t.length;if(!s)return-1;var u=null==r?0:i(r);return u<0&&(u=a(s+u,0)),n(t,o(e,3),u)}},2194:(t,e,r)=>{"use strict";var n=r(6036),o=r(4295),i=r(9515),a=r(459),s=r(9207),u=r(5489),c=r(4206).Graph,h=r(1882);function f(t,e,r){return n.map(e,(function(e){return s(t,e,r)}))}function d(t,e){var r=new c;n.forEach(t,(function(t){var o=t.graph().root,i=a(t,o,r,e);n.forEach(i.vs,(function(e,r){t.node(e).order=r})),u(t,r,i.vs)}))}function l(t,e){n.forEach(e,(function(e){n.forEach(e,(function(e,r){t.node(e).order=r}))}))}t.exports=function(t){var e=h.maxRank(t),r=f(t,n.range(1,e+1),"inEdges"),a=f(t,n.range(e-1,-1,-1),"outEdges"),s=o(t);l(t,s);for(var u,c=Number.POSITIVE_INFINITY,v=0,g=0;g<4;++v,++g){d(v%2?r:a,v%4>=2),s=h.buildLayerMatrix(t);var p=i(t,s);p<c&&(g=0,u=n.cloneDeep(s),c=p)}l(t,u)}},2323:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},2397:(t,e,r)=>{var n=r(957),o=r(8662);t.exports=function t(e,r,i,a,s){var u=-1,c=e.length;for(i||(i=o),s||(s=[]);++u<c;){var h=e[u];r>0&&i(h)?r>1?t(h,r-1,i,a,s):n(s,h):a||(s[s.length]=h)}return s}},2418:t=>{t.exports=function(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports.default=t.exports},2432:(t,e,r)=>{var n=r(4528),o=r(3919);t.exports=function(t,e){return null!=t&&o(t,e,n)}},2443:t=>{t.exports=function(t,e){return t<e}},2547:(t,e,r)=>{var n,o=r(2050),i=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";t.exports=function(t){return!!i&&i in t}},2569:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},2613:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},2626:t=>{t.exports=function(t){return t!=t}},2646:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return-1}},2679:(t,e,r)=>{var n=r(9838);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},2681:(t,e,r)=>{var n=r(5434),o=r(1693),i=r(1543);t.exports=function(t){return function(e,r,a){var s=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(s[t],t,s)}}var c=t(e,r,a);return c>-1?s[u?e[c]:c]:void 0}}},2705:(t,e,r)=>{var n=r(4542),o=r(4567),i=r(8972),a=r(2742),s=r(272),u=r(5167),c=r(8962),h=r(1266),f=r(9217),d=r(5179),l=r(7560),v=r(1340),g=r(6646),p=r(6095),y=r(5235);t.exports=function(t,e,r,m,w,x,b){var E=p(t,r),k=p(e,r),M=b.get(k);if(M)n(t,r,M);else{var N=x?x(E,k,r+"",t,e,b):void 0,_=void 0===N;if(_){var j=c(k),A=!j&&f(k),O=!j&&!A&&g(k);N=k,j||A||O?c(E)?N=E:h(E)?N=a(E):A?(_=!1,N=o(k,!0)):O?(_=!1,N=i(k,!0)):N=[]:v(k)||u(k)?(N=E,u(E)?N=y(E):l(E)&&!d(E)||(N=s(k))):_=!1}_&&(b.set(k,N),w(N,k,m,x,b),b.delete(k)),n(t,r,N)}}},2707:(t,e,r)=>{var n=r(1417),o=r(1693),i=r(8468),a=r(7560);t.exports=function(t,e,r){if(!a(r))return!1;var s=typeof e;return!!("number"==s?o(r)&&i(e,r.length):"string"==s&&e in r)&&n(r[e],t)}},2742:t=>{t.exports=function(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}},2812:(t,e,r)=>{var n=r(1126),o=r(7444),i=r(504),a=r(4537),s=r(8972);t.exports=function(t,e,r){var u=t.constructor;switch(e){case"[object ArrayBuffer]":return n(t);case"[object Boolean]":case"[object Date]":return new u(+t);case"[object DataView]":return o(t,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return s(t,r);case"[object Map]":case"[object Set]":return new u;case"[object Number]":case"[object String]":return new u(t);case"[object RegExp]":return i(t);case"[object Symbol]":return a(t)}}},2823:(t,e,r)=>{var n=r(1110),o=r(6229),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},2828:(t,e,r)=>{var n=r(3275);function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,n(o.key),o)}}t.exports=function(t,e,r){return e&&o(t.prototype,e),r&&o(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports},2848:(t,e,r)=>{var n=r(5818);t.exports=function(t){return n(t,5)}},2886:(t,e,r)=>{var n=r(1742),o=r(6692);t.exports=function(t,e,r,n){return function(t,e,r,n){var i,a,s={},u=new o,c=function(t){var e=t.v!==i?t.v:t.w,n=s[e],o=r(t),c=a.distance+o;if(o<0)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+t+" Weight: "+o);c<n.distance&&(n.distance=c,n.predecessor=i,u.decrease(e,c))};for(t.nodes().forEach((function(t){var r=t===e?0:Number.POSITIVE_INFINITY;s[t]={distance:r},u.add(t,r)}));u.size()>0&&(i=u.removeMin(),(a=s[i]).distance!==Number.POSITIVE_INFINITY);)n(i).forEach(c);return s}(t,String(e),r||i,n||function(e){return t.outEdges(e)})};var i=n.constant(1)},2920:(t,e,r)=>{var n=r(4096)();t.exports=n},2942:(t,e,r)=>{var n=r(9684),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,s=o(i.length-e,0),u=Array(s);++a<s;)u[a]=i[e+a];a=-1;for(var c=Array(e+1);++a<e;)c[a]=i[a];return c[e]=r(u),n(t,this,c)}}},2962:(t,e,r)=>{var n=r(3441)();t.exports=n},2988:(t,e,r)=>{var n=r(3936);t.exports=function(t){return n(this,t).get(t)}},2999:(t,e,r)=>{var n=r(1079),o=r(4680),i=r(4513),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},3114:t=>{var e=Object.prototype.hasOwnProperty;t.exports=function(t){var r=t.length,n=new t.constructor(r);return r&&"string"==typeof t[0]&&e.call(t,"index")&&(n.index=t.index,n.input=t.input),n}},3130:(t,e,r)=>{var n=r(7173),o=r(5167),i=r(8962),a=r(9217),s=r(8468),u=r(6646),c=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),h=!r&&o(t),f=!r&&!h&&a(t),d=!r&&!h&&!f&&u(t),l=r||h||f||d,v=l?n(t.length,String):[],g=v.length;for(var p in t)!e&&!c.call(t,p)||l&&("length"==p||f&&("offset"==p||"parent"==p)||d&&("buffer"==p||"byteLength"==p||"byteOffset"==p)||s(p,g))||v.push(p);return v}},3153:(t,e,r)=>{var n=r(5785),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,(function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)})),e}));t.exports=a},3167:(t,e,r)=>{var n=r(85),o=r(9942),i=r(5434);t.exports=function(t,e){var r={};return e=i(e,3),o(t,(function(t,o,i){n(r,o,e(t,o,i))})),r}},3191:(t,e,r)=>{var n=r(7953);t.exports=function(t,e){return t.get(n(t,e))},t.exports.__esModule=!0,t.exports.default=t.exports},3216:(t,e,r)=>{var n=r(2397),o=r(9797),i=r(6362),a=r(1266),s=o((function(t){return i(n(t,1,a,!0))}));t.exports=s},3225:(t,e,r)=>{var n=r(4513);t.exports=function(t){return"function"==typeof t?t:n}},3275:(t,e,r)=>{var n=r(9791).default,o=r(9542);t.exports=function(t){var e=o(t,"string");return"symbol"==n(e)?e:e+""},t.exports.__esModule=!0,t.exports.default=t.exports},3328:(t,e,r)=>{var n=r(4309);t.exports=function(t,e){return n(t,e,"post")}},3342:(t,e,r)=>{var n=r(1758)("length");t.exports=n},3441:(t,e,r)=>{var n=r(5058),o=r(2707),i=r(4121);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},3448:(t,e,r)=>{var n=r(5405),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},3468:(t,e,r)=>{var n=r(7560),o=r(1110),i=r(6678),a=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return i(t);var e=o(t),r=[];for(var s in t)("constructor"!=s||!e&&a.call(t,s))&&r.push(s);return r}},3512:(t,e,r)=>{var n=r(9838),o=r(2569),i=r(1002);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},3547:t=>{t.exports=function(){}},3563:(t,e,r)=>{var n=r(8421);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,s=i.length,u=r.length;++o<s;){var c=n(i[o],a[o]);if(c)return o>=u?c:c*("desc"==r[o]?-1:1)}return t.index-e.index}},3637:(t,e,r)=>{t.exports={graphlib:r(4206),layout:r(95),debug:r(546),util:{time:r(1882).time,notime:r(1882).notime},version:r(5781)}},3651:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},3663:(t,e,r)=>{var n=r(3791);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},3722:(t,e,r)=>{var n=r(5818);t.exports=function(t){return n(t,4)}},3743:(t,e,r)=>{var n=r(2099);t.exports=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=n(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var o=0,i=function(){};return{s:i,n:function(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}},t.exports.__esModule=!0,t.exports.default=t.exports},3791:(t,e,r)=>{var n=r(5850),o=r(8812);t.exports=function(t,e){for(var r=0,i=(e=n(e,t)).length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},3806:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1}},3818:(t,e,r)=>{var n=r(319)(r(6698),"WeakMap");t.exports=n},3904:(t,e,r)=>{var n=r(6036),o=r(1882);function i(t,e,r){for(var o;e.length&&(o=n.last(e)).i<=r;)e.pop(),t.push(o.vs),r++;return r}t.exports=function(t,e){var r,a=o.partition(t,(function(t){return n.has(t,"barycenter")})),s=a.lhs,u=n.sortBy(a.rhs,(function(t){return-t.i})),c=[],h=0,f=0,d=0;s.sort((r=!!e,function(t,e){return t.barycenter<e.barycenter?-1:t.barycenter>e.barycenter?1:r?e.i-t.i:t.i-e.i})),d=i(c,u,d),n.forEach(s,(function(t){d+=t.vs.length,c.push(t.vs),h+=t.barycenter*t.weight,f+=t.weight,d=i(c,u,d)}));var l={vs:n.flatten(c,!0)};return f&&(l.barycenter=h/f,l.weight=f),l}},3906:t=>{t.exports=function(t){return this.__data__.has(t)}},3919:(t,e,r)=>{var n=r(5850),o=r(5167),i=r(8962),a=r(8468),s=r(9703),u=r(8812);t.exports=function(t,e,r){for(var c=-1,h=(e=n(e,t)).length,f=!1;++c<h;){var d=u(e[c]);if(!(f=null!=t&&r(t,d)))break;t=t[d]}return f||++c!=h?f:!!(h=null==t?0:t.length)&&s(h)&&a(d,h)&&(i(t)||o(t))}},3936:(t,e,r)=>{var n=r(383);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},4008:(t,e,r)=>{var n=r(3936);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},4040:(t,e,r)=>{var n=r(4158);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},4069:(t,e,r)=>{var n=r(270),o=r(5422),i=r(6558);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},4071:(t,e,r)=>{var n=r(4309);t.exports=function(t,e){return n(t,e,"pre")}},4073:(t,e,r)=>{var n=r(1742);function o(t){var e={},r={},o=[];if(n.each(t.sinks(),(function a(s){if(n.has(r,s))throw new i;n.has(e,s)||(r[s]=!0,e[s]=!0,n.each(t.predecessors(s),a),delete r[s],o.push(s))})),n.size(e)!==t.nodeCount())throw new i;return o}function i(){}t.exports=o,o.CycleException=i,i.prototype=new Error},4096:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),s=a.length;s--;){var u=a[t?s:++o];if(!1===r(i[u],u,i))break}return e}}},4121:(t,e,r)=>{var n=r(1863),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}},4158:(t,e,r)=>{var n=r(1417);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return-1}},4185:(t,e,r)=>{var n=r(4073);t.exports=function(t){try{n(t)}catch(t){if(t instanceof n.CycleException)return!1;throw t}return!0}},4198:(t,e,r)=>{var n=r(1693);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,s=Object(r);(e?a--:++a<i)&&!1!==o(s[a],a,s););return r}}},4206:(t,e,r)=>{var n;try{n=r(591)}catch(t){}n||(n=window.graphlib),t.exports=n},4248:(t,e,r)=>{var n=r(1742),o=r(5527),i=r(6692);t.exports=function(t,e){var r,a=new o,s={},u=new i;function c(t){var n=t.v===r?t.w:t.v,o=u.priority(n);if(void 0!==o){var i=e(t);i<o&&(s[n]=r,u.decrease(n,i))}}if(0===t.nodeCount())return a;n.each(t.nodes(),(function(t){u.add(t,Number.POSITIVE_INFINITY),a.setNode(t)})),u.decrease(t.nodes()[0],0);for(var h=!1;u.size()>0;){if(r=u.removeMin(),n.has(s,r))a.setEdge(r,s[r]);else{if(h)throw new Error("Input graph is not connected: "+t);h=!0}t.nodeEdges(r).forEach(c)}return a}},4265:t=>{t.exports=function(){return[]}},4295:(t,e,r)=>{"use strict";var n=r(6036);t.exports=function(t){var e={},r=n.filter(t.nodes(),(function(e){return!t.children(e).length})),o=n.max(n.map(r,(function(e){return t.node(e).rank}))),i=n.map(n.range(o+1),(function(){return[]})),a=n.sortBy(r,(function(e){return t.node(e).rank}));return n.forEach(a,(function r(o){if(!n.has(e,o)){e[o]=!0;var a=t.node(o);i[a.rank].push(o),n.forEach(t.successors(o),r)}})),i}},4309:(t,e,r)=>{var n=r(1742);function o(t,e,r,i,a,s){n.has(i,e)||(i[e]=!0,r||s.push(e),n.each(a(e),(function(e){o(t,e,r,i,a,s)})),r&&s.push(e))}t.exports=function(t,e,r){n.isArray(e)||(e=[e]);var i=(t.isDirected()?t.successors:t.neighbors).bind(t),a=[],s={};return n.each(e,(function(e){if(!t.hasNode(e))throw new Error("Graph does not have node: "+e);o(t,e,"post"===r,s,i,a)})),a}},4317:(t,e,r)=>{"use strict";var n=r(6036);t.exports={longestPath:function(t){var e={};n.forEach(t.sources(),(function r(o){var i=t.node(o);if(n.has(e,o))return i.rank;e[o]=!0;var a=n.min(n.map(t.outEdges(o),(function(e){return r(e.w)-t.edge(e).minlen})));return a!==Number.POSITIVE_INFINITY&&null!=a||(a=0),i.rank=a}))},slack:function(t,e){return t.node(e.w).rank-t.node(e.v).rank-t.edge(e).minlen}}},4336:(t,e,r)=>{var n=r(2999),o=r(9824)(n);t.exports=o},4350:(t,e,r)=>{var n=r(2823),o=r(498),i=r(5167),a=r(8962),s=r(1693),u=r(9217),c=r(1110),h=r(6646),f=Object.prototype.hasOwnProperty;t.exports=function(t){if(null==t)return!0;if(s(t)&&(a(t)||"string"==typeof t||"function"==typeof t.splice||u(t)||h(t)||i(t)))return!t.length;var e=o(t);if("[object Map]"==e||"[object Set]"==e)return!t.size;if(c(t))return!n(t).length;for(var r in t)if(f.call(t,r))return!1;return!0}},4359:(t,e,r)=>{var n=r(6014),o=r(9858),i=r(8529),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},4442:(t,e,r)=>{var n=r(5422),o=r(6558),i=r(9838);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},4513:t=>{t.exports=function(t){return t}},4528:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},4537:(t,e,r)=>{var n=r(6014),o=n?n.prototype:void 0,i=o?o.valueOf:void 0;t.exports=function(t){return i?Object(i.call(t)):{}}},4542:(t,e,r)=>{var n=r(85),o=r(1417);t.exports=function(t,e,r){(void 0!==r&&!o(t[e],r)||void 0===r&&!(e in t))&&n(t,e,r)}},4545:(t,e,r)=>{var n=r(1742),o=r(5527);function i(t){return n.map(t.nodes(),(function(e){var r=t.node(e),o=t.parent(e),i={v:e};return n.isUndefined(r)||(i.value=r),n.isUndefined(o)||(i.parent=o),i}))}function a(t){return n.map(t.edges(),(function(e){var r=t.edge(e),o={v:e.v,w:e.w};return n.isUndefined(e.name)||(o.name=e.name),n.isUndefined(r)||(o.value=r),o}))}t.exports={write:function(t){var e={options:{directed:t.isDirected(),multigraph:t.isMultigraph(),compound:t.isCompound()},nodes:i(t),edges:a(t)};return n.isUndefined(t.graph())||(e.value=n.clone(t.graph())),e},read:function(t){var e=new o(t.options).setGraph(t.value);return n.each(t.nodes,(function(t){e.setNode(t.v,t.value),t.parent&&e.setParent(t.v,t.parent)})),n.each(t.edges,(function(t){e.setEdge({v:t.v,w:t.w,name:t.name},t.value)})),e}}},4567:(t,e,r)=>{t=r.nmd(t);var n=r(6698),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o?n.Buffer:void 0,s=a?a.allocUnsafe:void 0;t.exports=function(t,e){if(e)return t.slice();var r=t.length,n=s?s(r):new t.constructor(r);return t.copy(n),n}},4604:(t,e,r)=>{var n=r(319)(r(6698),"Set");t.exports=n},4620:(t,e,r)=>{var n=r(3130),o=r(3468),i=r(1693);t.exports=function(t){return i(t)?n(t,!0):o(t)}},4666:(t,e,r)=>{var n=r(2646),o=r(2626),i=r(3806);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},4668:(t,e,r)=>{"use strict";var n=r(6036),o=r(4206).Graph,i=r(4317).slack;function a(t,e){return n.forEach(t.nodes(),(function r(o){n.forEach(e.nodeEdges(o),(function(n){var a=n.v,s=o===a?n.w:a;t.hasNode(s)||i(e,n)||(t.setNode(s,{}),t.setEdge(o,s,{}),r(s))}))})),t.nodeCount()}function s(t,e){return n.minBy(e.edges(),(function(r){if(t.hasNode(r.v)!==t.hasNode(r.w))return i(e,r)}))}function u(t,e,r){n.forEach(t.nodes(),(function(t){e.node(t).rank+=r}))}t.exports=function(t){var e,r,n=new o({directed:!1}),c=t.nodes()[0],h=t.nodeCount();for(n.setNode(c,{});a(n,t)<h;)e=s(n,t),r=n.hasNode(e.v)?i(t,e):-i(t,e),u(n,t,r);return n}},4680:(t,e,r)=>{var n=r(319),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},4812:t=>{t.exports=function(t,e){return t.has(e)}},4814:t=>{var e="\\ud800-\\udfff",r="["+e+"]",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",o="\\ud83c[\\udffb-\\udfff]",i="[^"+e+"]",a="(?:\\ud83c[\\udde6-\\uddff]){2}",s="[\\ud800-\\udbff][\\udc00-\\udfff]",u="(?:"+n+"|"+o+")?",c="[\\ufe0e\\ufe0f]?",h=c+u+"(?:\\u200d(?:"+[i,a,s].join("|")+")"+c+u+")*",f="(?:"+[i+n+"?",n,a,s,r].join("|")+")",d=RegExp(o+"(?="+o+")|"+f+h,"g");t.exports=function(t){for(var e=d.lastIndex=0;d.test(t);)++e;return e}},4840:(t,e,r)=>{var n=r(9791).default;function o(){"use strict";t.exports=o=function(){return r},t.exports.__esModule=!0,t.exports.default=t.exports;var e,r={},i=Object.prototype,a=i.hasOwnProperty,s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",h=s.toStringTag||"@@toStringTag";function f(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{f({},"")}catch(e){f=function(t,e,r){return t[e]=r}}function d(t,r,n,o){var i=r&&r.prototype instanceof g?r:g,a=Object.create(i.prototype);return f(a,"_invoke",function(t,r,n){var o=1;return function(i,a){if(3===o)throw Error("Generator is already running");if(4===o){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var u=M(s,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(1===o)throw o=4,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=3;var c=l(t,r,n);if("normal"===c.type){if(o=n.done?4:2,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=4,n.method="throw",n.arg=c.arg)}}}(t,n,new j(o||[])),!0),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}r.wrap=d;var v={};function g(){}function p(){}function y(){}var m={};f(m,u,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w(A([])));x&&x!==i&&a.call(x,u)&&(m=x);var b=y.prototype=g.prototype=Object.create(m);function E(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,i,s,u){var c=l(t[o],t,i);if("throw"!==c.type){var h=c.arg,f=h.value;return f&&"object"==n(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,u)}),(function(t){r("throw",t,s,u)})):e.resolve(f).then((function(t){h.value=t,s(h)}),(function(t){return r("throw",t,s,u)}))}u(c.arg)}var o;f(this,"_invoke",(function(t,n){function i(){return new e((function(e,o){r(t,n,e,o)}))}return o=o?o.then(i,i):i()}),!0)}function M(t,r){var n=r.method,o=t.i[n];if(o===e)return r.delegate=null,"throw"===n&&t.i.return&&(r.method="return",r.arg=e,M(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,t.i,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[t.r]=a.value,r.next=t.n,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function N(t){this.tryEntries.push(t)}function _(t){var r=t[4]||{};r.type="normal",r.arg=e,t[4]=r}function j(t){this.tryEntries=[[-1]],t.forEach(N,this),this.reset(!0)}function A(t){if(null!=t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(a.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(n(t)+" is not iterable")}return p.prototype=y,f(b,"constructor",y),f(y,"constructor",p),p.displayName=f(y,h,"GeneratorFunction"),r.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,f(t,h,"GeneratorFunction")),t.prototype=Object.create(b),t},r.awrap=function(t){return{__await:t}},E(k.prototype),f(k.prototype,c,(function(){return this})),r.AsyncIterator=k,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new k(d(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(b),f(b,h,"Generator"),f(b,u,(function(){return this})),f(b,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var e=Object(t),r=[];for(var n in e)r.unshift(n);return function t(){for(;r.length;)if((n=r.pop())in e)return t.value=n,t.done=!1,t;return t.done=!0,t}},r.values=A,j.prototype={constructor:j,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(_),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(e){a.type="throw",a.arg=t,r.next=e}for(var o=r.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i[4],s=this.prev,u=i[1],c=i[2];if(-1===i[0])return n("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=s){if(s<u)return this.method="next",this.arg=e,n(u),!0;if(s<c)return n(c),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n[0]>-1&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]&&(o=null);var i=o?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),_(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r[0]===t){var n=r[4];if("throw"===n.type){var o=n.arg;_(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={i:A(t),r,n},"next"===this.method&&(this.arg=e),v}},r}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports},4857:(t,e,r)=>{var n=r(5559),o=0;t.exports=function(t){var e=++o;return n(t)+e}},4968:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}},4983:t=>{t.exports=function(t,e,r,n){var o=-1,i=null==t?0:t.length;for(n&&i&&(r=t[++o]);++o<i;)r=e(r,t[o],o,t);return r}},5025:(t,e,r)=>{"use strict";var n=r(6036);function o(t){n.forEach(t.nodes(),(function(e){i(t.node(e))})),n.forEach(t.edges(),(function(e){i(t.edge(e))}))}function i(t){var e=t.width;t.width=t.height,t.height=e}function a(t){t.y=-t.y}function s(t){var e=t.x;t.x=t.y,t.y=e}t.exports={adjust:function(t){var e=t.graph().rankdir.toLowerCase();"lr"!==e&&"rl"!==e||o(t)},undo:function(t){var e=t.graph().rankdir.toLowerCase();"bt"!==e&&"rl"!==e||function(t){n.forEach(t.nodes(),(function(e){a(t.node(e))})),n.forEach(t.edges(),(function(e){var r=t.edge(e);n.forEach(r.points,a),n.has(r,"y")&&a(r)}))}(t),"lr"!==e&&"rl"!==e||(function(t){n.forEach(t.nodes(),(function(e){s(t.node(e))})),n.forEach(t.edges(),(function(e){var r=t.edge(e);n.forEach(r.points,s),n.has(r,"x")&&s(r)}))}(t),o(t))}}},5058:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,s=r(e((n-t)/(o||1)),0),u=Array(s);s--;)u[i?s:++a]=t,t+=o;return u}},5096:(t,e,r)=>{var n=r(1614),o=r(7643)((function(t,e){return null==t?{}:n(t,e)}));t.exports=o},5143:(t,e,r)=>{var n=r(8729);t.exports=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=n(t)););return t},t.exports.__esModule=!0,t.exports.default=t.exports},5157:t=>{t.exports=function(t,e,r,n,o){return o(t,(function(t,o,i){r=n?(n=!1,t):e(r,t,o,i)})),r}},5167:(t,e,r)=>{var n=r(5967),o=r(6223),i=Object.prototype,a=i.hasOwnProperty,s=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!s.call(t,"callee")};t.exports=u},5179:(t,e,r)=>{var n=r(4359),o=r(7560);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5181:(t,e,r)=>{var n=r(381),o=r(1543);t.exports=function(t){return null==t?[]:n(t,o(t))}},5235:(t,e,r)=>{var n=r(9948),o=r(4620);t.exports=function(t){return n(t,o(t))}},5248:(t,e,r)=>{"use strict";var n=r(4317).longestPath,o=r(4668),i=r(1001);t.exports=function(t){switch(t.graph().ranker){case"network-simplex":default:!function(t){i(t)}(t);break;case"tight-tree":!function(t){n(t),o(t)}(t);break;case"longest-path":a(t)}};var a=n},5330:(t,e,r)=>{var n=r(5965),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,s){var u=1&r,c=n(t),h=c.length;if(h!=n(e).length&&!u)return!1;for(var f=h;f--;){var d=c[f];if(!(u?d in e:o.call(e,d)))return!1}var l=s.get(t),v=s.get(e);if(l&&v)return l==e&&v==t;var g=!0;s.set(t,e),s.set(e,t);for(var p=u;++f<h;){var y=t[d=c[f]],m=e[d];if(i)var w=u?i(m,y,d,e,t,s):i(y,m,d,t,e,s);if(!(void 0===w?y===m||a(y,m,r,i,s):w)){g=!1;break}p||(p="constructor"==d)}if(g&&!p){var x=t.constructor,b=e.constructor;x==b||!("constructor"in t)||!("constructor"in e)||"function"==typeof x&&x instanceof x&&"function"==typeof b&&b instanceof b||(g=!1)}return s.delete(t),s.delete(e),g}},5362:(t,e,r)=>{"use strict";var n=r(6036),o=r(4206).Graph,i=r(1882);function a(t,e){var r={};return n.reduce(e,(function(e,o){var i=0,a=0,s=e.length,c=n.last(o);return n.forEach(o,(function(e,h){var f=function(t,e){if(t.node(e).dummy)return n.find(t.predecessors(e),(function(e){return t.node(e).dummy}))}(t,e),d=f?t.node(f).order:s;(f||e===c)&&(n.forEach(o.slice(a,h+1),(function(e){n.forEach(t.predecessors(e),(function(n){var o=t.node(n),a=o.order;!(a<i||d<a)||o.dummy&&t.node(e).dummy||u(r,n,e)}))})),a=h+1,i=d)})),o})),r}function s(t,e){var r={};function o(e,o,i,a,s){var c;n.forEach(n.range(o,i),(function(o){c=e[o],t.node(c).dummy&&n.forEach(t.predecessors(c),(function(e){var n=t.node(e);n.dummy&&(n.order<a||n.order>s)&&u(r,e,c)}))}))}return n.reduce(e,(function(e,r){var i,a=-1,s=0;return n.forEach(r,(function(n,u){if("border"===t.node(n).dummy){var c=t.predecessors(n);c.length&&(i=t.node(c[0]).order,o(r,s,u,a,i),s=u,a=i)}o(r,s,r.length,i,e.length)})),r})),r}function u(t,e,r){if(e>r){var n=e;e=r,r=n}var o=t[e];o||(t[e]=o={}),o[r]=!0}function c(t,e,r){if(e>r){var o=e;e=r,r=o}return n.has(t[e],r)}function h(t,e,r,o){var i={},a={},s={};return n.forEach(e,(function(t){n.forEach(t,(function(t,e){i[t]=t,a[t]=t,s[t]=e}))})),n.forEach(e,(function(t){var e=-1;n.forEach(t,(function(t){var u=o(t);if(u.length){u=n.sortBy(u,(function(t){return s[t]}));for(var h=(u.length-1)/2,f=Math.floor(h),d=Math.ceil(h);f<=d;++f){var l=u[f];a[t]===t&&e<s[l]&&!c(r,t,l)&&(a[l]=t,a[t]=i[t]=i[l],e=s[l])}}}))})),{root:i,align:a}}function f(t,e,r,i,a){var s={},u=function(t,e,r,i){var a=new o,s=t.graph(),u=function(t,e,r){return function(o,i,a){var s,u=o.node(i),c=o.node(a),h=0;if(h+=u.width/2,n.has(u,"labelpos"))switch(u.labelpos.toLowerCase()){case"l":s=-u.width/2;break;case"r":s=u.width/2}if(s&&(h+=r?s:-s),s=0,h+=(u.dummy?e:t)/2,h+=(c.dummy?e:t)/2,h+=c.width/2,n.has(c,"labelpos"))switch(c.labelpos.toLowerCase()){case"l":s=c.width/2;break;case"r":s=-c.width/2}return s&&(h+=r?s:-s),s=0,h}}(s.nodesep,s.edgesep,i);return n.forEach(e,(function(e){var o;n.forEach(e,(function(e){var n=r[e];if(a.setNode(n),o){var i=r[o],s=a.edge(i,n);a.setEdge(i,n,Math.max(u(t,e,o),s||0))}o=e}))})),a}(t,e,r,a),c=a?"borderLeft":"borderRight";function h(t,e){for(var r=u.nodes(),n=r.pop(),o={};n;)o[n]?t(n):(o[n]=!0,r.push(n),r=r.concat(e(n))),n=r.pop()}return h((function(t){s[t]=u.inEdges(t).reduce((function(t,e){return Math.max(t,s[e.v]+u.edge(e))}),0)}),u.predecessors.bind(u)),h((function(e){var r=u.outEdges(e).reduce((function(t,e){return Math.min(t,s[e.w]-u.edge(e))}),Number.POSITIVE_INFINITY),n=t.node(e);r!==Number.POSITIVE_INFINITY&&n.borderType!==c&&(s[e]=Math.max(s[e],r))}),u.successors.bind(u)),n.forEach(i,(function(t){s[t]=s[r[t]]})),s}function d(t,e){return n.minBy(n.values(e),(function(e){var r=Number.NEGATIVE_INFINITY,o=Number.POSITIVE_INFINITY;return n.forIn(e,(function(e,n){var i=function(t,e){return t.node(e).width}(t,n)/2;r=Math.max(e+i,r),o=Math.min(e-i,o)})),r-o}))}function l(t,e){var r=n.values(e),o=n.min(r),i=n.max(r);n.forEach(["u","d"],(function(r){n.forEach(["l","r"],(function(a){var s,u=r+a,c=t[u];if(c!==e){var h=n.values(c);(s="l"===a?o-n.min(h):i-n.max(h))&&(t[u]=n.mapValues(c,(function(t){return t+s})))}}))}))}function v(t,e){return n.mapValues(t.ul,(function(r,o){if(e)return t[e.toLowerCase()][o];var i=n.sortBy(n.map(t,o));return(i[1]+i[2])/2}))}t.exports={positionX:function(t){var e,r=i.buildLayerMatrix(t),o=n.merge(a(t,r),s(t,r)),u={};n.forEach(["u","d"],(function(i){e="u"===i?r:n.values(r).reverse(),n.forEach(["l","r"],(function(r){"r"===r&&(e=n.map(e,(function(t){return n.values(t).reverse()})));var a=("u"===i?t.predecessors:t.successors).bind(t),s=h(0,e,o,a),c=f(t,e,s.root,s.align,"r"===r);"r"===r&&(c=n.mapValues(c,(function(t){return-t}))),u[i+r]=c}))}));var c=d(t,u);return l(u,c),v(u,t.graph().align)},findType1Conflicts:a,findType2Conflicts:s,addConflict:u,hasConflict:c,verticalAlignment:h,horizontalCompaction:f,alignCoordinates:l,findSmallestWidthAlignment:d,balance:v}},5405:(t,e,r)=>{var n=r(319)(Object,"create");t.exports=n},5422:(t,e,r)=>{var n=r(9493),o=r(981),i=r(6204),a=r(7464),s=r(4040);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,t.exports=u},5434:(t,e,r)=>{var n=r(7866),o=r(6539),i=r(4513),a=r(8962),s=r(1326);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):s(t)}},5489:(t,e,r)=>{var n=r(6036);t.exports=function(t,e,r){var o,i={};n.forEach(r,(function(r){for(var n,a,s=t.parent(r);s;){if((n=t.parent(s))?(a=i[n],i[n]=s):(a=o,o=s),a&&a!==s)return void e.setEdge(a,s);s=n}}))}},5506:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r}},5527:(t,e,r)=>{"use strict";var n=r(1742);t.exports=i;var o="\0";function i(t){this._isDirected=!n.has(t,"directed")||t.directed,this._isMultigraph=!!n.has(t,"multigraph")&&t.multigraph,this._isCompound=!!n.has(t,"compound")&&t.compound,this._label=void 0,this._defaultNodeLabelFn=n.constant(void 0),this._defaultEdgeLabelFn=n.constant(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[o]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}function a(t,e){t[e]?t[e]++:t[e]=1}function s(t,e){--t[e]||delete t[e]}function u(t,e,r,o){var i=""+e,a=""+r;if(!t&&i>a){var s=i;i=a,a=s}return i+""+a+""+(n.isUndefined(o)?"\0":o)}function c(t,e){return u(t,e.v,e.w,e.name)}i.prototype._nodeCount=0,i.prototype._edgeCount=0,i.prototype.isDirected=function(){return this._isDirected},i.prototype.isMultigraph=function(){return this._isMultigraph},i.prototype.isCompound=function(){return this._isCompound},i.prototype.setGraph=function(t){return this._label=t,this},i.prototype.graph=function(){return this._label},i.prototype.setDefaultNodeLabel=function(t){return n.isFunction(t)||(t=n.constant(t)),this._defaultNodeLabelFn=t,this},i.prototype.nodeCount=function(){return this._nodeCount},i.prototype.nodes=function(){return n.keys(this._nodes)},i.prototype.sources=function(){var t=this;return n.filter(this.nodes(),(function(e){return n.isEmpty(t._in[e])}))},i.prototype.sinks=function(){var t=this;return n.filter(this.nodes(),(function(e){return n.isEmpty(t._out[e])}))},i.prototype.setNodes=function(t,e){var r=arguments,o=this;return n.each(t,(function(t){r.length>1?o.setNode(t,e):o.setNode(t)})),this},i.prototype.setNode=function(t,e){return n.has(this._nodes,t)?(arguments.length>1&&(this._nodes[t]=e),this):(this._nodes[t]=arguments.length>1?e:this._defaultNodeLabelFn(t),this._isCompound&&(this._parent[t]=o,this._children[t]={},this._children[o][t]=!0),this._in[t]={},this._preds[t]={},this._out[t]={},this._sucs[t]={},++this._nodeCount,this)},i.prototype.node=function(t){return this._nodes[t]},i.prototype.hasNode=function(t){return n.has(this._nodes,t)},i.prototype.removeNode=function(t){var e=this;if(n.has(this._nodes,t)){var r=function(t){e.removeEdge(e._edgeObjs[t])};delete this._nodes[t],this._isCompound&&(this._removeFromParentsChildList(t),delete this._parent[t],n.each(this.children(t),(function(t){e.setParent(t)})),delete this._children[t]),n.each(n.keys(this._in[t]),r),delete this._in[t],delete this._preds[t],n.each(n.keys(this._out[t]),r),delete this._out[t],delete this._sucs[t],--this._nodeCount}return this},i.prototype.setParent=function(t,e){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(n.isUndefined(e))e=o;else{for(var r=e+="";!n.isUndefined(r);r=this.parent(r))if(r===t)throw new Error("Setting "+e+" as parent of "+t+" would create a cycle");this.setNode(e)}return this.setNode(t),this._removeFromParentsChildList(t),this._parent[t]=e,this._children[e][t]=!0,this},i.prototype._removeFromParentsChildList=function(t){delete this._children[this._parent[t]][t]},i.prototype.parent=function(t){if(this._isCompound){var e=this._parent[t];if(e!==o)return e}},i.prototype.children=function(t){if(n.isUndefined(t)&&(t=o),this._isCompound){var e=this._children[t];if(e)return n.keys(e)}else{if(t===o)return this.nodes();if(this.hasNode(t))return[]}},i.prototype.predecessors=function(t){var e=this._preds[t];if(e)return n.keys(e)},i.prototype.successors=function(t){var e=this._sucs[t];if(e)return n.keys(e)},i.prototype.neighbors=function(t){var e=this.predecessors(t);if(e)return n.union(e,this.successors(t))},i.prototype.isLeaf=function(t){return 0===(this.isDirected()?this.successors(t):this.neighbors(t)).length},i.prototype.filterNodes=function(t){var e=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});e.setGraph(this.graph());var r=this;n.each(this._nodes,(function(r,n){t(n)&&e.setNode(n,r)})),n.each(this._edgeObjs,(function(t){e.hasNode(t.v)&&e.hasNode(t.w)&&e.setEdge(t,r.edge(t))}));var o={};function i(t){var n=r.parent(t);return void 0===n||e.hasNode(n)?(o[t]=n,n):n in o?o[n]:i(n)}return this._isCompound&&n.each(e.nodes(),(function(t){e.setParent(t,i(t))})),e},i.prototype.setDefaultEdgeLabel=function(t){return n.isFunction(t)||(t=n.constant(t)),this._defaultEdgeLabelFn=t,this},i.prototype.edgeCount=function(){return this._edgeCount},i.prototype.edges=function(){return n.values(this._edgeObjs)},i.prototype.setPath=function(t,e){var r=this,o=arguments;return n.reduce(t,(function(t,n){return o.length>1?r.setEdge(t,n,e):r.setEdge(t,n),n})),this},i.prototype.setEdge=function(){var t,e,r,o,i=!1,s=arguments[0];"object"==typeof s&&null!==s&&"v"in s?(t=s.v,e=s.w,r=s.name,2===arguments.length&&(o=arguments[1],i=!0)):(t=s,e=arguments[1],r=arguments[3],arguments.length>2&&(o=arguments[2],i=!0)),t=""+t,e=""+e,n.isUndefined(r)||(r=""+r);var c=u(this._isDirected,t,e,r);if(n.has(this._edgeLabels,c))return i&&(this._edgeLabels[c]=o),this;if(!n.isUndefined(r)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(t),this.setNode(e),this._edgeLabels[c]=i?o:this._defaultEdgeLabelFn(t,e,r);var h=function(t,e,r,n){var o=""+e,i=""+r;if(!t&&o>i){var a=o;o=i,i=a}var s={v:o,w:i};return n&&(s.name=n),s}(this._isDirected,t,e,r);return t=h.v,e=h.w,Object.freeze(h),this._edgeObjs[c]=h,a(this._preds[e],t),a(this._sucs[t],e),this._in[e][c]=h,this._out[t][c]=h,this._edgeCount++,this},i.prototype.edge=function(t,e,r){var n=1===arguments.length?c(this._isDirected,arguments[0]):u(this._isDirected,t,e,r);return this._edgeLabels[n]},i.prototype.hasEdge=function(t,e,r){var o=1===arguments.length?c(this._isDirected,arguments[0]):u(this._isDirected,t,e,r);return n.has(this._edgeLabels,o)},i.prototype.removeEdge=function(t,e,r){var n=1===arguments.length?c(this._isDirected,arguments[0]):u(this._isDirected,t,e,r),o=this._edgeObjs[n];return o&&(t=o.v,e=o.w,delete this._edgeLabels[n],delete this._edgeObjs[n],s(this._preds[e],t),s(this._sucs[t],e),delete this._in[e][n],delete this._out[t][n],this._edgeCount--),this},i.prototype.inEdges=function(t,e){var r=this._in[t];if(r){var o=n.values(r);return e?n.filter(o,(function(t){return t.v===e})):o}},i.prototype.outEdges=function(t,e){var r=this._out[t];if(r){var o=n.values(r);return e?n.filter(o,(function(t){return t.w===e})):o}},i.prototype.nodeEdges=function(t,e){var r=this.inEdges(t,e);if(r)return r.concat(this.outEdges(t,e))}},5543:(t,e,r)=>{var n=r(7560);t.exports=function(t){return t==t&&!n(t)}},5559:(t,e,r)=>{var n=r(5991);t.exports=function(t){return null==t?"":n(t)}},5675:(t,e,r)=>{var n=r(319)(r(6698),"DataView");t.exports=n},5711:(t,e,r)=>{t.exports={Graph:r(5527),version:r(9375)}},5781:t=>{t.exports="0.8.5"},5785:(t,e,r)=>{var n=r(2679);t.exports=function(t){var e=n(t,(function(t){return 500===r.size&&r.clear(),t})),r=e.cache;return e}},5818:(t,e,r)=>{var n=r(9776),o=r(6532),i=r(9210),a=r(8790),s=r(9761),u=r(4567),c=r(2742),h=r(6358),f=r(1889),d=r(5965),l=r(8174),v=r(498),g=r(3114),p=r(2812),y=r(272),m=r(8962),w=r(9217),x=r(7877),b=r(7560),E=r(6643),k=r(1543),M=r(4620),N="[object Arguments]",_="[object Function]",j="[object Object]",A={};A[N]=A["[object Array]"]=A["[object ArrayBuffer]"]=A["[object DataView]"]=A["[object Boolean]"]=A["[object Date]"]=A["[object Float32Array]"]=A["[object Float64Array]"]=A["[object Int8Array]"]=A["[object Int16Array]"]=A["[object Int32Array]"]=A["[object Map]"]=A["[object Number]"]=A[j]=A["[object RegExp]"]=A["[object Set]"]=A["[object String]"]=A["[object Symbol]"]=A["[object Uint8Array]"]=A["[object Uint8ClampedArray]"]=A["[object Uint16Array]"]=A["[object Uint32Array]"]=!0,A["[object Error]"]=A[_]=A["[object WeakMap]"]=!1,t.exports=function t(e,r,O,S,z,R){var I,P=1&r,T=2&r,C=4&r;if(O&&(I=z?O(e,S,z,R):O(e)),void 0!==I)return I;if(!b(e))return e;var D=m(e);if(D){if(I=g(e),!P)return c(e,I)}else{var L=v(e),F=L==_||"[object GeneratorFunction]"==L;if(w(e))return u(e,P);if(L==j||L==N||F&&!z){if(I=T||F?{}:y(e),!P)return T?f(e,s(I,e)):h(e,a(I,e))}else{if(!A[L])return z?e:{};I=p(e,L,P)}}R||(R=new n);var q=R.get(e);if(q)return q;R.set(e,I),E(e)?e.forEach((function(n){I.add(t(n,r,O,n,e,R))})):x(e)&&e.forEach((function(n,o){I.set(o,t(n,r,O,o,e,R))}));var V=D?void 0:(C?T?l:d:T?M:k)(e);return o(V||e,(function(n,o){V&&(n=e[o=n]),i(I,o,t(n,r,O,o,e,R))})),I}},5850:(t,e,r)=>{var n=r(8962),o=r(1751),i=r(3153),a=r(5559);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},5965:(t,e,r)=>{var n=r(8832),o=r(1263),i=r(1543);t.exports=function(t){return n(t,i,o)}},5967:(t,e,r)=>{var n=r(4359),o=r(6223);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},5991:(t,e,r)=>{var n=r(6014),o=r(2323),i=r(8962),a=r(943),s=n?n.prototype:void 0,u=s?s.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return u?u.call(e):"";var r=e+"";return"0"==r&&1/e==-1/0?"-0":r}},6014:(t,e,r)=>{var n=r(6698).Symbol;t.exports=n},6036:(t,e,r)=>{var n;try{n={cloneDeep:r(2848),constant:r(1079),defaults:r(7945),each:r(9054),filter:r(7937),find:r(1362),flatten:r(7786),forEach:r(6269),forIn:r(8759),has:r(1763),isUndefined:r(7791),last:r(6395),map:r(349),mapValues:r(3167),max:r(9365),merge:r(8815),min:r(9007),minBy:r(9518),now:r(6243),pick:r(5096),range:r(2962),reduce:r(6309),sortBy:r(302),uniqueId:r(4857),values:r(5181),zipObject:r(7023)}}catch(t){}n||(n=window._),t.exports=n},6050:(t,e,r)=>{var n=r(3512),o=r(7029),i=r(4812);t.exports=function(t,e,r,a,s,u){var c=1&r,h=t.length,f=e.length;if(h!=f&&!(c&&f>h))return!1;var d=u.get(t),l=u.get(e);if(d&&l)return d==e&&l==t;var v=-1,g=!0,p=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++v<h;){var y=t[v],m=e[v];if(a)var w=c?a(m,y,v,e,t,u):a(y,m,v,t,e,u);if(void 0!==w){if(w)continue;g=!1;break}if(p){if(!o(e,(function(t,e){if(!i(p,e)&&(y===t||s(y,t,r,a,u)))return p.push(e)}))){g=!1;break}}else if(y!==m&&!s(y,m,r,a,u)){g=!1;break}}return u.delete(t),u.delete(e),g}},6095:t=>{t.exports=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}},6124:(t,e,r)=>{var n=r(7108),o=r(8729);t.exports=function(t,e,r,i){var a=n(o(1&i?t.prototype:t),e,r);return 2&i&&"function"==typeof a?function(t){return a.apply(r,t)}:a},t.exports.__esModule=!0,t.exports.default=t.exports},6204:(t,e,r)=>{var n=r(4158);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},6223:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},6229:(t,e,r)=>{var n=r(9122)(Object.keys,Object);t.exports=n},6243:(t,e,r)=>{var n=r(6698);t.exports=function(){return n.Date.now()}},6269:(t,e,r)=>{var n=r(6532),o=r(334),i=r(3225),a=r(8962);t.exports=function(t,e){return(a(t)?n:o)(t,i(e))}},6309:(t,e,r)=>{var n=r(4983),o=r(334),i=r(5434),a=r(5157),s=r(8962);t.exports=function(t,e,r){var u=s(t)?n:a,c=arguments.length<3;return u(t,i(e,4),r,c,o)}},6354:(t,e,r)=>{var n=r(2323),o=r(3791),i=r(5434),a=r(477),s=r(9150),u=r(1896),c=r(3563),h=r(4513),f=r(8962);t.exports=function(t,e,r){e=e.length?n(e,(function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t})):[h];var d=-1;e=n(e,u(i));var l=a(t,(function(t,r,o){return{criteria:n(e,(function(e){return e(t)})),index:++d,value:t}}));return s(l,(function(t,e){return c(t,e,r)}))}},6358:(t,e,r)=>{var n=r(9948),o=r(1263);t.exports=function(t,e){return n(t,o(t),e)}},6362:(t,e,r)=>{var n=r(3512),o=r(1888),i=r(7912),a=r(4812),s=r(7972),u=r(4968);t.exports=function(t,e,r){var c=-1,h=o,f=t.length,d=!0,l=[],v=l;if(r)d=!1,h=i;else if(f>=200){var g=e?null:s(t);if(g)return u(g);d=!1,h=a,v=new n}else v=e?[]:l;t:for(;++c<f;){var p=t[c],y=e?e(p):p;if(p=r||0!==p?p:0,d&&y==y){for(var m=v.length;m--;)if(v[m]===y)continue t;e&&v.push(y),l.push(p)}else h(v,y,r)||(v!==l&&v.push(y),l.push(p))}return l}},6395:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},6521:(t,e,r)=>{var n=r(6014),o=r(35),i=r(1417),a=r(6050),s=r(5506),u=r(4968),c=n?n.prototype:void 0,h=c?c.valueOf:void 0;t.exports=function(t,e,r,n,c,f,d){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!f(new o(t),new o(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var l=s;case"[object Set]":var v=1&n;if(l||(l=u),t.size!=e.size&&!v)return!1;var g=d.get(t);if(g)return g==e;n|=2,d.set(t,e);var p=a(l(t),l(e),n,c,f,d);return d.delete(t),p;case"[object Symbol]":if(h)return h.call(t)==h.call(e)}return!1}},6532:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n&&!1!==e(t[r],r,t););return t}},6539:(t,e,r)=>{var n=r(2027),o=r(3663),i=r(2432),a=r(1751),s=r(5543),u=r(756),c=r(8812);t.exports=function(t,e){return a(t)&&s(e)?u(c(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},6558:(t,e,r)=>{var n=r(319)(r(6698),"Map");t.exports=n},6584:t=>{t.exports=function(){return!1}},6643:(t,e,r)=>{var n=r(8739),o=r(1896),i=r(1710),a=i&&i.isSet,s=a?o(a):n;t.exports=s},6646:(t,e,r)=>{var n=r(1846),o=r(1896),i=r(1710),a=i&&i.isTypedArray,s=a?o(a):n;t.exports=s},6678:t=>{t.exports=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}},6682:t=>{t.exports=function(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n},t.exports.__esModule=!0,t.exports.default=t.exports},6692:(t,e,r)=>{var n=r(1742);function o(){this._arr=[],this._keyIndices={}}t.exports=o,o.prototype.size=function(){return this._arr.length},o.prototype.keys=function(){return this._arr.map((function(t){return t.key}))},o.prototype.has=function(t){return n.has(this._keyIndices,t)},o.prototype.priority=function(t){var e=this._keyIndices[t];if(void 0!==e)return this._arr[e].priority},o.prototype.min=function(){if(0===this.size())throw new Error("Queue underflow");return this._arr[0].key},o.prototype.add=function(t,e){var r=this._keyIndices;if(t=String(t),!n.has(r,t)){var o=this._arr,i=o.length;return r[t]=i,o.push({key:t,priority:e}),this._decrease(i),!0}return!1},o.prototype.removeMin=function(){this._swap(0,this._arr.length-1);var t=this._arr.pop();return delete this._keyIndices[t.key],this._heapify(0),t.key},o.prototype.decrease=function(t,e){var r=this._keyIndices[t];if(e>this._arr[r].priority)throw new Error("New priority is greater than current priority. Key: "+t+" Old: "+this._arr[r].priority+" New: "+e);this._arr[r].priority=e,this._decrease(r)},o.prototype._heapify=function(t){var e=this._arr,r=2*t,n=r+1,o=t;r<e.length&&(o=e[r].priority<e[o].priority?r:o,n<e.length&&(o=e[n].priority<e[o].priority?n:o),o!==t&&(this._swap(t,o),this._heapify(o)))},o.prototype._decrease=function(t){for(var e,r=this._arr,n=r[t].priority;0!==t&&!(r[e=t>>1].priority<n);)this._swap(t,e),t=e},o.prototype._swap=function(t,e){var r=this._arr,n=this._keyIndices,o=r[t],i=r[e];r[t]=i,r[e]=o,n[i.key]=t,n[o.key]=e}},6698:(t,e,r)=>{var n=r(9107),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},6727:(t,e,r)=>{"use strict";var n=r(6124).default,o=r(7236).default,i=r(411).default,a=r(3191).default,s=r(7159).default,u=r(9370).default,c=r(8853).default,h=r(9844).default,f=r(7953).default,d=r(9168).default,l=r(3743).default,v=r(4840).default,g=(r(9940).default,r(8186).default),p=r(2828).default,y=r(1869),m=r(8),w=" ".repeat(2),x=" ".repeat(4);function b(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.maxRows,n=void 0===r?15:r,o=e.maxColumns,i=void 0===o?10:o,a=e.maxNumSize,s=void 0===a?8:a,u=e.padMinus,c=void 0===u?"auto":u;return"".concat(t.constructor.name," {\n").concat(w,"[\n").concat(x).concat(function(t,e,r,n,o){var i=t.rows,a=t.columns,s=Math.min(i,e),u=Math.min(a,r),c=[];if("auto"===o){o=!1;t:for(var h=0;h<s;h++)for(var f=0;f<u;f++)if(t.get(h,f)<0){o=!0;break t}}for(var d=0;d<s;d++){for(var l=[],v=0;v<u;v++)l.push(E(t.get(d,v),n,o));c.push("".concat(l.join(" ")))}return u!==a&&(c[c.length-1]+=" ... ".concat(a-r," more columns")),s!==i&&c.push("... ".concat(i-e," more rows")),c.join("\n".concat(x))}(t,n,i,s,c),"\n").concat(w,"]\n").concat(w,"rows: ").concat(t.rows,"\n").concat(w,"columns: ").concat(t.columns,"\n}")}function E(t,e,r){return(t>=0&&r?" ".concat(k(t,e-1)):k(t,e)).padEnd(e)}function k(t,e){var r=t.toString();if(r.length<=e)return r;var n=t.toFixed(e);if(n.length>e&&(n=t.toFixed(Math.max(0,e-(n.length-e)))),n.length<=e&&!n.startsWith("0.000")&&!n.startsWith("-0.000"))return n;var o=t.toExponential(e);return o.length>e&&(o=t.toExponential(Math.max(0,e-(o.length-e)))),o.slice(0)}function M(t,e,r){var n=r?t.rows:t.rows-1;if(e<0||e>n)throw new RangeError("Row index out of range")}function N(t,e,r){var n=r?t.columns:t.columns-1;if(e<0||e>n)throw new RangeError("Column index out of range")}function _(t,e){if(e.to1DArray&&(e=e.to1DArray()),e.length!==t.columns)throw new RangeError("vector size must be the same as the number of columns");return e}function j(t,e){if(e.to1DArray&&(e=e.to1DArray()),e.length!==t.rows)throw new RangeError("vector size must be the same as the number of rows");return e}function A(t,e){if(!y.isAnyArray(e))throw new TypeError("row indices must be an array");for(var r=0;r<e.length;r++)if(e[r]<0||e[r]>=t.rows)throw new RangeError("row indices are out of range")}function O(t,e){if(!y.isAnyArray(e))throw new TypeError("column indices must be an array");for(var r=0;r<e.length;r++)if(e[r]<0||e[r]>=t.columns)throw new RangeError("column indices are out of range")}function S(t,e,r,n,o){if(5!==arguments.length)throw new RangeError("expected 4 arguments");if(R("startRow",e),R("endRow",r),R("startColumn",n),R("endColumn",o),e>r||n>o||e<0||e>=t.rows||r<0||r>=t.rows||n<0||n>=t.columns||o<0||o>=t.columns)throw new RangeError("Submatrix indices are out of range")}function z(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[],n=0;n<t;n++)r.push(e);return r}function R(t,e){if("number"!=typeof e)throw new TypeError("".concat(t," must be a number"))}function I(t){if(t.isEmpty())throw new Error("Empty matrix has no elements to index")}var P=function(){function t(){g(this,t)}return p(t,[{key:"size",get:function(){return this.rows*this.columns}},{key:"apply",value:function(t){if("function"!=typeof t)throw new TypeError("callback must be a function");for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)t.call(this,e,r);return this}},{key:"to1DArray",value:function(){for(var t=[],e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)t.push(this.get(e,r));return t}},{key:"to2DArray",value:function(){for(var t=[],e=0;e<this.rows;e++){t.push([]);for(var r=0;r<this.columns;r++)t[e].push(this.get(e,r))}return t}},{key:"toJSON",value:function(){return this.to2DArray()}},{key:"isRowVector",value:function(){return 1===this.rows}},{key:"isColumnVector",value:function(){return 1===this.columns}},{key:"isVector",value:function(){return 1===this.rows||1===this.columns}},{key:"isSquare",value:function(){return this.rows===this.columns}},{key:"isEmpty",value:function(){return 0===this.rows||0===this.columns}},{key:"isSymmetric",value:function(){if(this.isSquare()){for(var t=0;t<this.rows;t++)for(var e=0;e<=t;e++)if(this.get(t,e)!==this.get(e,t))return!1;return!0}return!1}},{key:"isDistance",value:function(){if(!this.isSymmetric())return!1;for(var t=0;t<this.rows;t++)if(0!==this.get(t,t))return!1;return!0}},{key:"isEchelonForm",value:function(){for(var t=0,e=0,r=-1,n=!0,o=!1;t<this.rows&&n;){for(e=0,o=!1;e<this.columns&&!1===o;)0===this.get(t,e)?e++:1===this.get(t,e)&&e>r?(o=!0,r=e):(n=!1,o=!0);t++}return n}},{key:"isReducedEchelonForm",value:function(){for(var t=0,e=0,r=-1,n=!0,o=!1;t<this.rows&&n;){for(e=0,o=!1;e<this.columns&&!1===o;)0===this.get(t,e)?e++:1===this.get(t,e)&&e>r?(o=!0,r=e):(n=!1,o=!0);for(var i=e+1;i<this.rows;i++)0!==this.get(t,i)&&(n=!1);t++}return n}},{key:"echelonForm",value:function(){for(var t=this.clone(),e=0,r=0;e<t.rows&&r<t.columns;){for(var n=e,o=e;o<t.rows;o++)t.get(o,r)>t.get(n,r)&&(n=o);if(0===t.get(n,r))r++;else{t.swapRows(e,n);for(var i=t.get(e,r),a=r;a<t.columns;a++)t.set(e,a,t.get(e,a)/i);for(var s=e+1;s<t.rows;s++){var u=t.get(s,r)/t.get(e,r);t.set(s,r,0);for(var c=r+1;c<t.columns;c++)t.set(s,c,t.get(s,c)-t.get(e,c)*u)}e++,r++}}return t}},{key:"reducedEchelonForm",value:function(){for(var t=this.echelonForm(),e=t.columns,r=t.rows,n=r-1;n>=0;)if(0===t.maxRow(n))n--;else{for(var o=0,i=!1;o<r&&!1===i;)1===t.get(n,o)?i=!0:o++;for(var a=0;a<n;a++)for(var s=t.get(a,o),u=o;u<e;u++){var c=t.get(a,u)-s*t.get(n,u);t.set(a,u,c)}n--}return t}},{key:"set",value:function(){throw new Error("set method is unimplemented")}},{key:"get",value:function(){throw new Error("get method is unimplemented")}},{key:"repeat",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("object"!=typeof t)throw new TypeError("options must be an object");var e=t.rows,r=void 0===e?1:e,n=t.columns,o=void 0===n?1:n;if(!Number.isInteger(r)||r<=0)throw new TypeError("rows must be a positive integer");if(!Number.isInteger(o)||o<=0)throw new TypeError("columns must be a positive integer");for(var i=new D(this.rows*r,this.columns*o),a=0;a<r;a++)for(var s=0;s<o;s++)i.setSubMatrix(this,this.rows*a,this.columns*s);return i}},{key:"fill",value:function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,t);return this}},{key:"neg",value:function(){return this.mulS(-1)}},{key:"getRow",value:function(t){M(this,t);for(var e=[],r=0;r<this.columns;r++)e.push(this.get(t,r));return e}},{key:"getRowVector",value:function(t){return D.rowVector(this.getRow(t))}},{key:"setRow",value:function(t,e){M(this,t),e=_(this,e);for(var r=0;r<this.columns;r++)this.set(t,r,e[r]);return this}},{key:"swapRows",value:function(t,e){M(this,t),M(this,e);for(var r=0;r<this.columns;r++){var n=this.get(t,r);this.set(t,r,this.get(e,r)),this.set(e,r,n)}return this}},{key:"getColumn",value:function(t){N(this,t);for(var e=[],r=0;r<this.rows;r++)e.push(this.get(r,t));return e}},{key:"getColumnVector",value:function(t){return D.columnVector(this.getColumn(t))}},{key:"setColumn",value:function(t,e){N(this,t),e=j(this,e);for(var r=0;r<this.rows;r++)this.set(r,t,e[r]);return this}},{key:"swapColumns",value:function(t,e){N(this,t),N(this,e);for(var r=0;r<this.rows;r++){var n=this.get(r,t);this.set(r,t,this.get(r,e)),this.set(r,e,n)}return this}},{key:"addRowVector",value:function(t){t=_(this,t);for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)+t[r]);return this}},{key:"subRowVector",value:function(t){t=_(this,t);for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)-t[r]);return this}},{key:"mulRowVector",value:function(t){t=_(this,t);for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)*t[r]);return this}},{key:"divRowVector",value:function(t){t=_(this,t);for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)/t[r]);return this}},{key:"addColumnVector",value:function(t){t=j(this,t);for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)+t[e]);return this}},{key:"subColumnVector",value:function(t){t=j(this,t);for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)-t[e]);return this}},{key:"mulColumnVector",value:function(t){t=j(this,t);for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)*t[e]);return this}},{key:"divColumnVector",value:function(t){t=j(this,t);for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)/t[e]);return this}},{key:"mulRow",value:function(t,e){M(this,t);for(var r=0;r<this.columns;r++)this.set(t,r,this.get(t,r)*e);return this}},{key:"mulColumn",value:function(t,e){N(this,t);for(var r=0;r<this.rows;r++)this.set(r,t,this.get(r,t)*e);return this}},{key:"max",value:function(t){if(this.isEmpty())return NaN;switch(t){case"row":for(var e=new Array(this.rows).fill(Number.NEGATIVE_INFINITY),r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.get(r,n)>e[r]&&(e[r]=this.get(r,n));return e;case"column":for(var o=new Array(this.columns).fill(Number.NEGATIVE_INFINITY),i=0;i<this.rows;i++)for(var a=0;a<this.columns;a++)this.get(i,a)>o[a]&&(o[a]=this.get(i,a));return o;case void 0:for(var s=this.get(0,0),u=0;u<this.rows;u++)for(var c=0;c<this.columns;c++)this.get(u,c)>s&&(s=this.get(u,c));return s;default:throw new Error("invalid option: ".concat(t))}}},{key:"maxIndex",value:function(){I(this);for(var t=this.get(0,0),e=[0,0],r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.get(r,n)>t&&(t=this.get(r,n),e[0]=r,e[1]=n);return e}},{key:"min",value:function(t){if(this.isEmpty())return NaN;switch(t){case"row":for(var e=new Array(this.rows).fill(Number.POSITIVE_INFINITY),r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.get(r,n)<e[r]&&(e[r]=this.get(r,n));return e;case"column":for(var o=new Array(this.columns).fill(Number.POSITIVE_INFINITY),i=0;i<this.rows;i++)for(var a=0;a<this.columns;a++)this.get(i,a)<o[a]&&(o[a]=this.get(i,a));return o;case void 0:for(var s=this.get(0,0),u=0;u<this.rows;u++)for(var c=0;c<this.columns;c++)this.get(u,c)<s&&(s=this.get(u,c));return s;default:throw new Error("invalid option: ".concat(t))}}},{key:"minIndex",value:function(){I(this);for(var t=this.get(0,0),e=[0,0],r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.get(r,n)<t&&(t=this.get(r,n),e[0]=r,e[1]=n);return e}},{key:"maxRow",value:function(t){if(M(this,t),this.isEmpty())return NaN;for(var e=this.get(t,0),r=1;r<this.columns;r++)this.get(t,r)>e&&(e=this.get(t,r));return e}},{key:"maxRowIndex",value:function(t){M(this,t),I(this);for(var e=this.get(t,0),r=[t,0],n=1;n<this.columns;n++)this.get(t,n)>e&&(e=this.get(t,n),r[1]=n);return r}},{key:"minRow",value:function(t){if(M(this,t),this.isEmpty())return NaN;for(var e=this.get(t,0),r=1;r<this.columns;r++)this.get(t,r)<e&&(e=this.get(t,r));return e}},{key:"minRowIndex",value:function(t){M(this,t),I(this);for(var e=this.get(t,0),r=[t,0],n=1;n<this.columns;n++)this.get(t,n)<e&&(e=this.get(t,n),r[1]=n);return r}},{key:"maxColumn",value:function(t){if(N(this,t),this.isEmpty())return NaN;for(var e=this.get(0,t),r=1;r<this.rows;r++)this.get(r,t)>e&&(e=this.get(r,t));return e}},{key:"maxColumnIndex",value:function(t){N(this,t),I(this);for(var e=this.get(0,t),r=[0,t],n=1;n<this.rows;n++)this.get(n,t)>e&&(e=this.get(n,t),r[0]=n);return r}},{key:"minColumn",value:function(t){if(N(this,t),this.isEmpty())return NaN;for(var e=this.get(0,t),r=1;r<this.rows;r++)this.get(r,t)<e&&(e=this.get(r,t));return e}},{key:"minColumnIndex",value:function(t){N(this,t),I(this);for(var e=this.get(0,t),r=[0,t],n=1;n<this.rows;n++)this.get(n,t)<e&&(e=this.get(n,t),r[0]=n);return r}},{key:"diag",value:function(){for(var t=Math.min(this.rows,this.columns),e=[],r=0;r<t;r++)e.push(this.get(r,r));return e}},{key:"norm",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"frobenius";switch(t){case"max":return this.max();case"frobenius":return Math.sqrt(this.dot(this));default:throw new RangeError("unknown norm type: ".concat(t))}}},{key:"cumulativeSum",value:function(){for(var t=0,e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)t+=this.get(e,r),this.set(e,r,t);return this}},{key:"dot",value:function(e){t.isMatrix(e)&&(e=e.to1DArray());var r=this.to1DArray();if(r.length!==e.length)throw new RangeError("vectors do not have the same size");for(var n=0,o=0;o<r.length;o++)n+=r[o]*e[o];return n}},{key:"mmul",value:function(t){t=D.checkMatrix(t);for(var e=this.rows,r=this.columns,n=t.columns,o=new D(e,n),i=new Float64Array(r),a=0;a<n;a++){for(var s=0;s<r;s++)i[s]=t.get(s,a);for(var u=0;u<e;u++){for(var c=0,h=0;h<r;h++)c+=this.get(u,h)*i[h];o.set(u,a,c)}}return o}},{key:"mpow",value:function(t){if(!this.isSquare())throw new RangeError("Matrix must be square");if(!Number.isInteger(t)||t<0)throw new RangeError("Exponent must be a non-negative integer");for(var e=D.eye(this.rows),r=this,n=t;n>=1;n/=2)1&n&&(e=e.mmul(r)),r=r.mmul(r);return e}},{key:"strassen2x2",value:function(t){t=D.checkMatrix(t);var e=new D(2,2),r=this.get(0,0),n=t.get(0,0),o=this.get(0,1),i=t.get(0,1),a=this.get(1,0),s=t.get(1,0),u=this.get(1,1),c=t.get(1,1),h=(r+u)*(n+c),f=(a+u)*n,d=r*(i-c),l=u*(s-n),v=(r+o)*c,g=h+l-v+(o-u)*(s+c),p=d+v,y=f+l,m=h-f+d+(a-r)*(n+i);return e.set(0,0,g),e.set(0,1,p),e.set(1,0,y),e.set(1,1,m),e}},{key:"strassen3x3",value:function(t){t=D.checkMatrix(t);var e=new D(3,3),r=this.get(0,0),n=this.get(0,1),o=this.get(0,2),i=this.get(1,0),a=this.get(1,1),s=this.get(1,2),u=this.get(2,0),c=this.get(2,1),h=this.get(2,2),f=t.get(0,0),d=t.get(0,1),l=t.get(0,2),v=t.get(1,0),g=t.get(1,1),p=t.get(1,2),y=t.get(2,0),m=t.get(2,1),w=t.get(2,2),x=(r-i)*(-d+g),b=(-r+i+a)*(f-d+g),E=(i+a)*(-f+d),k=r*f,M=(-r+u+c)*(f-l+p),N=(-r+u)*(l-p),_=(u+c)*(-f+l),j=(-o+c+h)*(g+y-m),A=(o-h)*(g-m),O=o*y,S=(c+h)*(-y+m),z=(-o+a+s)*(p+y-w),R=(o-s)*(p-w),I=(a+s)*(-y+w),P=k+O+n*v,T=(r+n+o-i-a-c-h)*g+b+E+k+j+O+S,C=k+M+_+(r+n+o-a-s-u-c)*p+O+z+I,L=x+a*(-f+d+v-g-p-y+w)+b+k+O+z+R,F=x+b+E+k+s*m,q=O+z+R+I+i*l,V=k+M+N+c*(-f+l+v-g-p-y+m)+j+A+O,G=j+A+O+S+u*d,B=k+M+N+_+h*w;return e.set(0,0,P),e.set(0,1,T),e.set(0,2,C),e.set(1,0,L),e.set(1,1,F),e.set(1,2,q),e.set(2,0,V),e.set(2,1,G),e.set(2,2,B),e}},{key:"mmulStrassen",value:function(e){e=D.checkMatrix(e);var r=this.clone(),n=r.rows,o=r.columns,i=e.rows,a=e.columns;function s(e,r,n){var o=e.rows,i=e.columns;if(o===r&&i===n)return e;var a=t.zeros(r,n);return a.setSubMatrix(e,0,0)}o!==i&&console.warn("Multiplying ".concat(n," x ").concat(o," and ").concat(i," x ").concat(a," matrix: dimensions do not match."));var u=Math.max(n,i),c=Math.max(o,a);return function e(r,n,o,i){if(o<=512||i<=512)return r.mmul(n);o%2==1&&i%2==1?(r=s(r,o+1,i+1),n=s(n,o+1,i+1)):o%2==1?(r=s(r,o+1,i),n=s(n,o+1,i)):i%2==1&&(r=s(r,o,i+1),n=s(n,o,i+1));var a=parseInt(r.rows/2,10),u=parseInt(r.columns/2,10),c=r.subMatrix(0,a-1,0,u-1),h=n.subMatrix(0,a-1,0,u-1),f=r.subMatrix(0,a-1,u,r.columns-1),d=n.subMatrix(0,a-1,u,n.columns-1),l=r.subMatrix(a,r.rows-1,0,u-1),v=n.subMatrix(a,n.rows-1,0,u-1),g=r.subMatrix(a,r.rows-1,u,r.columns-1),p=n.subMatrix(a,n.rows-1,u,n.columns-1),y=e(t.add(c,g),t.add(h,p),a,u),m=e(t.add(l,g),h,a,u),w=e(c,t.sub(d,p),a,u),x=e(g,t.sub(v,h),a,u),b=e(t.add(c,f),p,a,u),E=e(t.sub(l,c),t.add(h,d),a,u),k=e(t.sub(f,g),t.add(v,p),a,u),M=t.add(y,x);M.sub(b),M.add(k);var N=t.add(w,b),_=t.add(m,x),j=t.sub(y,m);j.add(w),j.add(E);var A=t.zeros(2*M.rows,2*M.columns);return(A=(A=(A=(A=A.setSubMatrix(M,0,0)).setSubMatrix(N,M.rows,0)).setSubMatrix(_,0,M.columns)).setSubMatrix(j,M.rows,M.columns)).subMatrix(0,o-1,0,i-1)}(r=s(r,u,c),e=s(e,u,c),u,c)}},{key:"scaleRows",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("object"!=typeof t)throw new TypeError("options must be an object");var e=t.min,r=void 0===e?0:e,n=t.max,o=void 0===n?1:n;if(!Number.isFinite(r))throw new TypeError("min must be a number");if(!Number.isFinite(o))throw new TypeError("max must be a number");if(r>=o)throw new RangeError("min must be smaller than max");for(var i=new D(this.rows,this.columns),a=0;a<this.rows;a++){var s=this.getRow(a);s.length>0&&m(s,{min:r,max:o,output:s}),i.setRow(a,s)}return i}},{key:"scaleColumns",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("object"!=typeof t)throw new TypeError("options must be an object");var e=t.min,r=void 0===e?0:e,n=t.max,o=void 0===n?1:n;if(!Number.isFinite(r))throw new TypeError("min must be a number");if(!Number.isFinite(o))throw new TypeError("max must be a number");if(r>=o)throw new RangeError("min must be smaller than max");for(var i=new D(this.rows,this.columns),a=0;a<this.columns;a++){var s=this.getColumn(a);s.length&&m(s,{min:r,max:o,output:s}),i.setColumn(a,s)}return i}},{key:"flipRows",value:function(){for(var t=Math.ceil(this.columns/2),e=0;e<this.rows;e++)for(var r=0;r<t;r++){var n=this.get(e,r),o=this.get(e,this.columns-1-r);this.set(e,r,o),this.set(e,this.columns-1-r,n)}return this}},{key:"flipColumns",value:function(){for(var t=Math.ceil(this.rows/2),e=0;e<this.columns;e++)for(var r=0;r<t;r++){var n=this.get(r,e),o=this.get(this.rows-1-r,e);this.set(r,e,o),this.set(this.rows-1-r,e,n)}return this}},{key:"kroneckerProduct",value:function(t){t=D.checkMatrix(t);for(var e=this.rows,r=this.columns,n=t.rows,o=t.columns,i=new D(e*n,r*o),a=0;a<e;a++)for(var s=0;s<r;s++)for(var u=0;u<n;u++)for(var c=0;c<o;c++)i.set(n*a+u,o*s+c,this.get(a,s)*t.get(u,c));return i}},{key:"kroneckerSum",value:function(t){if(t=D.checkMatrix(t),!this.isSquare()||!t.isSquare())throw new Error("Kronecker Sum needs two Square Matrices");var e=this.rows,r=t.rows,n=this.kroneckerProduct(D.eye(r,r)),o=D.eye(e,e).kroneckerProduct(t);return n.add(o)}},{key:"transpose",value:function(){for(var t=new D(this.columns,this.rows),e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)t.set(r,e,this.get(e,r));return t}},{key:"sortRows",value:function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:T,e=0;e<this.rows;e++)this.setRow(e,this.getRow(e).sort(t));return this}},{key:"sortColumns",value:function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:T,e=0;e<this.columns;e++)this.setColumn(e,this.getColumn(e).sort(t));return this}},{key:"subMatrix",value:function(t,e,r,n){S(this,t,e,r,n);for(var o=new D(e-t+1,n-r+1),i=t;i<=e;i++)for(var a=r;a<=n;a++)o.set(i-t,a-r,this.get(i,a));return o}},{key:"subMatrixRow",value:function(t,e,r){if(void 0===e&&(e=0),void 0===r&&(r=this.columns-1),e>r||e<0||e>=this.columns||r<0||r>=this.columns)throw new RangeError("Argument out of range");for(var n=new D(t.length,r-e+1),o=0;o<t.length;o++)for(var i=e;i<=r;i++){if(t[o]<0||t[o]>=this.rows)throw new RangeError("Row index out of range: ".concat(t[o]));n.set(o,i-e,this.get(t[o],i))}return n}},{key:"subMatrixColumn",value:function(t,e,r){if(void 0===e&&(e=0),void 0===r&&(r=this.rows-1),e>r||e<0||e>=this.rows||r<0||r>=this.rows)throw new RangeError("Argument out of range");for(var n=new D(r-e+1,t.length),o=0;o<t.length;o++)for(var i=e;i<=r;i++){if(t[o]<0||t[o]>=this.columns)throw new RangeError("Column index out of range: ".concat(t[o]));n.set(i-e,o,this.get(i,t[o]))}return n}},{key:"setSubMatrix",value:function(t,e,r){if((t=D.checkMatrix(t)).isEmpty())return this;S(this,e,e+t.rows-1,r,r+t.columns-1);for(var n=0;n<t.rows;n++)for(var o=0;o<t.columns;o++)this.set(e+n,r+o,t.get(n,o));return this}},{key:"selection",value:function(t,e){A(this,t),O(this,e);for(var r=new D(t.length,e.length),n=0;n<t.length;n++)for(var o=t[n],i=0;i<e.length;i++){var a=e[i];r.set(n,i,this.get(o,a))}return r}},{key:"trace",value:function(){for(var t=Math.min(this.rows,this.columns),e=0,r=0;r<t;r++)e+=this.get(r,r);return e}},{key:"clone",value:function(){return this.constructor.copy(this,new D(this.rows,this.columns))}},{key:"sum",value:function(t){switch(t){case"row":return function(t){for(var e=z(t.rows),r=0;r<t.rows;++r)for(var n=0;n<t.columns;++n)e[r]+=t.get(r,n);return e}(this);case"column":return function(t){for(var e=z(t.columns),r=0;r<t.rows;++r)for(var n=0;n<t.columns;++n)e[n]+=t.get(r,n);return e}(this);case void 0:return function(t){for(var e=0,r=0;r<t.rows;r++)for(var n=0;n<t.columns;n++)e+=t.get(r,n);return e}(this);default:throw new Error("invalid option: ".concat(t))}}},{key:"product",value:function(t){switch(t){case"row":return function(t){for(var e=z(t.rows,1),r=0;r<t.rows;++r)for(var n=0;n<t.columns;++n)e[r]*=t.get(r,n);return e}(this);case"column":return function(t){for(var e=z(t.columns,1),r=0;r<t.rows;++r)for(var n=0;n<t.columns;++n)e[n]*=t.get(r,n);return e}(this);case void 0:return function(t){for(var e=1,r=0;r<t.rows;r++)for(var n=0;n<t.columns;n++)e*=t.get(r,n);return e}(this);default:throw new Error("invalid option: ".concat(t))}}},{key:"mean",value:function(t){var e=this.sum(t);switch(t){case"row":for(var r=0;r<this.rows;r++)e[r]/=this.columns;return e;case"column":for(var n=0;n<this.columns;n++)e[n]/=this.rows;return e;case void 0:return e/this.size;default:throw new Error("invalid option: ".concat(t))}}},{key:"variance",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("object"==typeof t&&(e=t,t=void 0),"object"!=typeof e)throw new TypeError("options must be an object");var r=e,n=r.unbiased,o=void 0===n||n,i=r.mean,a=void 0===i?this.mean(t):i;if("boolean"!=typeof o)throw new TypeError("unbiased must be a boolean");switch(t){case"row":if(!y.isAnyArray(a))throw new TypeError("mean must be an array");return function(t,e,r){for(var n=t.rows,o=t.columns,i=[],a=0;a<n;a++){for(var s=0,u=0,c=0,h=0;h<o;h++)s+=c=t.get(a,h)-r[a],u+=c*c;e?i.push((u-s*s/o)/(o-1)):i.push((u-s*s/o)/o)}return i}(this,o,a);case"column":if(!y.isAnyArray(a))throw new TypeError("mean must be an array");return function(t,e,r){for(var n=t.rows,o=t.columns,i=[],a=0;a<o;a++){for(var s=0,u=0,c=0,h=0;h<n;h++)s+=c=t.get(h,a)-r[a],u+=c*c;e?i.push((u-s*s/n)/(n-1)):i.push((u-s*s/n)/n)}return i}(this,o,a);case void 0:if("number"!=typeof a)throw new TypeError("mean must be a number");return function(t,e,r){for(var n=t.rows,o=t.columns,i=n*o,a=0,s=0,u=0,c=0;c<n;c++)for(var h=0;h<o;h++)a+=u=t.get(c,h)-r,s+=u*u;return e?(s-a*a/i)/(i-1):(s-a*a/i)/i}(this,o,a);default:throw new Error("invalid option: ".concat(t))}}},{key:"standardDeviation",value:function(t,e){"object"==typeof t&&(e=t,t=void 0);var r=this.variance(t,e);if(void 0===t)return Math.sqrt(r);for(var n=0;n<r.length;n++)r[n]=Math.sqrt(r[n]);return r}},{key:"center",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("object"==typeof t&&(e=t,t=void 0),"object"!=typeof e)throw new TypeError("options must be an object");var r=e.center,n=void 0===r?this.mean(t):r;switch(t){case"row":if(!y.isAnyArray(n))throw new TypeError("center must be an array");return function(t,e){for(var r=0;r<t.rows;r++)for(var n=0;n<t.columns;n++)t.set(r,n,t.get(r,n)-e[r])}(this,n),this;case"column":if(!y.isAnyArray(n))throw new TypeError("center must be an array");return function(t,e){for(var r=0;r<t.rows;r++)for(var n=0;n<t.columns;n++)t.set(r,n,t.get(r,n)-e[n])}(this,n),this;case void 0:if("number"!=typeof n)throw new TypeError("center must be a number");return function(t,e){for(var r=0;r<t.rows;r++)for(var n=0;n<t.columns;n++)t.set(r,n,t.get(r,n)-e)}(this,n),this;default:throw new Error("invalid option: ".concat(t))}}},{key:"scale",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("object"==typeof t&&(e=t,t=void 0),"object"!=typeof e)throw new TypeError("options must be an object");var r=e.scale;switch(t){case"row":if(void 0===r)r=function(t){for(var e=[],r=0;r<t.rows;r++){for(var n=0,o=0;o<t.columns;o++)n+=Math.pow(t.get(r,o),2)/(t.columns-1);e.push(Math.sqrt(n))}return e}(this);else if(!y.isAnyArray(r))throw new TypeError("scale must be an array");return function(t,e){for(var r=0;r<t.rows;r++)for(var n=0;n<t.columns;n++)t.set(r,n,t.get(r,n)/e[r])}(this,r),this;case"column":if(void 0===r)r=function(t){for(var e=[],r=0;r<t.columns;r++){for(var n=0,o=0;o<t.rows;o++)n+=Math.pow(t.get(o,r),2)/(t.rows-1);e.push(Math.sqrt(n))}return e}(this);else if(!y.isAnyArray(r))throw new TypeError("scale must be an array");return function(t,e){for(var r=0;r<t.rows;r++)for(var n=0;n<t.columns;n++)t.set(r,n,t.get(r,n)/e[n])}(this,r),this;case void 0:if(void 0===r)r=function(t){for(var e=t.size-1,r=0,n=0;n<t.columns;n++)for(var o=0;o<t.rows;o++)r+=Math.pow(t.get(o,n),2)/e;return Math.sqrt(r)}(this);else if("number"!=typeof r)throw new TypeError("scale must be a number");return function(t,e){for(var r=0;r<t.rows;r++)for(var n=0;n<t.columns;n++)t.set(r,n,t.get(r,n)/e)}(this,r),this;default:throw new Error("invalid option: ".concat(t))}}},{key:"toString",value:function(t){return b(this,t)}},{key:Symbol.iterator,value:function(){return this.entries()}},{key:"entries",value:v().mark((function t(){var e,r;return v().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=0;case 1:if(!(e<this.rows)){t.next=12;break}r=0;case 3:if(!(r<this.columns)){t.next=9;break}return t.next=6,[e,r,this.get(e,r)];case 6:r++,t.next=3;break;case 9:e++,t.next=1;break;case 12:case"end":return t.stop()}}),t,this)}))},{key:"values",value:v().mark((function t(){var e,r;return v().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=0;case 1:if(!(e<this.rows)){t.next=12;break}r=0;case 3:if(!(r<this.columns)){t.next=9;break}return t.next=6,this.get(e,r);case 6:r++,t.next=3;break;case 9:e++,t.next=1;break;case 12:case"end":return t.stop()}}),t,this)}))}],[{key:"from1DArray",value:function(t,e,r){if(t*e!==r.length)throw new RangeError("data length does not match given dimensions");for(var n=new D(t,e),o=0;o<t;o++)for(var i=0;i<e;i++)n.set(o,i,r[o*e+i]);return n}},{key:"rowVector",value:function(t){for(var e=new D(1,t.length),r=0;r<t.length;r++)e.set(0,r,t[r]);return e}},{key:"columnVector",value:function(t){for(var e=new D(t.length,1),r=0;r<t.length;r++)e.set(r,0,t[r]);return e}},{key:"zeros",value:function(t,e){return new D(t,e)}},{key:"ones",value:function(t,e){return new D(t,e).fill(1)}},{key:"rand",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("object"!=typeof r)throw new TypeError("options must be an object");for(var n=r.random,o=void 0===n?Math.random:n,i=new D(t,e),a=0;a<t;a++)for(var s=0;s<e;s++)i.set(a,s,o());return i}},{key:"randInt",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("object"!=typeof r)throw new TypeError("options must be an object");var n=r.min,o=void 0===n?0:n,i=r.max,a=void 0===i?1e3:i,s=r.random,u=void 0===s?Math.random:s;if(!Number.isInteger(o))throw new TypeError("min must be an integer");if(!Number.isInteger(a))throw new TypeError("max must be an integer");if(o>=a)throw new RangeError("min must be smaller than max");for(var c=a-o,h=new D(t,e),f=0;f<t;f++)for(var d=0;d<e;d++){var l=o+Math.round(u()*c);h.set(f,d,l)}return h}},{key:"eye",value:function(t,e,r){void 0===e&&(e=t),void 0===r&&(r=1);for(var n=Math.min(t,e),o=this.zeros(t,e),i=0;i<n;i++)o.set(i,i,r);return o}},{key:"diag",value:function(t,e,r){var n=t.length;void 0===e&&(e=n),void 0===r&&(r=e);for(var o=Math.min(n,e,r),i=this.zeros(e,r),a=0;a<o;a++)i.set(a,a,t[a]);return i}},{key:"min",value:function(t,e){t=this.checkMatrix(t),e=this.checkMatrix(e);for(var r=t.rows,n=t.columns,o=new D(r,n),i=0;i<r;i++)for(var a=0;a<n;a++)o.set(i,a,Math.min(t.get(i,a),e.get(i,a)));return o}},{key:"max",value:function(t,e){t=this.checkMatrix(t),e=this.checkMatrix(e);for(var r=t.rows,n=t.columns,o=new this(r,n),i=0;i<r;i++)for(var a=0;a<n;a++)o.set(i,a,Math.max(t.get(i,a),e.get(i,a)));return o}},{key:"checkMatrix",value:function(e){return t.isMatrix(e)?e:new D(e)}},{key:"isMatrix",value:function(t){return null!=t&&"Matrix"===t.klass}},{key:"copy",value:function(t,e){var r,n=l(t.entries());try{for(n.s();!(r=n.n()).done;){var o=d(r.value,3),i=o[0],a=o[1],s=o[2];e.set(i,a,s)}}catch(t){n.e(t)}finally{n.f()}return e}}])}();function T(t,e){return t-e}P.prototype.klass="Matrix","undefined"!=typeof Symbol&&(P.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return b(this)}),P.random=P.rand,P.randomInt=P.randInt,P.diagonal=P.diag,P.prototype.diagonal=P.prototype.diag,P.identity=P.eye,P.prototype.negate=P.prototype.neg,P.prototype.tensorProduct=P.prototype.kroneckerProduct;var C=new WeakSet,D=function(t){function e(t,r){var n;if(g(this,e),n=s(this,e),c(n,C),h(n,"data",void 0),e.isMatrix(t))f(C,n,L).call(n,t.rows,t.columns),e.copy(t,n);else if(Number.isInteger(t)&&t>=0)f(C,n,L).call(n,t,r);else{if(!y.isAnyArray(t))throw new TypeError("First argument must be a positive number or an array");var o=t;if("number"!=typeof(r=(t=o.length)?o[0].length:0))throw new TypeError("Data must be a 2D array with at least one element");n.data=[];for(var i=0;i<t;i++){if(o[i].length!==r)throw new RangeError("Inconsistent array dimensions");if(!o[i].every((function(t){return"number"==typeof t})))throw new TypeError("Input data contains non-numeric values");n.data.push(Float64Array.from(o[i]))}n.rows=t,n.columns=r}return n}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.data[t][e]=r,this}},{key:"get",value:function(t,e){return this.data[t][e]}},{key:"removeRow",value:function(t){return M(this,t),this.data.splice(t,1),this.rows-=1,this}},{key:"addRow",value:function(t,e){return void 0===e&&(e=t,t=this.rows),M(this,t,!0),e=Float64Array.from(_(this,e)),this.data.splice(t,0,e),this.rows+=1,this}},{key:"removeColumn",value:function(t){N(this,t);for(var e=0;e<this.rows;e++){for(var r=new Float64Array(this.columns-1),n=0;n<t;n++)r[n]=this.data[e][n];for(var o=t+1;o<this.columns;o++)r[o-1]=this.data[e][o];this.data[e]=r}return this.columns-=1,this}},{key:"addColumn",value:function(t,e){void 0===e&&(e=t,t=this.columns),N(this,t,!0),e=j(this,e);for(var r=0;r<this.rows;r++){for(var n=new Float64Array(this.columns+1),o=0;o<t;o++)n[o]=this.data[r][o];for(n[o++]=e[r];o<this.columns+1;o++)n[o]=this.data[r][o-1];this.data[r]=n}return this.columns+=1,this}}])}(P);function L(t,e){if(this.data=[],!(Number.isInteger(e)&&e>=0))throw new TypeError("nColumns must be a positive integer");for(var r=0;r<t;r++)this.data.push(new Float64Array(e));this.rows=t,this.columns=e}!function(t,e){t.prototype.add=function(t){return"number"==typeof t?this.addS(t):this.addM(t)},t.prototype.addS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)+t);return this},t.prototype.addM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)+t.get(r,n));return this},t.add=function(t,r){return new e(t).add(r)},t.prototype.sub=function(t){return"number"==typeof t?this.subS(t):this.subM(t)},t.prototype.subS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)-t);return this},t.prototype.subM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)-t.get(r,n));return this},t.sub=function(t,r){return new e(t).sub(r)},t.prototype.subtract=t.prototype.sub,t.prototype.subtractS=t.prototype.subS,t.prototype.subtractM=t.prototype.subM,t.subtract=t.sub,t.prototype.mul=function(t){return"number"==typeof t?this.mulS(t):this.mulM(t)},t.prototype.mulS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)*t);return this},t.prototype.mulM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)*t.get(r,n));return this},t.mul=function(t,r){return new e(t).mul(r)},t.prototype.multiply=t.prototype.mul,t.prototype.multiplyS=t.prototype.mulS,t.prototype.multiplyM=t.prototype.mulM,t.multiply=t.mul,t.prototype.div=function(t){return"number"==typeof t?this.divS(t):this.divM(t)},t.prototype.divS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)/t);return this},t.prototype.divM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)/t.get(r,n));return this},t.div=function(t,r){return new e(t).div(r)},t.prototype.divide=t.prototype.div,t.prototype.divideS=t.prototype.divS,t.prototype.divideM=t.prototype.divM,t.divide=t.div,t.prototype.mod=function(t){return"number"==typeof t?this.modS(t):this.modM(t)},t.prototype.modS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)%t);return this},t.prototype.modM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)%t.get(r,n));return this},t.mod=function(t,r){return new e(t).mod(r)},t.prototype.modulus=t.prototype.mod,t.prototype.modulusS=t.prototype.modS,t.prototype.modulusM=t.prototype.modM,t.modulus=t.mod,t.prototype.and=function(t){return"number"==typeof t?this.andS(t):this.andM(t)},t.prototype.andS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)&t);return this},t.prototype.andM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)&t.get(r,n));return this},t.and=function(t,r){return new e(t).and(r)},t.prototype.or=function(t){return"number"==typeof t?this.orS(t):this.orM(t)},t.prototype.orS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)|t);return this},t.prototype.orM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)|t.get(r,n));return this},t.or=function(t,r){return new e(t).or(r)},t.prototype.xor=function(t){return"number"==typeof t?this.xorS(t):this.xorM(t)},t.prototype.xorS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)^t);return this},t.prototype.xorM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)^t.get(r,n));return this},t.xor=function(t,r){return new e(t).xor(r)},t.prototype.leftShift=function(t){return"number"==typeof t?this.leftShiftS(t):this.leftShiftM(t)},t.prototype.leftShiftS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)<<t);return this},t.prototype.leftShiftM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)<<t.get(r,n));return this},t.leftShift=function(t,r){return new e(t).leftShift(r)},t.prototype.signPropagatingRightShift=function(t){return"number"==typeof t?this.signPropagatingRightShiftS(t):this.signPropagatingRightShiftM(t)},t.prototype.signPropagatingRightShiftS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)>>t);return this},t.prototype.signPropagatingRightShiftM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)>>t.get(r,n));return this},t.signPropagatingRightShift=function(t,r){return new e(t).signPropagatingRightShift(r)},t.prototype.rightShift=function(t){return"number"==typeof t?this.rightShiftS(t):this.rightShiftM(t)},t.prototype.rightShiftS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)>>>t);return this},t.prototype.rightShiftM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,this.get(r,n)>>>t.get(r,n));return this},t.rightShift=function(t,r){return new e(t).rightShift(r)},t.prototype.zeroFillRightShift=t.prototype.rightShift,t.prototype.zeroFillRightShiftS=t.prototype.rightShiftS,t.prototype.zeroFillRightShiftM=t.prototype.rightShiftM,t.zeroFillRightShift=t.rightShift,t.prototype.not=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,~this.get(t,e));return this},t.not=function(t){return new e(t).not()},t.prototype.abs=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.abs(this.get(t,e)));return this},t.abs=function(t){return new e(t).abs()},t.prototype.acos=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.acos(this.get(t,e)));return this},t.acos=function(t){return new e(t).acos()},t.prototype.acosh=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.acosh(this.get(t,e)));return this},t.acosh=function(t){return new e(t).acosh()},t.prototype.asin=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.asin(this.get(t,e)));return this},t.asin=function(t){return new e(t).asin()},t.prototype.asinh=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.asinh(this.get(t,e)));return this},t.asinh=function(t){return new e(t).asinh()},t.prototype.atan=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.atan(this.get(t,e)));return this},t.atan=function(t){return new e(t).atan()},t.prototype.atanh=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.atanh(this.get(t,e)));return this},t.atanh=function(t){return new e(t).atanh()},t.prototype.cbrt=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.cbrt(this.get(t,e)));return this},t.cbrt=function(t){return new e(t).cbrt()},t.prototype.ceil=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.ceil(this.get(t,e)));return this},t.ceil=function(t){return new e(t).ceil()},t.prototype.clz32=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.clz32(this.get(t,e)));return this},t.clz32=function(t){return new e(t).clz32()},t.prototype.cos=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.cos(this.get(t,e)));return this},t.cos=function(t){return new e(t).cos()},t.prototype.cosh=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.cosh(this.get(t,e)));return this},t.cosh=function(t){return new e(t).cosh()},t.prototype.exp=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.exp(this.get(t,e)));return this},t.exp=function(t){return new e(t).exp()},t.prototype.expm1=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.expm1(this.get(t,e)));return this},t.expm1=function(t){return new e(t).expm1()},t.prototype.floor=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.floor(this.get(t,e)));return this},t.floor=function(t){return new e(t).floor()},t.prototype.fround=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.fround(this.get(t,e)));return this},t.fround=function(t){return new e(t).fround()},t.prototype.log=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.log(this.get(t,e)));return this},t.log=function(t){return new e(t).log()},t.prototype.log1p=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.log1p(this.get(t,e)));return this},t.log1p=function(t){return new e(t).log1p()},t.prototype.log10=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.log10(this.get(t,e)));return this},t.log10=function(t){return new e(t).log10()},t.prototype.log2=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.log2(this.get(t,e)));return this},t.log2=function(t){return new e(t).log2()},t.prototype.round=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.round(this.get(t,e)));return this},t.round=function(t){return new e(t).round()},t.prototype.sign=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.sign(this.get(t,e)));return this},t.sign=function(t){return new e(t).sign()},t.prototype.sin=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.sin(this.get(t,e)));return this},t.sin=function(t){return new e(t).sin()},t.prototype.sinh=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.sinh(this.get(t,e)));return this},t.sinh=function(t){return new e(t).sinh()},t.prototype.sqrt=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.sqrt(this.get(t,e)));return this},t.sqrt=function(t){return new e(t).sqrt()},t.prototype.tan=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.tan(this.get(t,e)));return this},t.tan=function(t){return new e(t).tan()},t.prototype.tanh=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.tanh(this.get(t,e)));return this},t.tanh=function(t){return new e(t).tanh()},t.prototype.trunc=function(){for(var t=0;t<this.rows;t++)for(var e=0;e<this.columns;e++)this.set(t,e,Math.trunc(this.get(t,e)));return this},t.trunc=function(t){return new e(t).trunc()},t.pow=function(t,r){return new e(t).pow(r)},t.prototype.pow=function(t){return"number"==typeof t?this.powS(t):this.powM(t)},t.prototype.powS=function(t){for(var e=0;e<this.rows;e++)for(var r=0;r<this.columns;r++)this.set(e,r,Math.pow(this.get(e,r),t));return this},t.prototype.powM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(var r=0;r<this.rows;r++)for(var n=0;n<this.columns;n++)this.set(r,n,Math.pow(this.get(r,n),t.get(r,n)));return this}}(P,D);var F=new WeakMap,q=function(t){function e(t){var r;if(g(this,e),r=s(this,e),o(r,F,void 0),D.isMatrix(t)){if(!t.isSymmetric())throw new TypeError("not symmetric data");i(F,r,D.copy(t,new D(t.rows,t.rows)))}else if(Number.isInteger(t)&&t>=0)i(F,r,new D(t,t));else if(i(F,r,new D(t)),!r.isSymmetric())throw new TypeError("not symmetric data");return r}return u(e,t),p(e,[{key:"size",get:function(){return a(F,this).size}},{key:"rows",get:function(){return a(F,this).rows}},{key:"columns",get:function(){return a(F,this).columns}},{key:"diagonalSize",get:function(){return this.rows}},{key:"clone",value:function(){var t,r=new e(this.diagonalSize),n=l(this.upperRightEntries());try{for(n.s();!(t=n.n()).done;){var o=d(t.value,3),i=o[0],a=o[1],s=o[2];r.set(i,a,s)}}catch(t){n.e(t)}finally{n.f()}return r}},{key:"toMatrix",value:function(){return new D(this)}},{key:"get",value:function(t,e){return a(F,this).get(t,e)}},{key:"set",value:function(t,e,r){return a(F,this).set(t,e,r),a(F,this).set(e,t,r),this}},{key:"removeCross",value:function(t){return a(F,this).removeRow(t),a(F,this).removeColumn(t),this}},{key:"addCross",value:function(t,e){void 0===e&&(e=t,t=this.diagonalSize);var r=e.slice();return r.splice(t,1),a(F,this).addRow(t,r),a(F,this).addColumn(t,e),this}},{key:"applyMask",value:function(t){if(t.length!==this.diagonalSize)throw new RangeError("Mask size do not match with matrix size");var e,r=[],n=l(t.entries());try{for(n.s();!(e=n.n()).done;){var o=d(e.value,2),i=o[0];o[1]||r.push(i)}}catch(t){n.e(t)}finally{n.f()}r.reverse();for(var a=0,s=r;a<s.length;a++){var u=s[a];this.removeCross(u)}return this}},{key:"toCompact",value:function(){for(var t=this.diagonalSize,e=new Array(t*(t+1)/2),r=0,n=0,o=0;o<e.length;o++)e[o]=this.get(n,r),++r>=t&&(r=++n);return e}},{key:"upperRightEntries",value:v().mark((function t(){var e,r,n;return v().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=0,r=0;case 1:if(!(e<this.diagonalSize)){t.next=9;break}return n=this.get(e,r),t.next=5,[e,r,n];case 5:++r>=this.diagonalSize&&(r=++e);case 6:t.next=1;break;case 9:case"end":return t.stop()}}),t,this)}))},{key:"upperRightValues",value:v().mark((function t(){var e,r,n;return v().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=0,r=0;case 1:if(!(e<this.diagonalSize)){t.next=9;break}return n=this.get(e,r),t.next=5,n;case 5:++r>=this.diagonalSize&&(r=++e);case 6:t.next=1;break;case 9:case"end":return t.stop()}}),t,this)}))}],[{key:"isSymmetricMatrix",value:function(t){return D.isMatrix(t)&&"SymmetricMatrix"===t.klassType}},{key:"zeros",value:function(t){return new this(t)}},{key:"ones",value:function(t){return new this(t).fill(1)}},{key:"fromCompact",value:function(t){var r=t.length,n=(Math.sqrt(8*r+1)-1)/2;if(!Number.isInteger(n))throw new TypeError("This array is not a compact representation of a Symmetric Matrix, ".concat(JSON.stringify(t)));for(var o=new e(n),i=0,a=0,s=0;s<r;s++)o.set(i,a,t[s]),++i>=n&&(i=++a);return o}}])}(P);q.prototype.klassType="SymmetricMatrix";var V=function(t){function e(t){var r;if(g(this,e),!(r=s(this,e,[t])).isDistance())throw new TypeError("Provided arguments do no produce a distance matrix");return r}return u(e,t),p(e,[{key:"set",value:function(t,r,o){return t===r&&(o=0),n(e,"set",this,3)([t,r,o])}},{key:"addCross",value:function(t,r){return void 0===r&&(r=t,t=this.diagonalSize),(r=r.slice())[t]=0,n(e,"addCross",this,3)([t,r])}},{key:"toSymmetricMatrix",value:function(){return new q(this)}},{key:"clone",value:function(){var t,r=new e(this.diagonalSize),n=l(this.upperRightEntries());try{for(n.s();!(t=n.n()).done;){var o=d(t.value,3),i=o[0],a=o[1],s=o[2];i!==a&&r.set(i,a,s)}}catch(t){n.e(t)}finally{n.f()}return r}},{key:"toCompact",value:function(){for(var t=this.diagonalSize,e=new Array((t-1)*t/2),r=1,n=0,o=0;o<e.length;o++)e[o]=this.get(n,r),++r>=t&&(r=1+ ++n);return e}}],[{key:"isDistanceMatrix",value:function(t){return q.isSymmetricMatrix(t)&&"DistanceMatrix"===t.klassSubType}},{key:"fromCompact",value:function(t){var e=t.length;if(0===e)return new this(0);var r=(Math.sqrt(8*e+1)+1)/2;if(!Number.isInteger(r))throw new TypeError("This array is not a compact representation of a DistanceMatrix, ".concat(JSON.stringify(t)));for(var n=new this(r),o=1,i=0,a=0;a<e;a++)n.set(o,i,t[a]),++o>=r&&(o=1+ ++i);return n}}])}(q);V.prototype.klassSubType="DistanceMatrix";var G=function(t){function e(t,r,n){var o;return g(this,e),(o=s(this,e)).matrix=t,o.rows=r,o.columns=n,o}return u(e,t),p(e)}(P),B=function(t){function e(t,r){var n;return g(this,e),N(t,r),(n=s(this,e,[t,t.rows,1])).column=r,n}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(t,this.column,r),this}},{key:"get",value:function(t){return this.matrix.get(t,this.column)}}])}(G),U=function(t){function e(t,r){var n;return g(this,e),O(t,r),(n=s(this,e,[t,t.rows,r.length])).columnIndices=r,n}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(t,this.columnIndices[e],r),this}},{key:"get",value:function(t,e){return this.matrix.get(t,this.columnIndices[e])}}])}(G),W=function(t){function e(t){return g(this,e),s(this,e,[t,t.rows,t.columns])}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(t,this.columns-e-1,r),this}},{key:"get",value:function(t,e){return this.matrix.get(t,this.columns-e-1)}}])}(G),Y=function(t){function e(t){return g(this,e),s(this,e,[t,t.rows,t.columns])}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(this.rows-t-1,e,r),this}},{key:"get",value:function(t,e){return this.matrix.get(this.rows-t-1,e)}}])}(G),H=function(t){function e(t,r){var n;return g(this,e),M(t,r),(n=s(this,e,[t,1,t.columns])).row=r,n}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(this.row,e,r),this}},{key:"get",value:function(t,e){return this.matrix.get(this.row,e)}}])}(G),K=function(t){function e(t,r){var n;return g(this,e),A(t,r),(n=s(this,e,[t,r.length,t.columns])).rowIndices=r,n}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(this.rowIndices[t],e,r),this}},{key:"get",value:function(t,e){return this.matrix.get(this.rowIndices[t],e)}}])}(G),$=function(t){function e(t,r,n){var o;return g(this,e),A(t,r),O(t,n),(o=s(this,e,[t,r.length,n.length])).rowIndices=r,o.columnIndices=n,o}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(this.rowIndices[t],this.columnIndices[e],r),this}},{key:"get",value:function(t,e){return this.matrix.get(this.rowIndices[t],this.columnIndices[e])}}])}(G),J=function(t){function e(t,r,n,o,i){var a;return g(this,e),S(t,r,n,o,i),(a=s(this,e,[t,n-r+1,i-o+1])).startRow=r,a.startColumn=o,a}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(this.startRow+t,this.startColumn+e,r),this}},{key:"get",value:function(t,e){return this.matrix.get(this.startRow+t,this.startColumn+e)}}])}(G),Q=function(t){function e(t){return g(this,e),s(this,e,[t,t.columns,t.rows])}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.matrix.set(e,t,r),this}},{key:"get",value:function(t,e){return this.matrix.get(e,t)}}])}(G),X=function(t){function e(t){var r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};g(this,e);var o=n.rows,i=void 0===o?1:o;if(t.length%i!==0)throw new Error("the data length is not divisible by the number of rows");return(r=s(this,e)).rows=i,r.columns=t.length/i,r.data=t,r}return u(e,t),p(e,[{key:"set",value:function(t,e,r){var n=this._calculateIndex(t,e);return this.data[n]=r,this}},{key:"get",value:function(t,e){var r=this._calculateIndex(t,e);return this.data[r]}},{key:"_calculateIndex",value:function(t,e){return t*this.columns+e}}])}(P),Z=function(t){function e(t){var r;return g(this,e),(r=s(this,e)).data=t,r.rows=t.length,r.columns=t[0].length,r}return u(e,t),p(e,[{key:"set",value:function(t,e,r){return this.data[t][e]=r,this}},{key:"get",value:function(t,e){return this.data[t][e]}}])}(P),tt=function(){return p((function t(e){g(this,t);var r,n,o,i,a,s,u,c,h,f=(e=Z.checkMatrix(e)).clone(),d=f.rows,l=f.columns,v=new Float64Array(d),p=1;for(r=0;r<d;r++)v[r]=r;for(c=new Float64Array(d),n=0;n<l;n++){for(r=0;r<d;r++)c[r]=f.get(r,n);for(r=0;r<d;r++){for(h=Math.min(r,n),a=0,o=0;o<h;o++)a+=f.get(r,o)*c[o];c[r]-=a,f.set(r,n,c[r])}for(i=n,r=n+1;r<d;r++)Math.abs(c[r])>Math.abs(c[i])&&(i=r);if(i!==n){for(o=0;o<l;o++)s=f.get(i,o),f.set(i,o,f.get(n,o)),f.set(n,o,s);u=v[i],v[i]=v[n],v[n]=u,p=-p}if(n<d&&0!==f.get(n,n))for(r=n+1;r<d;r++)f.set(r,n,f.get(r,n)/f.get(n,n))}this.LU=f,this.pivotVector=v,this.pivotSign=p}),[{key:"isSingular",value:function(){for(var t=this.LU,e=t.columns,r=0;r<e;r++)if(0===t.get(r,r))return!0;return!1}},{key:"solve",value:function(t){t=D.checkMatrix(t);var e=this.LU;if(e.rows!==t.rows)throw new Error("Invalid matrix dimensions");if(this.isSingular())throw new Error("LU matrix is singular");var r,n,o,i=t.columns,a=t.subMatrixRow(this.pivotVector,0,i-1),s=e.columns;for(o=0;o<s;o++)for(r=o+1;r<s;r++)for(n=0;n<i;n++)a.set(r,n,a.get(r,n)-a.get(o,n)*e.get(r,o));for(o=s-1;o>=0;o--){for(n=0;n<i;n++)a.set(o,n,a.get(o,n)/e.get(o,o));for(r=0;r<o;r++)for(n=0;n<i;n++)a.set(r,n,a.get(r,n)-a.get(o,n)*e.get(r,o))}return a}},{key:"determinant",get:function(){var t=this.LU;if(!t.isSquare())throw new Error("Matrix must be square");for(var e=this.pivotSign,r=t.columns,n=0;n<r;n++)e*=t.get(n,n);return e}},{key:"lowerTriangularMatrix",get:function(){for(var t=this.LU,e=t.rows,r=t.columns,n=new D(e,r),o=0;o<e;o++)for(var i=0;i<r;i++)o>i?n.set(o,i,t.get(o,i)):o===i?n.set(o,i,1):n.set(o,i,0);return n}},{key:"upperTriangularMatrix",get:function(){for(var t=this.LU,e=t.rows,r=t.columns,n=new D(e,r),o=0;o<e;o++)for(var i=0;i<r;i++)o<=i?n.set(o,i,t.get(o,i)):n.set(o,i,0);return n}},{key:"pivotPermutationVector",get:function(){return Array.from(this.pivotVector)}}])}();function et(t,e){var r=0;return Math.abs(t)>Math.abs(e)?(r=e/t,Math.abs(t)*Math.sqrt(1+r*r)):0!==e?(r=t/e,Math.abs(e)*Math.sqrt(1+r*r)):0}var rt=function(){return p((function t(e){g(this,t);var r,n,o,i,a=(e=Z.checkMatrix(e)).clone(),s=e.rows,u=e.columns,c=new Float64Array(u);for(o=0;o<u;o++){var h=0;for(r=o;r<s;r++)h=et(h,a.get(r,o));if(0!==h){for(a.get(o,o)<0&&(h=-h),r=o;r<s;r++)a.set(r,o,a.get(r,o)/h);for(a.set(o,o,a.get(o,o)+1),n=o+1;n<u;n++){for(i=0,r=o;r<s;r++)i+=a.get(r,o)*a.get(r,n);for(i=-i/a.get(o,o),r=o;r<s;r++)a.set(r,n,a.get(r,n)+i*a.get(r,o))}}c[o]=-h}this.QR=a,this.Rdiag=c}),[{key:"solve",value:function(t){t=D.checkMatrix(t);var e=this.QR,r=e.rows;if(t.rows!==r)throw new Error("Matrix row dimensions must agree");if(!this.isFullRank())throw new Error("Matrix is rank deficient");var n,o,i,a,s=t.columns,u=t.clone(),c=e.columns;for(i=0;i<c;i++)for(o=0;o<s;o++){for(a=0,n=i;n<r;n++)a+=e.get(n,i)*u.get(n,o);for(a=-a/e.get(i,i),n=i;n<r;n++)u.set(n,o,u.get(n,o)+a*e.get(n,i))}for(i=c-1;i>=0;i--){for(o=0;o<s;o++)u.set(i,o,u.get(i,o)/this.Rdiag[i]);for(n=0;n<i;n++)for(o=0;o<s;o++)u.set(n,o,u.get(n,o)-u.get(i,o)*e.get(n,i))}return u.subMatrix(0,c-1,0,s-1)}},{key:"isFullRank",value:function(){for(var t=this.QR.columns,e=0;e<t;e++)if(0===this.Rdiag[e])return!1;return!0}},{key:"upperTriangularMatrix",get:function(){var t,e,r=this.QR,n=r.columns,o=new D(n,n);for(t=0;t<n;t++)for(e=0;e<n;e++)t<e?o.set(t,e,r.get(t,e)):t===e?o.set(t,e,this.Rdiag[t]):o.set(t,e,0);return o}},{key:"orthogonalMatrix",get:function(){var t,e,r,n,o=this.QR,i=o.rows,a=o.columns,s=new D(i,a);for(r=a-1;r>=0;r--){for(t=0;t<i;t++)s.set(t,r,0);for(s.set(r,r,1),e=r;e<a;e++)if(0!==o.get(r,r)){for(n=0,t=r;t<i;t++)n+=o.get(t,r)*s.get(t,e);for(n=-n/o.get(r,r),t=r;t<i;t++)s.set(t,e,s.get(t,e)+n*o.get(t,r))}}return s}}])}(),nt=function(){return p((function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(g(this,t),(e=Z.checkMatrix(e)).isEmpty())throw new Error("Matrix must be non-empty");var n,o=e.rows,i=e.columns,a=r.computeLeftSingularVectors,s=void 0===a||a,u=r.computeRightSingularVectors,c=void 0===u||u,h=r.autoTranspose,f=void 0!==h&&h,d=Boolean(s),l=Boolean(c),v=!1;if(o<i)if(f){o=(n=e.transpose()).rows,i=n.columns,v=!0;var p=d;d=l,l=p}else n=e.clone(),console.warn("Computing SVD on a matrix with more columns than rows. Consider enabling autoTranspose");else n=e.clone();for(var y=Math.min(o,i),m=Math.min(o+1,i),w=new Float64Array(m),x=new D(o,y),b=new D(i,i),E=new Float64Array(i),k=new Float64Array(o),M=new Float64Array(m),N=0;N<m;N++)M[N]=N;for(var _=Math.min(o-1,i),j=Math.max(0,Math.min(i-2,o)),A=Math.max(_,j),O=0;O<A;O++){if(O<_){w[O]=0;for(var S=O;S<o;S++)w[O]=et(w[O],n.get(S,O));if(0!==w[O]){n.get(O,O)<0&&(w[O]=-w[O]);for(var z=O;z<o;z++)n.set(z,O,n.get(z,O)/w[O]);n.set(O,O,n.get(O,O)+1)}w[O]=-w[O]}for(var R=O+1;R<i;R++){if(O<_&&0!==w[O]){for(var I=0,P=O;P<o;P++)I+=n.get(P,O)*n.get(P,R);I=-I/n.get(O,O);for(var T=O;T<o;T++)n.set(T,R,n.get(T,R)+I*n.get(T,O))}E[R]=n.get(O,R)}if(d&&O<_)for(var C=O;C<o;C++)x.set(C,O,n.get(C,O));if(O<j){E[O]=0;for(var L=O+1;L<i;L++)E[O]=et(E[O],E[L]);if(0!==E[O]){E[O+1]<0&&(E[O]=0-E[O]);for(var F=O+1;F<i;F++)E[F]/=E[O];E[O+1]+=1}if(E[O]=-E[O],O+1<o&&0!==E[O]){for(var q=O+1;q<o;q++)k[q]=0;for(var V=O+1;V<o;V++)for(var G=O+1;G<i;G++)k[V]+=E[G]*n.get(V,G);for(var B=O+1;B<i;B++)for(var U=-E[B]/E[O+1],W=O+1;W<o;W++)n.set(W,B,n.get(W,B)+U*k[W])}if(l)for(var Y=O+1;Y<i;Y++)b.set(Y,O,E[Y])}}var H=Math.min(i,o+1);if(_<i&&(w[_]=n.get(_,_)),o<H&&(w[H-1]=0),j+1<H&&(E[j]=n.get(j,H-1)),E[H-1]=0,d){for(var K=_;K<y;K++){for(var $=0;$<o;$++)x.set($,K,0);x.set(K,K,1)}for(var J=_-1;J>=0;J--)if(0!==w[J]){for(var Q=J+1;Q<y;Q++){for(var X=0,tt=J;tt<o;tt++)X+=x.get(tt,J)*x.get(tt,Q);X=-X/x.get(J,J);for(var rt=J;rt<o;rt++)x.set(rt,Q,x.get(rt,Q)+X*x.get(rt,J))}for(var nt=J;nt<o;nt++)x.set(nt,J,-x.get(nt,J));x.set(J,J,1+x.get(J,J));for(var ot=0;ot<J-1;ot++)x.set(ot,J,0)}else{for(var it=0;it<o;it++)x.set(it,J,0);x.set(J,J,1)}}if(l)for(var at=i-1;at>=0;at--){if(at<j&&0!==E[at])for(var st=at+1;st<i;st++){for(var ut=0,ct=at+1;ct<i;ct++)ut+=b.get(ct,at)*b.get(ct,st);ut=-ut/b.get(at+1,at);for(var ht=at+1;ht<i;ht++)b.set(ht,st,b.get(ht,st)+ut*b.get(ht,at))}for(var ft=0;ft<i;ft++)b.set(ft,at,0);b.set(at,at,1)}for(var dt=H-1,lt=Number.EPSILON;H>0;){var vt=void 0,gt=void 0;for(vt=H-2;vt>=-1&&-1!==vt;vt--){var pt=Number.MIN_VALUE+lt*Math.abs(w[vt]+Math.abs(w[vt+1]));if(Math.abs(E[vt])<=pt||Number.isNaN(E[vt])){E[vt]=0;break}}if(vt===H-2)gt=4;else{var yt=void 0;for(yt=H-1;yt>=vt&&yt!==vt;yt--){var mt=(yt!==H?Math.abs(E[yt]):0)+(yt!==vt+1?Math.abs(E[yt-1]):0);if(Math.abs(w[yt])<=lt*mt){w[yt]=0;break}}yt===vt?gt=3:yt===H-1?gt=1:(gt=2,vt=yt)}switch(vt++,gt){case 1:var wt=E[H-2];E[H-2]=0;for(var xt=H-2;xt>=vt;xt--){var bt=et(w[xt],wt),Et=w[xt]/bt,kt=wt/bt;if(w[xt]=bt,xt!==vt&&(wt=-kt*E[xt-1],E[xt-1]=Et*E[xt-1]),l)for(var Mt=0;Mt<i;Mt++)bt=Et*b.get(Mt,xt)+kt*b.get(Mt,H-1),b.set(Mt,H-1,-kt*b.get(Mt,xt)+Et*b.get(Mt,H-1)),b.set(Mt,xt,bt)}break;case 2:var Nt=E[vt-1];E[vt-1]=0;for(var _t=vt;_t<H;_t++){var jt=et(w[_t],Nt),At=w[_t]/jt,Ot=Nt/jt;if(w[_t]=jt,Nt=-Ot*E[_t],E[_t]=At*E[_t],d)for(var St=0;St<o;St++)jt=At*x.get(St,_t)+Ot*x.get(St,vt-1),x.set(St,vt-1,-Ot*x.get(St,_t)+At*x.get(St,vt-1)),x.set(St,_t,jt)}break;case 3:var zt=Math.max(Math.abs(w[H-1]),Math.abs(w[H-2]),Math.abs(E[H-2]),Math.abs(w[vt]),Math.abs(E[vt])),Rt=w[H-1]/zt,It=w[H-2]/zt,Pt=E[H-2]/zt,Tt=w[vt]/zt,Ct=E[vt]/zt,Dt=((It+Rt)*(It-Rt)+Pt*Pt)/2,Lt=Rt*Pt*(Rt*Pt),Ft=0;0===Dt&&0===Lt||(Ft=Lt/(Dt+(Ft=Dt<0?0-Math.sqrt(Dt*Dt+Lt):Math.sqrt(Dt*Dt+Lt))));for(var qt=(Tt+Rt)*(Tt-Rt)+Ft,Vt=Tt*Ct,Gt=vt;Gt<H-1;Gt++){var Bt=et(qt,Vt);0===Bt&&(Bt=Number.MIN_VALUE);var Ut=qt/Bt,Wt=Vt/Bt;if(Gt!==vt&&(E[Gt-1]=Bt),qt=Ut*w[Gt]+Wt*E[Gt],E[Gt]=Ut*E[Gt]-Wt*w[Gt],Vt=Wt*w[Gt+1],w[Gt+1]=Ut*w[Gt+1],l)for(var Yt=0;Yt<i;Yt++)Bt=Ut*b.get(Yt,Gt)+Wt*b.get(Yt,Gt+1),b.set(Yt,Gt+1,-Wt*b.get(Yt,Gt)+Ut*b.get(Yt,Gt+1)),b.set(Yt,Gt,Bt);if(0===(Bt=et(qt,Vt))&&(Bt=Number.MIN_VALUE),Ut=qt/Bt,Wt=Vt/Bt,w[Gt]=Bt,qt=Ut*E[Gt]+Wt*w[Gt+1],w[Gt+1]=-Wt*E[Gt]+Ut*w[Gt+1],Vt=Wt*E[Gt+1],E[Gt+1]=Ut*E[Gt+1],d&&Gt<o-1)for(var Ht=0;Ht<o;Ht++)Bt=Ut*x.get(Ht,Gt)+Wt*x.get(Ht,Gt+1),x.set(Ht,Gt+1,-Wt*x.get(Ht,Gt)+Ut*x.get(Ht,Gt+1)),x.set(Ht,Gt,Bt)}E[H-2]=qt;break;case 4:if(w[vt]<=0&&(w[vt]=w[vt]<0?-w[vt]:0,l))for(var Kt=0;Kt<=dt;Kt++)b.set(Kt,vt,-b.get(Kt,vt));for(;vt<dt&&!(w[vt]>=w[vt+1]);){var $t=w[vt];if(w[vt]=w[vt+1],w[vt+1]=$t,l&&vt<i-1)for(var Jt=0;Jt<i;Jt++)$t=b.get(Jt,vt+1),b.set(Jt,vt+1,b.get(Jt,vt)),b.set(Jt,vt,$t);if(d&&vt<o-1)for(var Qt=0;Qt<o;Qt++)$t=x.get(Qt,vt+1),x.set(Qt,vt+1,x.get(Qt,vt)),x.set(Qt,vt,$t);vt++}H--}}if(v){var Xt=b;b=x,x=Xt}this.m=o,this.n=i,this.s=w,this.U=x,this.V=b}),[{key:"solve",value:function(t){for(var e=t,r=this.threshold,n=this.s.length,o=D.zeros(n,n),i=0;i<n;i++)Math.abs(this.s[i])<=r?o.set(i,i,0):o.set(i,i,1/this.s[i]);for(var a=this.U,s=this.rightSingularVectors,u=s.mmul(o),c=s.rows,h=a.rows,f=D.zeros(c,h),d=0;d<c;d++)for(var l=0;l<h;l++){for(var v=0,g=0;g<n;g++)v+=u.get(d,g)*a.get(l,g);f.set(d,l,v)}return f.mmul(e)}},{key:"solveForDiagonal",value:function(t){return this.solve(D.diag(t))}},{key:"inverse",value:function(){for(var t=this.V,e=this.threshold,r=t.rows,n=t.columns,o=new D(r,this.s.length),i=0;i<r;i++)for(var a=0;a<n;a++)Math.abs(this.s[a])>e&&o.set(i,a,t.get(i,a)/this.s[a]);for(var s=this.U,u=s.rows,c=s.columns,h=new D(r,u),f=0;f<r;f++)for(var d=0;d<u;d++){for(var l=0,v=0;v<c;v++)l+=o.get(f,v)*s.get(d,v);h.set(f,d,l)}return h}},{key:"condition",get:function(){return this.s[0]/this.s[Math.min(this.m,this.n)-1]}},{key:"norm2",get:function(){return this.s[0]}},{key:"rank",get:function(){for(var t=Math.max(this.m,this.n)*this.s[0]*Number.EPSILON,e=0,r=this.s,n=0,o=r.length;n<o;n++)r[n]>t&&e++;return e}},{key:"diagonal",get:function(){return Array.from(this.s)}},{key:"threshold",get:function(){return Number.EPSILON/2*Math.max(this.m,this.n)*this.s[0]}},{key:"leftSingularVectors",get:function(){return this.U}},{key:"rightSingularVectors",get:function(){return this.V}},{key:"diagonalMatrix",get:function(){return D.diag(this.s)}}])}();function ot(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t=Z.checkMatrix(t),e=Z.checkMatrix(e),r?new nt(t).solve(e):t.isSquare()?new tt(t).solve(e):new rt(t).solve(e)}function it(t,e){for(var r=[],n=0;n<t;n++)n!==e&&r.push(n);return r}function at(t,e,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e-9;if(t>(arguments.length>4&&void 0!==arguments[4]?arguments[4]:1e-9))return new Array(e.rows+1).fill(0);for(var o=e.addRow(r,[0]),i=0;i<o.rows;i++)Math.abs(o.get(i,0))<n&&o.set(i,0,0);return o.to1DArray()}var st=function(){return p((function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};g(this,t);var n=r.assumeSymmetric,o=void 0!==n&&n;if(!(e=Z.checkMatrix(e)).isSquare())throw new Error("Matrix is not a square matrix");if(e.isEmpty())throw new Error("Matrix must be non-empty");var i,a,s=e.columns,u=new D(s,s),c=new Float64Array(s),h=new Float64Array(s),f=e;if(o||e.isSymmetric()){for(i=0;i<s;i++)for(a=0;a<s;a++)u.set(i,a,f.get(i,a));!function(t,e,r,n){var o,i,a,s,u,c,h,f;for(u=0;u<t;u++)r[u]=n.get(t-1,u);for(s=t-1;s>0;s--){for(f=0,a=0,c=0;c<s;c++)f+=Math.abs(r[c]);if(0===f)for(e[s]=r[s-1],u=0;u<s;u++)r[u]=n.get(s-1,u),n.set(s,u,0),n.set(u,s,0);else{for(c=0;c<s;c++)r[c]/=f,a+=r[c]*r[c];for(o=r[s-1],i=Math.sqrt(a),o>0&&(i=-i),e[s]=f*i,a-=o*i,r[s-1]=o-i,u=0;u<s;u++)e[u]=0;for(u=0;u<s;u++){for(o=r[u],n.set(u,s,o),i=e[u]+n.get(u,u)*o,c=u+1;c<=s-1;c++)i+=n.get(c,u)*r[c],e[c]+=n.get(c,u)*o;e[u]=i}for(o=0,u=0;u<s;u++)e[u]/=a,o+=e[u]*r[u];for(h=o/(a+a),u=0;u<s;u++)e[u]-=h*r[u];for(u=0;u<s;u++){for(o=r[u],i=e[u],c=u;c<=s-1;c++)n.set(c,u,n.get(c,u)-(o*e[c]+i*r[c]));r[u]=n.get(s-1,u),n.set(s,u,0)}}r[s]=a}for(s=0;s<t-1;s++){if(n.set(t-1,s,n.get(s,s)),n.set(s,s,1),0!==(a=r[s+1])){for(c=0;c<=s;c++)r[c]=n.get(c,s+1)/a;for(u=0;u<=s;u++){for(i=0,c=0;c<=s;c++)i+=n.get(c,s+1)*n.get(c,u);for(c=0;c<=s;c++)n.set(c,u,n.get(c,u)-i*r[c])}}for(c=0;c<=s;c++)n.set(c,s+1,0)}for(u=0;u<t;u++)r[u]=n.get(t-1,u),n.set(t-1,u,0);n.set(t-1,t-1,1),e[0]=0}(s,h,c,u),function(t,e,r,n){var o,i,a,s,u,c,h,f,d,l,v,g,p,y,m,w;for(a=1;a<t;a++)e[a-1]=e[a];e[t-1]=0;var x=0,b=0,E=Number.EPSILON;for(c=0;c<t;c++){for(b=Math.max(b,Math.abs(r[c])+Math.abs(e[c])),h=c;h<t&&!(Math.abs(e[h])<=E*b);)h++;if(h>c)do{for(o=r[c],d=et(f=(r[c+1]-o)/(2*e[c]),1),f<0&&(d=-d),r[c]=e[c]/(f+d),r[c+1]=e[c]*(f+d),l=r[c+1],i=o-r[c],a=c+2;a<t;a++)r[a]-=i;for(x+=i,f=r[h],g=v=1,p=v,y=e[c+1],m=0,w=0,a=h-1;a>=c;a--)for(p=g,g=v,w=m,o=v*e[a],i=v*f,d=et(f,e[a]),e[a+1]=m*d,m=e[a]/d,f=(v=f/d)*r[a]-m*o,r[a+1]=i+m*(v*o+m*r[a]),u=0;u<t;u++)i=n.get(u,a+1),n.set(u,a+1,m*n.get(u,a)+v*i),n.set(u,a,v*n.get(u,a)-m*i);f=-m*w*p*y*e[c]/l,e[c]=m*f,r[c]=v*f}while(Math.abs(e[c])>E*b);r[c]=r[c]+x,e[c]=0}for(a=0;a<t-1;a++){for(u=a,f=r[a],s=a+1;s<t;s++)r[s]<f&&(u=s,f=r[s]);if(u!==a)for(r[u]=r[a],r[a]=f,s=0;s<t;s++)f=n.get(s,a),n.set(s,a,n.get(s,u)),n.set(s,u,f)}}(s,h,c,u)}else{var d=new D(s,s),l=new Float64Array(s);for(a=0;a<s;a++)for(i=0;i<s;i++)d.set(i,a,f.get(i,a));!function(t,e,r,n){var o,i,a,s,u,c,h,f=t-1;for(c=1;c<=f-1;c++){for(h=0,s=c;s<=f;s++)h+=Math.abs(e.get(s,c-1));if(0!==h){for(a=0,s=f;s>=c;s--)r[s]=e.get(s,c-1)/h,a+=r[s]*r[s];for(i=Math.sqrt(a),r[c]>0&&(i=-i),a-=r[c]*i,r[c]=r[c]-i,u=c;u<t;u++){for(o=0,s=f;s>=c;s--)o+=r[s]*e.get(s,u);for(o/=a,s=c;s<=f;s++)e.set(s,u,e.get(s,u)-o*r[s])}for(s=0;s<=f;s++){for(o=0,u=f;u>=c;u--)o+=r[u]*e.get(s,u);for(o/=a,u=c;u<=f;u++)e.set(s,u,e.get(s,u)-o*r[u])}r[c]=h*r[c],e.set(c,c-1,h*i)}}for(s=0;s<t;s++)for(u=0;u<t;u++)n.set(s,u,s===u?1:0);for(c=f-1;c>=1;c--)if(0!==e.get(c,c-1)){for(s=c+1;s<=f;s++)r[s]=e.get(s,c-1);for(u=c;u<=f;u++){for(i=0,s=c;s<=f;s++)i+=r[s]*n.get(s,u);for(i=i/r[c]/e.get(c,c-1),s=c;s<=f;s++)n.set(s,u,n.get(s,u)+i*r[s])}}}(s,d,l,u),function(t,e,r,n,o){var i,a,s,u,c,h,f,d,l,v,g,p,y,m,w,x=t-1,b=t-1,E=Number.EPSILON,k=0,M=0,N=0,_=0,j=0,A=0,O=0,S=0;for(i=0;i<t;i++)for((i<0||i>b)&&(r[i]=o.get(i,i),e[i]=0),a=Math.max(i-1,0);a<t;a++)M+=Math.abs(o.get(i,a));for(;x>=0;){for(u=x;u>0&&(0===(A=Math.abs(o.get(u-1,u-1))+Math.abs(o.get(u,u)))&&(A=M),!(Math.abs(o.get(u,u-1))<E*A));)u--;if(u===x)o.set(x,x,o.get(x,x)+k),r[x]=o.get(x,x),e[x]=0,x--,S=0;else if(u===x-1){if(f=o.get(x,x-1)*o.get(x-1,x),_=(N=(o.get(x-1,x-1)-o.get(x,x))/2)*N+f,O=Math.sqrt(Math.abs(_)),o.set(x,x,o.get(x,x)+k),o.set(x-1,x-1,o.get(x-1,x-1)+k),d=o.get(x,x),_>=0){for(O=N>=0?N+O:N-O,r[x-1]=d+O,r[x]=r[x-1],0!==O&&(r[x]=d-f/O),e[x-1]=0,e[x]=0,N=(d=o.get(x,x-1))/(A=Math.abs(d)+Math.abs(O)),_=O/A,N/=j=Math.sqrt(N*N+_*_),_/=j,a=x-1;a<t;a++)O=o.get(x-1,a),o.set(x-1,a,_*O+N*o.get(x,a)),o.set(x,a,_*o.get(x,a)-N*O);for(i=0;i<=x;i++)O=o.get(i,x-1),o.set(i,x-1,_*O+N*o.get(i,x)),o.set(i,x,_*o.get(i,x)-N*O);for(i=0;i<=b;i++)O=n.get(i,x-1),n.set(i,x-1,_*O+N*n.get(i,x)),n.set(i,x,_*n.get(i,x)-N*O)}else r[x-1]=d+N,r[x]=d+N,e[x-1]=O,e[x]=-O;x-=2,S=0}else{if(d=o.get(x,x),l=0,f=0,u<x&&(l=o.get(x-1,x-1),f=o.get(x,x-1)*o.get(x-1,x)),10===S){for(k+=d,i=0;i<=x;i++)o.set(i,i,o.get(i,i)-d);d=l=.75*(A=Math.abs(o.get(x,x-1))+Math.abs(o.get(x-1,x-2))),f=-.4375*A*A}if(30===S&&(A=(A=(l-d)/2)*A+f)>0){for(A=Math.sqrt(A),l<d&&(A=-A),A=d-f/((l-d)/2+A),i=0;i<=x;i++)o.set(i,i,o.get(i,i)-A);k+=A,d=l=f=.964}for(S+=1,c=x-2;c>=u&&(N=((j=d-(O=o.get(c,c)))*(A=l-O)-f)/o.get(c+1,c)+o.get(c,c+1),_=o.get(c+1,c+1)-O-j-A,j=o.get(c+2,c+1),N/=A=Math.abs(N)+Math.abs(_)+Math.abs(j),_/=A,j/=A,c!==u)&&!(Math.abs(o.get(c,c-1))*(Math.abs(_)+Math.abs(j))<E*(Math.abs(N)*(Math.abs(o.get(c-1,c-1))+Math.abs(O)+Math.abs(o.get(c+1,c+1)))));)c--;for(i=c+2;i<=x;i++)o.set(i,i-2,0),i>c+2&&o.set(i,i-3,0);for(s=c;s<=x-1&&(m=s!==x-1,s!==c&&(N=o.get(s,s-1),_=o.get(s+1,s-1),j=m?o.get(s+2,s-1):0,0!==(d=Math.abs(N)+Math.abs(_)+Math.abs(j))&&(N/=d,_/=d,j/=d)),0!==d);s++)if(A=Math.sqrt(N*N+_*_+j*j),N<0&&(A=-A),0!==A){for(s!==c?o.set(s,s-1,-A*d):u!==c&&o.set(s,s-1,-o.get(s,s-1)),d=(N+=A)/A,l=_/A,O=j/A,_/=N,j/=N,a=s;a<t;a++)N=o.get(s,a)+_*o.get(s+1,a),m&&(N+=j*o.get(s+2,a),o.set(s+2,a,o.get(s+2,a)-N*O)),o.set(s,a,o.get(s,a)-N*d),o.set(s+1,a,o.get(s+1,a)-N*l);for(i=0;i<=Math.min(x,s+3);i++)N=d*o.get(i,s)+l*o.get(i,s+1),m&&(N+=O*o.get(i,s+2),o.set(i,s+2,o.get(i,s+2)-N*j)),o.set(i,s,o.get(i,s)-N),o.set(i,s+1,o.get(i,s+1)-N*_);for(i=0;i<=b;i++)N=d*n.get(i,s)+l*n.get(i,s+1),m&&(N+=O*n.get(i,s+2),n.set(i,s+2,n.get(i,s+2)-N*j)),n.set(i,s,n.get(i,s)-N),n.set(i,s+1,n.get(i,s+1)-N*_)}}}if(0!==M){for(x=t-1;x>=0;x--)if(N=r[x],0===(_=e[x]))for(u=x,o.set(x,x,1),i=x-1;i>=0;i--){for(f=o.get(i,i)-N,j=0,a=u;a<=x;a++)j+=o.get(i,a)*o.get(a,x);if(e[i]<0)O=f,A=j;else if(u=i,0===e[i]?o.set(i,x,0!==f?-j/f:-j/(E*M)):(d=o.get(i,i+1),l=o.get(i+1,i),h=(d*A-O*j)/(_=(r[i]-N)*(r[i]-N)+e[i]*e[i]),o.set(i,x,h),o.set(i+1,x,Math.abs(d)>Math.abs(O)?(-j-f*h)/d:(-A-l*h)/O)),E*(h=Math.abs(o.get(i,x)))*h>1)for(a=i;a<=x;a++)o.set(a,x,o.get(a,x)/h)}else if(_<0)for(u=x-1,Math.abs(o.get(x,x-1))>Math.abs(o.get(x-1,x))?(o.set(x-1,x-1,_/o.get(x,x-1)),o.set(x-1,x,-(o.get(x,x)-N)/o.get(x,x-1))):(w=ut(0,-o.get(x-1,x),o.get(x-1,x-1)-N,_),o.set(x-1,x-1,w[0]),o.set(x-1,x,w[1])),o.set(x,x-1,0),o.set(x,x,1),i=x-2;i>=0;i--){for(v=0,g=0,a=u;a<=x;a++)v+=o.get(i,a)*o.get(a,x-1),g+=o.get(i,a)*o.get(a,x);if(f=o.get(i,i)-N,e[i]<0)O=f,j=v,A=g;else if(u=i,0===e[i]?(w=ut(-v,-g,f,_),o.set(i,x-1,w[0]),o.set(i,x,w[1])):(d=o.get(i,i+1),l=o.get(i+1,i),p=(r[i]-N)*(r[i]-N)+e[i]*e[i]-_*_,y=2*(r[i]-N)*_,0===p&&0===y&&(p=E*M*(Math.abs(f)+Math.abs(_)+Math.abs(d)+Math.abs(l)+Math.abs(O))),w=ut(d*j-O*v+_*g,d*A-O*g-_*v,p,y),o.set(i,x-1,w[0]),o.set(i,x,w[1]),Math.abs(d)>Math.abs(O)+Math.abs(_)?(o.set(i+1,x-1,(-v-f*o.get(i,x-1)+_*o.get(i,x))/d),o.set(i+1,x,(-g-f*o.get(i,x)-_*o.get(i,x-1))/d)):(w=ut(-j-l*o.get(i,x-1),-A-l*o.get(i,x),O,_),o.set(i+1,x-1,w[0]),o.set(i+1,x,w[1]))),E*(h=Math.max(Math.abs(o.get(i,x-1)),Math.abs(o.get(i,x))))*h>1)for(a=i;a<=x;a++)o.set(a,x-1,o.get(a,x-1)/h),o.set(a,x,o.get(a,x)/h)}for(i=0;i<t;i++)if(i<0||i>b)for(a=i;a<t;a++)n.set(i,a,o.get(i,a));for(a=t-1;a>=0;a--)for(i=0;i<=b;i++){for(O=0,s=0;s<=Math.min(a,b);s++)O+=n.get(i,s)*o.get(s,a);n.set(i,a,O)}}}(s,h,c,u,d)}this.n=s,this.e=h,this.d=c,this.V=u}),[{key:"realEigenvalues",get:function(){return Array.from(this.d)}},{key:"imaginaryEigenvalues",get:function(){return Array.from(this.e)}},{key:"eigenvectorMatrix",get:function(){return this.V}},{key:"diagonalMatrix",get:function(){var t,e,r=this.n,n=this.e,o=this.d,i=new D(r,r);for(t=0;t<r;t++){for(e=0;e<r;e++)i.set(t,e,0);i.set(t,t,o[t]),n[t]>0?i.set(t,t+1,n[t]):n[t]<0&&i.set(t,t-1,n[t])}return i}}])}();function ut(t,e,r,n){var o,i;return Math.abs(r)>Math.abs(n)?[(t+(o=n/r)*e)/(i=r+o*n),(e-o*t)/i]:[((o=r/n)*t+e)/(i=n+o*r),(o*e-t)/i]}var ct=function(){return p((function t(e){if(g(this,t),!(e=Z.checkMatrix(e)).isSymmetric())throw new Error("Matrix is not symmetric");var r,n,o,i=e,a=i.rows,s=new D(a,a),u=!0;for(n=0;n<a;n++){var c=0;for(o=0;o<n;o++){var h=0;for(r=0;r<o;r++)h+=s.get(o,r)*s.get(n,r);h=(i.get(n,o)-h)/s.get(o,o),s.set(n,o,h),c+=h*h}for(c=i.get(n,n)-c,u&&(u=c>0),s.set(n,n,Math.sqrt(Math.max(c,0))),o=n+1;o<a;o++)s.set(n,o,0)}this.L=s,this.positiveDefinite=u}),[{key:"isPositiveDefinite",value:function(){return this.positiveDefinite}},{key:"solve",value:function(t){t=Z.checkMatrix(t);var e=this.L,r=e.rows;if(t.rows!==r)throw new Error("Matrix dimensions do not match");if(!1===this.isPositiveDefinite())throw new Error("Matrix is not positive definite");var n,o,i,a=t.columns,s=t.clone();for(i=0;i<r;i++)for(o=0;o<a;o++){for(n=0;n<i;n++)s.set(i,o,s.get(i,o)-s.get(n,o)*e.get(i,n));s.set(i,o,s.get(i,o)/e.get(i,i))}for(i=r-1;i>=0;i--)for(o=0;o<a;o++){for(n=i+1;n<r;n++)s.set(i,o,s.get(i,o)-s.get(n,o)*e.get(n,i));s.set(i,o,s.get(i,o)/e.get(i,i))}return s}},{key:"lowerTriangularMatrix",get:function(){return this.L}}])}(),ht=p((function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};g(this,t),e=Z.checkMatrix(e);var n,o=r.Y,i=r.scaleScores,a=void 0!==i&&i,s=r.maxIterations,u=void 0===s?1e3:s,c=r.terminationCriteria,h=void 0===c?1e-10:c;if(o){if((o=y.isAnyArray(o)&&"number"==typeof o[0]?D.columnVector(o):Z.checkMatrix(o)).rows!==e.rows)throw new Error("Y should have the same number of rows as X");n=o.getColumnVector(0)}else n=e.getColumnVector(0);for(var f,d,l,v,p=1,m=0;m<u&&p>h;m++)l=(l=e.transpose().mmul(n).div(n.transpose().mmul(n).get(0,0))).div(l.norm()),f=e.mmul(l).div(l.transpose().mmul(l).get(0,0)),m>0&&(p=f.clone().sub(v).pow(2).sum()),v=f.clone(),o?(d=(d=o.transpose().mmul(f).div(f.transpose().mmul(f).get(0,0))).div(d.norm()),n=o.mmul(d).div(d.transpose().mmul(d).get(0,0))):n=f;if(o){var w=e.transpose().mmul(f).div(f.transpose().mmul(f).get(0,0));w=w.div(w.norm());var x=e.clone().sub(f.clone().mmul(w.transpose())),b=n.transpose().mmul(f).div(f.transpose().mmul(f).get(0,0)),E=o.clone().sub(f.clone().mulS(b.get(0,0)).mmul(d.transpose()));this.t=f,this.p=w.transpose(),this.w=l.transpose(),this.q=d,this.u=n,this.s=f.transpose().mmul(f),this.xResidual=x,this.yResidual=E,this.betas=b}else this.w=l.transpose(),this.s=f.transpose().mmul(f).sqrt(),this.t=a?f.clone().div(this.s.get(0,0)):f,this.xResidual=e.sub(f.mmul(l.transpose()))}));e.y3=P,e.jy=ct,e.oN=ct,e.Hc=V,e.cg=st,e.hj=st,e.LU=tt,e.Tb=tt,e.uq=D,e.Zm=U,e.Dq=B,e.__=W,e.q0=Y,e.lh=K,e.pI=H,e.zC=$,e.zg=J,e.g6=Q,e.OL=ht,e.ks=ht,e.QR=rt,e.jp=rt,e.mk=nt,e.W2=nt,e.l=q,e.KY=X,e.dv=Z,e.BR=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};t=new D(t);var n=!1;if("object"!=typeof e||D.isMatrix(e)||y.isAnyArray(e)?e=new D(e):(r=e,e=t,n=!0),t.rows!==e.rows)throw new TypeError("Both matrices must have the same number of rows");var o=r,i=o.center,a=void 0===i||i,s=o.scale,u=void 0===s||s;a&&(t.center("column"),n||e.center("column")),u&&(t.scale("column"),n||e.scale("column"));for(var c=t.standardDeviation("column",{unbiased:!0}),h=n?c:e.standardDeviation("column",{unbiased:!0}),f=t.transpose().mmul(e),d=0;d<f.rows;d++)for(var l=0;l<f.columns;l++)f.set(d,l,f.get(d,l)*(1/(c[d]*h[l]))*(1/(t.rows-1)));return f},e.Wu=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};t=new D(t);var n=!1;if("object"!=typeof e||D.isMatrix(e)||y.isAnyArray(e)?e=new D(e):(r=e,e=t,n=!0),t.rows!==e.rows)throw new TypeError("Both matrices must have the same number of rows");var o=r.center;(void 0===o||o)&&(t=t.center("column"),n||(e=e.center("column")));for(var i=t.transpose().mmul(e),a=0;a<i.rows;a++)for(var s=0;s<i.columns;s++)i.set(a,s,i.get(a,s)*(1/(t.rows-1)));return i},e.a4=function t(e){var r,n,o,i,a,s;if((e=D.checkMatrix(e)).isSquare())return 0===e.columns?1:2===e.columns?(r=e.get(0,0),n=e.get(0,1),o=e.get(1,0),r*e.get(1,1)-n*o):3===e.columns?(i=new $(e,[1,2],[1,2]),a=new $(e,[1,2],[0,2]),s=new $(e,[1,2],[0,1]),r=e.get(0,0),n=e.get(0,1),o=e.get(0,2),r*t(i)-n*t(a)+o*t(s)):new tt(e).determinant;throw Error("determinant can only be calculated for a square matrix")},e.DI=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t=Z.checkMatrix(t),e?new nt(t).inverse():ot(t,D.eye(t.rows))},e.Jo=function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.thresholdValue,n=void 0===r?1e-9:r,o=e.thresholdError,i=void 0===o?1e-9:o,a=(t=D.checkMatrix(t)).rows,s=new D(a,a),u=0;u<a;u++){var c=D.columnVector(t.getRow(u)),h=t.subMatrixRow(it(a,u)).transpose(),f=new nt(h).solve(c),d=D.sub(c,h.mmul(f)).abs().max();s.setRow(u,at(d,f,u,n,i))}return s},e.Zi=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.EPSILON;if((t=D.checkMatrix(t)).isEmpty())return t.transpose();for(var r=new nt(t,{autoTranspose:!0}),n=r.leftSingularVectors,o=r.rightSingularVectors,i=r.diagonal,a=0;a<i.length;a++)Math.abs(i[a])>e?i[a]=1/i[a]:i[a]=0;return o.mmul(D.diag(i).mmul(n.transpose()))},e.kH=ot,e.LV=function(t,e){if(y.isAnyArray(t))return t[0]&&y.isAnyArray(t[0])?new Z(t):new X(t,e);throw new Error("the argument is not an array")}},6757:t=>{var e=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},6785:(t,e,r)=>{var n=r(334);t.exports=function(t,e){var r=[];return n(t,(function(t,n,o){e(t,n,o)&&r.push(t)})),r}},6804:t=>{t.exports=function(t,e){return t>e}},6853:(t,e,r)=>{var n=r(9776),o=r(6050),i=r(6521),a=r(5330),s=r(498),u=r(8962),c=r(9217),h=r(6646),f="[object Arguments]",d="[object Array]",l="[object Object]",v=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,g,p,y){var m=u(t),w=u(e),x=m?d:s(t),b=w?d:s(e),E=(x=x==f?l:x)==l,k=(b=b==f?l:b)==l,M=x==b;if(M&&c(t)){if(!c(e))return!1;m=!0,E=!1}if(M&&!E)return y||(y=new n),m||h(t)?o(t,e,r,g,p,y):i(t,e,x,r,g,p,y);if(!(1&r)){var N=E&&v.call(t,"__wrapped__"),_=k&&v.call(e,"__wrapped__");if(N||_){var j=N?t.value():t,A=_?e.value():e;return y||(y=new n),p(j,A,r,g,y)}}return!!M&&(y||(y=new n),a(t,e,r,g,p,y))}},6876:(t,e,r)=>{var n=r(5405),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},6889:(t,e,r)=>{"use strict";var n=r(6036),o=r(1882),i=r(5362).positionX;t.exports=function(t){(function(t){var e=o.buildLayerMatrix(t),r=t.graph().ranksep,i=0;n.forEach(e,(function(e){var o=n.max(n.map(e,(function(e){return t.node(e).height})));n.forEach(e,(function(e){t.node(e).y=i+o/2})),i+=o+r}))})(t=o.asNonCompoundGraph(t)),n.forEach(i(t),(function(e,r){t.node(r).x=e}))}},7023:(t,e,r)=>{var n=r(9210),o=r(1167);t.exports=function(t,e){return o(t||[],e||[],n)}},7029:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},7108:(t,e,r)=>{var n=r(5143);function o(){return t.exports=o="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var o=n(t,e);if(o){var i=Object.getOwnPropertyDescriptor(o,e);return i.get?i.get.call(arguments.length<3?t:r):i.value}},t.exports.__esModule=!0,t.exports.default=t.exports,o.apply(null,arguments)}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports},7159:(t,e,r)=>{var n=r(8729),o=r(199),i=r(1251);t.exports=function(t,e,r){return e=n(e),i(t,o()?Reflect.construct(e,r||[],n(t).constructor):e.apply(t,r))},t.exports.__esModule=!0,t.exports.default=t.exports},7173:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},7219:(t,e,r)=>{var n=r(5422);t.exports=function(){this.__data__=new n,this.size=0}},7236:(t,e,r)=>{var n=r(1906);t.exports=function(t,e,r){n(t,e),e.set(t,r)},t.exports.__esModule=!0,t.exports.default=t.exports},7358:(t,e,r)=>{var n=r(4121);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},7404:t=>{t.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports},7444:(t,e,r)=>{var n=r(1126);t.exports=function(t,e){var r=e?n(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}},7464:(t,e,r)=>{var n=r(4158);t.exports=function(t){return n(this.__data__,t)>-1}},7560:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},7643:(t,e,r)=>{var n=r(7786),o=r(2942),i=r(4336);t.exports=function(t){return i(o(t,void 0,n),t+"")}},7644:(t,e,r)=>{var n=r(9797),o=r(2707);t.exports=function(t){return n((function(e,r){var n=-1,i=r.length,a=i>1?r[i-1]:void 0,s=i>2?r[2]:void 0;for(a=t.length>3&&"function"==typeof a?(i--,a):void 0,s&&o(r[0],r[1],s)&&(a=i<3?void 0:a,i=1),e=Object(e);++n<i;){var u=r[n];u&&t(e,u,n,a)}return e}))}},7671:(t,e,r)=>{var n=r(7560),o=Object.create,i=function(){function t(){}return function(e){if(!n(e))return{};if(o)return o(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();t.exports=i},7786:(t,e,r)=>{var n=r(2397);t.exports=function(t){return null!=t&&t.length?n(t,1):[]}},7788:(t,e,r)=>{var n=r(3791);t.exports=function(t){return function(e){return n(e,t)}}},7791:t=>{t.exports=function(t){return void 0===t}},7866:(t,e,r)=>{var n=r(8590),o=r(1643),i=r(756);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},7877:(t,e,r)=>{var n=r(9973),o=r(1896),i=r(1710),a=i&&i.isMap,s=a?o(a):n;t.exports=s},7880:(t,e,r)=>{var n=r(2886),o=r(1742);t.exports=function(t,e,r){return o.transform(t.nodes(),(function(o,i){o[i]=n(t,i,e,r)}),{})}},7896:(t,e,r)=>{var n=r(3936);t.exports=function(t){return n(this,t).has(t)}},7912:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},7937:(t,e,r)=>{var n=r(3651),o=r(6785),i=r(5434),a=r(8962);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},7945:(t,e,r)=>{var n=r(9797),o=r(1417),i=r(2707),a=r(4620),s=Object.prototype,u=s.hasOwnProperty,c=n((function(t,e){t=Object(t);var r=-1,n=e.length,c=n>2?e[2]:void 0;for(c&&i(e[0],e[1],c)&&(n=1);++r<n;)for(var h=e[r],f=a(h),d=-1,l=f.length;++d<l;){var v=f[d],g=t[v];(void 0===g||o(g,s[v])&&!u.call(t,v))&&(t[v]=h[v])}return t}));t.exports=c},7950:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},7951:(t,e,r)=>{var n=r(6532),o=r(7671),i=r(9942),a=r(5434),s=r(8084),u=r(8962),c=r(9217),h=r(5179),f=r(7560),d=r(6646);t.exports=function(t,e,r){var l=u(t),v=l||c(t)||d(t);if(e=a(e,4),null==r){var g=t&&t.constructor;r=v?l?new g:[]:f(t)&&h(g)?o(s(t)):{}}return(v?n:i)(t,(function(t,n,o){return e(r,t,n,o)})),r}},7953:t=>{t.exports=function(t,e,r){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")},t.exports.__esModule=!0,t.exports.default=t.exports},7968:(t,e,r)=>{var n=r(1742);t.exports=function(t){var e=0,r=[],o={},i=[];function a(s){var u=o[s]={onStack:!0,lowlink:e,index:e++};if(r.push(s),t.successors(s).forEach((function(t){n.has(o,t)?o[t].onStack&&(u.lowlink=Math.min(u.lowlink,o[t].index)):(a(t),u.lowlink=Math.min(u.lowlink,o[t].lowlink))})),u.lowlink===u.index){var c,h=[];do{c=r.pop(),o[c].onStack=!1,h.push(c)}while(s!==c);i.push(h)}}return t.nodes().forEach((function(t){n.has(o,t)||a(t)})),i}},7972:(t,e,r)=>{var n=r(4604),o=r(3547),i=r(4968),a=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o;t.exports=a},8084:(t,e,r)=>{var n=r(9122)(Object.getPrototypeOf,Object);t.exports=n},8123:(t,e,r)=>{var n=r(9210),o=r(5850),i=r(8468),a=r(7560),s=r(8812);t.exports=function(t,e,r,u){if(!a(t))return t;for(var c=-1,h=(e=o(e,t)).length,f=h-1,d=t;null!=d&&++c<h;){var l=s(e[c]),v=r;if("__proto__"===l||"constructor"===l||"prototype"===l)return t;if(c!=f){var g=d[l];void 0===(v=u?u(g,l,d):void 0)&&(v=a(g)?g:i(e[c+1])?[]:{})}n(d,l,v),d=d[l]}return t}},8174:(t,e,r)=>{var n=r(8832),o=r(68),i=r(4620);t.exports=function(t){return n(t,i,o)}},8186:t=>{t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports.default=t.exports},8373:t=>{function e(r,n){return t.exports=e=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},t.exports.__esModule=!0,t.exports.default=t.exports,e(r,n)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},8421:(t,e,r)=>{var n=r(943);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),s=void 0!==e,u=null===e,c=e==e,h=n(e);if(!u&&!h&&!a&&t>e||a&&s&&c&&!u&&!h||o&&s&&c||!r&&c||!i)return 1;if(!o&&!a&&!h&&t<e||h&&r&&i&&!o&&!a||u&&r&&i||!s&&i||!c)return-1}return 0}},8468:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},8478:(t,e,r)=>{"use strict";var n=r(6036),o=r(1882);t.exports={run:function(t){t.graph().dummyChains=[],n.forEach(t.edges(),(function(e){!function(t,e){var r,n,i,a=e.v,s=t.node(a).rank,u=e.w,c=t.node(u).rank,h=e.name,f=t.edge(e),d=f.labelRank;if(c!==s+1){for(t.removeEdge(e),i=0,++s;s<c;++i,++s)f.points=[],n={width:0,height:0,edgeLabel:f,edgeObj:e,rank:s},r=o.addDummyNode(t,"edge",n,"_d"),s===d&&(n.width=f.width,n.height=f.height,n.dummy="edge-label",n.labelpos=f.labelpos),t.setEdge(a,r,{weight:f.weight},h),0===i&&t.graph().dummyChains.push(r),a=r;t.setEdge(a,u,{weight:f.weight},h)}}(t,e)}))},undo:function(t){n.forEach(t.graph().dummyChains,(function(e){var r,n=t.node(e),o=n.edgeLabel;for(t.setEdge(n.edgeObj,o);n.dummy;)r=t.successors(e)[0],t.removeNode(e),o.points.push({x:n.x,y:n.y}),"edge-label"===n.dummy&&(o.x=n.x,o.y=n.y,o.width=n.width,o.height=n.height),e=r,n=t.node(e)}))}}},8529:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},8590:(t,e,r)=>{var n=r(9776),o=r(2027);t.exports=function(t,e,r,i){var a=r.length,s=a,u=!i;if(null==t)return!s;for(t=Object(t);a--;){var c=r[a];if(u&&c[2]?c[1]!==t[c[0]]:!(c[0]in t))return!1}for(;++a<s;){var h=(c=r[a])[0],f=t[h],d=c[1];if(u&&c[2]){if(void 0===f&&!(h in t))return!1}else{var l=new n;if(i)var v=i(f,d,h,t,e,l);if(!(void 0===v?o(d,f,3,i,l):v))return!1}}return!0}},8662:(t,e,r)=>{var n=r(6014),o=r(5167),i=r(8962),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},8729:t=>{function e(r){return t.exports=e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.__esModule=!0,t.exports.default=t.exports,e(r)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},8739:(t,e,r)=>{var n=r(498),o=r(6223);t.exports=function(t){return o(t)&&"[object Set]"==n(t)}},8759:(t,e,r)=>{var n=r(2920),o=r(3225),i=r(4620);t.exports=function(t,e){return null==t?t:n(t,o(e),i)}},8775:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},8790:(t,e,r)=>{var n=r(9948),o=r(1543);t.exports=function(t,e){return t&&n(e,o(e),t)}},8808:(t,e,r)=>{var n=r(943);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],s=e(a);if(null!=s&&(void 0===u?s==s&&!n(s):r(s,u)))var u=s,c=a}return c}},8812:(t,e,r)=>{var n=r(943);t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}},8815:(t,e,r)=>{var n=r(911),o=r(7644)((function(t,e,r){n(t,e,r)}));t.exports=o},8832:(t,e,r)=>{var n=r(957),o=r(8962);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},8853:(t,e,r)=>{var n=r(1906);t.exports=function(t,e){n(t,e),e.add(t)},t.exports.__esModule=!0,t.exports.default=t.exports},8873:(t,e,r)=>{var n=r(319)(r(6698),"Promise");t.exports=n},8962:t=>{var e=Array.isArray;t.exports=e},8972:(t,e,r)=>{var n=r(1126);t.exports=function(t,e){var r=e?n(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}},8979:(t,e,r)=>{var n=r(3791),o=r(8123),i=r(5850);t.exports=function(t,e,r){for(var a=-1,s=e.length,u={};++a<s;){var c=e[a],h=n(t,c);r(h,c)&&o(u,i(c,t),h)}return u}},9007:(t,e,r)=>{var n=r(8808),o=r(2443),i=r(4513);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},9054:(t,e,r)=>{t.exports=r(6269)},9107:(t,e,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},9122:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},9150:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},9168:(t,e,r)=>{var n=r(2418),o=r(9501),i=r(2099),a=r(1011);t.exports=function(t,e){return n(t)||o(t,e)||i(t,e)||a()},t.exports.__esModule=!0,t.exports.default=t.exports},9207:(t,e,r)=>{var n=r(6036),o=r(4206).Graph;t.exports=function(t,e,r){var i=function(t){for(var e;t.hasNode(e=n.uniqueId("_root")););return e}(t),a=new o({compound:!0}).setGraph({root:i}).setDefaultNodeLabel((function(e){return t.node(e)}));return n.forEach(t.nodes(),(function(o){var s=t.node(o),u=t.parent(o);(s.rank===e||s.minRank<=e&&e<=s.maxRank)&&(a.setNode(o),a.setParent(o,u||i),n.forEach(t[r](o),(function(e){var r=e.v===o?e.w:e.v,i=a.edge(r,o),s=n.isUndefined(i)?0:i.weight;a.setEdge(r,o,{weight:t.edge(e).weight+s})})),n.has(s,"minRank")&&a.setNode(o,{borderLeft:s.borderLeft[e],borderRight:s.borderRight[e]}))})),a}},9210:(t,e,r)=>{var n=r(85),o=r(1417),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r){var a=t[e];i.call(t,e)&&o(a,r)&&(void 0!==r||e in t)||n(t,e,r)}},9217:(t,e,r)=>{t=r.nmd(t);var n=r(6698),o=r(6584),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,s=a&&a.exports===i?n.Buffer:void 0,u=(s?s.isBuffer:void 0)||o;t.exports=u},9318:(t,e,r)=>{var n=r(4359),o=r(8962),i=r(6223);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},9327:(t,e,r)=>{var n=r(1742),o=r(7968);t.exports=function(t){return n.filter(o(t),(function(e){return e.length>1||1===e.length&&t.hasEdge(e[0],e[0])}))}},9360:(t,e,r)=>{var n=r(1742);t.exports=function(t){var e,r={},o=[];function i(o){n.has(r,o)||(r[o]=!0,e.push(o),n.each(t.successors(o),i),n.each(t.predecessors(o),i))}return n.each(t.nodes(),(function(t){e=[],i(t),e.length&&o.push(e)})),o}},9365:(t,e,r)=>{var n=r(8808),o=r(6804),i=r(4513);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},9370:(t,e,r)=>{var n=r(8373);t.exports=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&n(t,e)},t.exports.__esModule=!0,t.exports.default=t.exports},9375:t=>{t.exports="2.1.8"},9381:(t,e,r)=>{var n=r(3936);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},9493:t=>{t.exports=function(){this.__data__=[],this.size=0}},9501:t=>{t.exports=function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}},t.exports.__esModule=!0,t.exports.default=t.exports},9509:(t,e,r)=>{var n=r(6036);t.exports=function(t,e){return n.map(e,(function(e){var r=t.inEdges(e);if(r.length){var o=n.reduce(r,(function(e,r){var n=t.edge(r),o=t.node(r.v);return{sum:e.sum+n.weight*o.order,weight:e.weight+n.weight}}),{sum:0,weight:0});return{v:e,barycenter:o.sum/o.weight,weight:o.weight}}return{v:e}}))}},9515:(t,e,r)=>{"use strict";var n=r(6036);function o(t,e,r){for(var o=n.zipObject(r,n.map(r,(function(t,e){return e}))),i=n.flatten(n.map(e,(function(e){return n.sortBy(n.map(t.outEdges(e),(function(e){return{pos:o[e.w],weight:t.edge(e).weight}})),"pos")})),!0),a=1;a<r.length;)a<<=1;var s=2*a-1;a-=1;var u=n.map(new Array(s),(function(){return 0})),c=0;return n.forEach(i.forEach((function(t){var e=t.pos+a;u[e]+=t.weight;for(var r=0;e>0;)e%2&&(r+=u[e+1]),u[e=e-1>>1]+=t.weight;c+=t.weight*r}))),c}t.exports=function(t,e){for(var r=0,n=1;n<e.length;++n)r+=o(t,e[n-1],e[n]);return r}},9518:(t,e,r)=>{var n=r(8808),o=r(5434),i=r(2443);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},9542:(t,e,r)=>{var n=r(9791).default;t.exports=function(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},9558:(t,e,r)=>{var n=r(2823),o=r(498),i=r(1693),a=r(9318),s=r(1734);t.exports=function(t){if(null==t)return 0;if(i(t))return a(t)?s(t):t.length;var e=o(t);return"[object Map]"==e||"[object Set]"==e?t.size:n(t).length}},9671:(t,e,r)=>{var n=r(6036);t.exports=function(t){var e=function(t){var e={},r=0;return n.forEach(t.children(),(function o(i){var a=r;n.forEach(t.children(i),o),e[i]={low:a,lim:r++}})),e}(t);n.forEach(t.graph().dummyChains,(function(r){for(var n=t.node(r),o=n.edgeObj,i=function(t,e,r,n){var o,i,a=[],s=[],u=Math.min(e[r].low,e[n].low),c=Math.max(e[r].lim,e[n].lim);o=r;do{o=t.parent(o),a.push(o)}while(o&&(e[o].low>u||c>e[o].lim));for(i=o,o=n;(o=t.parent(o))!==i;)s.push(o);return{path:a.concat(s.reverse()),lca:i}}(t,e,o.v,o.w),a=i.path,s=i.lca,u=0,c=a[u],h=!0;r!==o.w;){if(n=t.node(r),h){for(;(c=a[u])!==s&&t.node(c).maxRank<n.rank;)u++;c===s&&(h=!1)}if(!h){for(;u<a.length-1&&t.node(c=a[u+1]).minRank<=n.rank;)u++;c=a[u]}t.setParent(r,c),r=t.successors(r)[0]}}))}},9684:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},9703:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},9761:(t,e,r)=>{var n=r(9948),o=r(4620);t.exports=function(t,e){return t&&n(e,o(e),t)}},9776:(t,e,r)=>{var n=r(5422),o=r(7219),i=r(8775),a=r(486),s=r(3906),u=r(4442);function c(t){var e=this.__data__=new n(t);this.size=e.size}c.prototype.clear=o,c.prototype.delete=i,c.prototype.get=a,c.prototype.has=s,c.prototype.set=u,t.exports=c},9780:(t,e,r)=>{var n=r(5179),o=r(2547),i=r(7560),a=r(7950),s=/^\[object .+?Constructor\]$/,u=Function.prototype,c=Object.prototype,h=u.toString,f=c.hasOwnProperty,d=RegExp("^"+h.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?d:s).test(a(t))}},9791:t=>{function e(r){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(r)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},9797:(t,e,r)=>{var n=r(4513),o=r(2942),i=r(4336);t.exports=function(t,e){return i(o(t,e,n),t+"")}},9824:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},9838:(t,e,r)=>{var n=r(4069),o=r(9381),i=r(2988),a=r(7896),s=r(4008);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=s,t.exports=u},9844:(t,e,r)=>{var n=r(3275);t.exports=function(t,e,r){return(e=n(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t},t.exports.__esModule=!0,t.exports.default=t.exports},9858:(t,e,r)=>{var n=r(6014),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,s=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,s),r=t[s];try{t[s]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[s]=r:delete t[s]),o}},9940:t=>{t.exports=function(t){throw new TypeError('"'+t+'" is read-only')},t.exports.__esModule=!0,t.exports.default=t.exports},9942:(t,e,r)=>{var n=r(2920),o=r(1543);t.exports=function(t,e){return t&&n(t,e,o)}},9948:(t,e,r)=>{var n=r(9210),o=r(85);t.exports=function(t,e,r,i){var a=!r;r||(r={});for(var s=-1,u=e.length;++s<u;){var c=e[s],h=i?i(r[c],t[c],c,r,t):void 0;void 0===h&&(h=t[c]),a?o(r,c,h):n(r,c,h)}return r}},9973:(t,e,r)=>{var n=r(498),o=r(6223);t.exports=function(t){return o(t)&&"[object Map]"==n(t)}}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={id:n,loaded:!1,exports:{}};return t[n](i,i.exports,r),i.loaded=!0,i.exports}return r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nmd=t=>(t.paths=[],t.children||(t.children=[]),t),(()=>{"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){e=function(){return n};var r,n={},o=Object.prototype,i=o.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function h(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{h({},"")}catch(r){h=function(t,e,r){return t[e]=r}}function f(t,e,n,o){var i=e&&e.prototype instanceof v?e:v,a=Object.create(i.prototype);return h(a,"_invoke",function(t,e,n){var o=1;return function(i,a){if(3===o)throw Error("Generator is already running");if(4===o){if("throw"===i)throw a;return{value:r,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var u=k(s,n);if(u){if(u===l)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(1===o)throw o=4,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=3;var c=d(t,e,n);if("normal"===c.type){if(o=n.done?4:2,c.arg===l)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=4,n.method="throw",n.arg=c.arg)}}}(t,n,new _(o||[])),!0),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=f;var l={};function v(){}function g(){}function p(){}var y={};h(y,s,(function(){return this}));var m=Object.getPrototypeOf,w=m&&m(m(j([])));w&&w!==o&&i.call(w,s)&&(y=w);var x=p.prototype=v.prototype=Object.create(y);function b(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function E(e,r){function n(o,a,s,u){var c=d(e[o],e,a);if("throw"!==c.type){var h=c.arg,f=h.value;return f&&"object"==t(f)&&i.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,s,u)}),(function(t){n("throw",t,s,u)})):r.resolve(f).then((function(t){h.value=t,s(h)}),(function(t){return n("throw",t,s,u)}))}u(c.arg)}var o;h(this,"_invoke",(function(t,e){function i(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(i,i):i()}),!0)}function k(t,e){var n=e.method,o=t.i[n];if(o===r)return e.delegate=null,"throw"===n&&t.i.return&&(e.method="return",e.arg=r,k(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),l;var i=d(o,t.i,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,l;var a=i.arg;return a?a.done?(e[t.r]=a.value,e.next=t.n,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,l):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}function M(t){this.tryEntries.push(t)}function N(t){var e=t[4]||{};e.type="normal",e.arg=r,t[4]=e}function _(t){this.tryEntries=[[-1]],t.forEach(M,this),this.reset(!0)}function j(e){if(null!=e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function t(){for(;++o<e.length;)if(i.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=r,t.done=!0,t};return a.next=a}}throw new TypeError(t(e)+" is not iterable")}return g.prototype=p,h(x,"constructor",p),h(p,"constructor",g),g.displayName=h(p,c,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,h(t,c,"GeneratorFunction")),t.prototype=Object.create(x),t},n.awrap=function(t){return{__await:t}},b(E.prototype),h(E.prototype,u,(function(){return this})),n.AsyncIterator=E,n.async=function(t,e,r,o,i){void 0===i&&(i=Promise);var a=new E(f(t,e,r,o),i);return n.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(x),h(x,c,"Generator"),h(x,s,(function(){return this})),h(x,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.unshift(n);return function t(){for(;r.length;)if((n=r.pop())in e)return t.value=n,t.done=!1,t;return t.done=!0,t}},n.values=j,_.prototype={constructor:_,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(N),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r){a.type="throw",a.arg=t,e.next=r}for(var o=e.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i[4],s=this.prev,u=i[1],c=i[2];if(-1===i[0])return n("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=s){if(s<u)return this.method="next",this.arg=r,n(u),!0;if(s<c)return n(c),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n[0]>-1&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]&&(o=null);var i=o?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],l):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),N(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r[0]===t){var n=r[4];if("throw"===n.type){var o=n.arg;N(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={i:j(t),r:e,n},"next"===this.method&&(this.arg=r),l}},n}function n(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(r[n[o]]=t[n[o]])}return r}function o(t,e,r,n){return new(r||(r=Promise))((function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(a,s)}u((n=n.apply(t,e||[])).next())}))}function i(e){var r=function(e){if("object"!=t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==t(r)?r:r+""}function a(t,e,r){return(e=i(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){a(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){if(t){if("string"==typeof t)return c(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(t,e):void 0}}function f(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(t,e)||h(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=h(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){s=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function l(t){return function(t){if(Array.isArray(t))return c(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||h(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function g(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,i(n.key),n)}}function p(t,e,r){return e&&g(t.prototype,e),r&&g(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function y(e,r){if(r&&("object"==t(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e)}function m(t){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},m(t)}function w(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(w=function(){return!!t})()}function x(t,e,r){return e=m(e),y(t,w()?Reflect.construct(e,r||[],m(t).constructor):e.apply(t,r))}function b(t,e){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},b(t,e)}function E(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&b(t,e)}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;const k=function(){function t(){this._events={}}return t.prototype.on=function(t,e,r){return this._events[t]||(this._events[t]=[]),this._events[t].push({callback:e,once:!!r}),this},t.prototype.once=function(t,e){return this.on(t,e,!0)},t.prototype.emit=function(t){for(var e=this,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var o=this._events[t]||[],i=this._events["*"]||[],a=function(n){for(var o=n.length,i=0;i<o;i++)if(n[i]){var a=n[i],s=a.callback;a.once&&(n.splice(i,1),0===n.length&&delete e._events[t],o--,i--),s.apply(e,r)}};a(o),a(i)},t.prototype.off=function(t,e){if(t)if(e){for(var r=this._events[t]||[],n=r.length,o=0;o<n;o++)r[o].callback===e&&(r.splice(o,1),n--,o--);0===r.length&&delete this._events[t]}else delete this._events[t];else this._events={};return this},t.prototype.getEvents=function(){return this._events},t}();function M(t,e,r,n){for(;t.length;){var o=t.shift();if(r(o))return!0;e.add(o.id),n(o.id).forEach((function(r){e.has(r.id)||(e.add(r.id),t.push(r))}))}return!1}function N(t,e,r,n){if(r(t))return!0;e.add(t.id);var o,i=d(n(t.id));try{for(i.s();!(o=i.n()).done;){var a=o.value;if(!e.has(a.id)&&N(a,e,r,n))return!0}}catch(t){i.e(t)}finally{i.f()}return!1}var _=function(){return!0},j=function(){return p((function t(e){var r=this;v(this,t),a(this,"graph",void 0),a(this,"nodeFilter",void 0),a(this,"edgeFilter",void 0),a(this,"cacheEnabled",void 0),a(this,"inEdgesMap",new Map),a(this,"outEdgesMap",new Map),a(this,"bothEdgesMap",new Map),a(this,"allNodesMap",new Map),a(this,"allEdgesMap",new Map),a(this,"clearCache",(function(){r.inEdgesMap.clear(),r.outEdgesMap.clear(),r.bothEdgesMap.clear(),r.allNodesMap.clear(),r.allEdgesMap.clear()})),a(this,"refreshCache",(function(){r.clearCache(),r.updateCache(r.graph.getAllNodes().map((function(t){return t.id})))})),a(this,"updateCache",(function(t){var e=new Set;t.forEach((function(t){var n=r.bothEdgesMap.get(t);if(n&&n.forEach((function(t){return e.add(t.id)})),r.hasNode(t)){var o=r.graph.getRelatedEdges(t,"in").filter(r.edgeFilter),i=r.graph.getRelatedEdges(t,"out").filter(r.edgeFilter),a=Array.from(new Set([].concat(l(o),l(i))));a.forEach((function(t){return e.add(t.id)})),r.inEdgesMap.set(t,o),r.outEdgesMap.set(t,i),r.bothEdgesMap.set(t,a),r.allNodesMap.set(t,r.graph.getNode(t))}else r.inEdgesMap.delete(t),r.outEdgesMap.delete(t),r.bothEdgesMap.delete(t),r.allNodesMap.delete(t)})),e.forEach((function(t){r.hasEdge(t)?r.allEdgesMap.set(t,r.graph.getEdge(t)):r.allEdgesMap.delete(t)}))})),a(this,"handleGraphChanged",(function(t){var e=new Set;t.changes.forEach((function(r){switch(r.type){case"NodeAdded":case"NodeRemoved":e.add(r.value.id);break;case"NodeDataUpdated":e.add(r.id);break;case"EdgeAdded":case"EdgeRemoved":e.add(r.value.source),e.add(r.value.target);break;case"EdgeUpdated":"source"!==r.propertyName&&"target"!==r.propertyName||(e.add(r.oldValue),e.add(r.newValue));break;case"EdgeDataUpdated":if(t.graph.hasEdge(r.id)){var n=t.graph.getEdge(r.id);e.add(n.source),e.add(n.target)}}})),r.updateCache(e)})),this.graph=e.graph;var n=e.nodeFilter||_,o=e.edgeFilter||_;this.nodeFilter=n,this.edgeFilter=function(t){var e=r.graph.getEdgeDetail(t.id),i=e.source,a=e.target;return!(!n(i)||!n(a))&&o(t,i,a)},"auto"===e.cache?(this.cacheEnabled=!0,this.startAutoCache()):"manual"===e.cache?this.cacheEnabled=!0:this.cacheEnabled=!1}),[{key:"startAutoCache",value:function(){this.refreshCache(),this.graph.on("changed",this.handleGraphChanged)}},{key:"stopAutoCache",value:function(){this.graph.off("changed",this.handleGraphChanged)}},{key:"checkNodeExistence",value:function(t){this.getNode(t)}},{key:"hasNode",value:function(t){if(!this.graph.hasNode(t))return!1;var e=this.graph.getNode(t);return this.nodeFilter(e)}},{key:"areNeighbors",value:function(t,e){return this.checkNodeExistence(t),this.getNeighbors(e).some((function(e){return e.id===t}))}},{key:"getNode",value:function(t){var e=this.graph.getNode(t);if(!this.nodeFilter(e))throw new Error("Node not found for id: "+t);return e}},{key:"getRelatedEdges",value:function(t,e){return this.checkNodeExistence(t),this.cacheEnabled?"in"===e?this.inEdgesMap.get(t):"out"===e?this.outEdgesMap.get(t):this.bothEdgesMap.get(t):this.graph.getRelatedEdges(t,e).filter(this.edgeFilter)}},{key:"getDegree",value:function(t,e){return this.getRelatedEdges(t,e).length}},{key:"getSuccessors",value:function(t){var e=this,r=this.getRelatedEdges(t,"out").map((function(t){return e.getNode(t.target)}));return Array.from(new Set(r))}},{key:"getPredecessors",value:function(t){var e=this,r=this.getRelatedEdges(t,"in").map((function(t){return e.getNode(t.source)}));return Array.from(new Set(r))}},{key:"getNeighbors",value:function(t){var e=this.getPredecessors(t),r=this.getSuccessors(t);return Array.from(new Set([].concat(l(e),l(r))))}},{key:"hasEdge",value:function(t){if(!this.graph.hasEdge(t))return!1;var e=this.graph.getEdge(t);return this.edgeFilter(e)}},{key:"getEdge",value:function(t){var e=this.graph.getEdge(t);if(!this.edgeFilter(e))throw new Error("Edge not found for id: "+t);return e}},{key:"getEdgeDetail",value:function(t){var e=this.getEdge(t);return{edge:e,source:this.getNode(e.source),target:this.getNode(e.target)}}},{key:"hasTreeStructure",value:function(t){return this.graph.hasTreeStructure(t)}},{key:"getRoots",value:function(t){return this.graph.getRoots(t).filter(this.nodeFilter)}},{key:"getChildren",value:function(t,e){return this.checkNodeExistence(t),this.graph.getChildren(t,e).filter(this.nodeFilter)}},{key:"getParent",value:function(t,e){this.checkNodeExistence(t);var r=this.graph.getParent(t,e);return r&&this.nodeFilter(r)?r:null}},{key:"getAllNodes",value:function(){return this.cacheEnabled?Array.from(this.allNodesMap.values()):this.graph.getAllNodes().filter(this.nodeFilter)}},{key:"getAllEdges",value:function(){return this.cacheEnabled?Array.from(this.allEdgesMap.values()):this.graph.getAllEdges().filter(this.edgeFilter)}},{key:"bfs",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"out",n={in:this.getPredecessors.bind(this),out:this.getSuccessors.bind(this),both:this.getNeighbors.bind(this)}[r];M([this.getNode(t)],new Set,e,n)}},{key:"dfs",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"out",n={in:this.getPredecessors.bind(this),out:this.getSuccessors.bind(this),both:this.getNeighbors.bind(this)}[r];N(this.getNode(t),new Set,e,n)}}])}(),A=function(t){function e(t){var r;return v(this,e),a(r=x(this,e),"nodeMap",new Map),a(r,"edgeMap",new Map),a(r,"inEdgesMap",new Map),a(r,"outEdgesMap",new Map),a(r,"bothEdgesMap",new Map),a(r,"treeIndices",new Map),a(r,"changes",[]),a(r,"batchCount",0),a(r,"onChanged",(function(){})),a(r,"batch",(function(t){r.batchCount+=1,t(),r.batchCount-=1,r.batchCount||r.commit()})),t?(t.nodes&&r.addNodes(t.nodes),t.edges&&r.addEdges(t.edges),t.tree&&r.addTree(t.tree),t.onChanged&&(r.onChanged=t.onChanged),r):y(r)}return E(e,t),p(e,[{key:"commit",value:function(){var t=this.changes;this.changes=[];var e={graph:this,changes:t};this.emit("changed",e),this.onChanged(e)}},{key:"reduceChanges",value:function(t){var e=[];return t.forEach((function(t){switch(t.type){case"NodeRemoved":var r=!1;e=e.filter((function(e){if("NodeAdded"===e.type){var n=e.value.id===t.value.id;return n&&(r=!0),!n}return"NodeDataUpdated"===e.type?e.id!==t.value.id:"TreeStructureChanged"!==e.type||e.nodeId!==t.value.id})),r||e.push(t);break;case"EdgeRemoved":var n=!1;e=e.filter((function(e){if("EdgeAdded"===e.type){var r=e.value.id===t.value.id;return r&&(n=!0),!r}return"EdgeDataUpdated"!==e.type&&"EdgeUpdated"!==e.type||e.id!==t.value.id})),n||e.push(t);break;case"NodeDataUpdated":case"EdgeDataUpdated":case"EdgeUpdated":var o=e.findIndex((function(e){return e.type===t.type&&e.id===t.id&&(void 0===t.propertyName||e.propertyName===t.propertyName)})),i=e[o];i?void 0!==t.propertyName?i.newValue=t.newValue:(e.splice(o,1),e.push(t)):e.push(t);break;case"TreeStructureDetached":(e=e.filter((function(e){return"TreeStructureAttached"===e.type?e.treeKey!==t.treeKey:"TreeStructureChanged"!==e.type||e.treeKey!==t.treeKey}))).push(t);break;case"TreeStructureChanged":var a=e.find((function(e){return"TreeStructureChanged"===e.type&&e.treeKey===t.treeKey&&e.nodeId===t.nodeId}));a?a.newParentId=t.newParentId:e.push(t);break;default:e.push(t)}})),e}},{key:"checkNodeExistence",value:function(t){this.getNode(t)}},{key:"hasNode",value:function(t){return this.nodeMap.has(t)}},{key:"areNeighbors",value:function(t,e){return this.getNeighbors(e).some((function(e){return e.id===t}))}},{key:"getNode",value:function(t){var e=this.nodeMap.get(t);if(!e)throw new Error("Node not found for id: "+t);return e}},{key:"getRelatedEdges",value:function(t,e){if(this.checkNodeExistence(t),"in"===e){var r=this.inEdgesMap.get(t);return Array.from(r)}if("out"===e){var n=this.outEdgesMap.get(t);return Array.from(n)}var o=this.bothEdgesMap.get(t);return Array.from(o)}},{key:"getDegree",value:function(t,e){return this.getRelatedEdges(t,e).length}},{key:"getSuccessors",value:function(t){var e=this,r=this.getRelatedEdges(t,"out").map((function(t){return e.getNode(t.target)}));return Array.from(new Set(r))}},{key:"getPredecessors",value:function(t){var e=this,r=this.getRelatedEdges(t,"in").map((function(t){return e.getNode(t.source)}));return Array.from(new Set(r))}},{key:"getNeighbors",value:function(t){var e=this.getPredecessors(t),r=this.getSuccessors(t);return Array.from(new Set([].concat(l(e),l(r))))}},{key:"doAddNode",value:function(t){if(this.hasNode(t.id))throw new Error("Node already exists: "+t.id);this.nodeMap.set(t.id,t),this.inEdgesMap.set(t.id,new Set),this.outEdgesMap.set(t.id,new Set),this.bothEdgesMap.set(t.id,new Set),this.treeIndices.forEach((function(e){e.childrenMap.set(t.id,new Set)})),this.changes.push({type:"NodeAdded",value:t})}},{key:"addNodes",value:function(t){var e=this;this.batch((function(){var r,n=d(t);try{for(n.s();!(r=n.n()).done;){var o=r.value;e.doAddNode(o)}}catch(t){n.e(t)}finally{n.f()}}))}},{key:"addNode",value:function(t){this.addNodes([t])}},{key:"doRemoveNode",value:function(t){var e=this,r=this.getNode(t),n=this.bothEdgesMap.get(t);null==n||n.forEach((function(t){return e.doRemoveEdge(t.id)})),this.nodeMap.delete(t),this.treeIndices.forEach((function(e){var n,o;null===(n=e.childrenMap.get(t))||void 0===n||n.forEach((function(t){e.parentMap.delete(t.id)}));var i=e.parentMap.get(t);i&&(null===(o=e.childrenMap.get(i.id))||void 0===o||o.delete(r)),e.parentMap.delete(t),e.childrenMap.delete(t)})),this.bothEdgesMap.delete(t),this.inEdgesMap.delete(t),this.outEdgesMap.delete(t),this.changes.push({type:"NodeRemoved",value:r})}},{key:"removeNodes",value:function(t){var e=this;this.batch((function(){t.forEach((function(t){return e.doRemoveNode(t)}))}))}},{key:"removeNode",value:function(t){this.removeNodes([t])}},{key:"updateNodeDataProperty",value:function(t,e,r){var n=this,o=this.getNode(t);this.batch((function(){var i=o.data[e],a=r;o.data[e]=a,n.changes.push({type:"NodeDataUpdated",id:t,propertyName:e,oldValue:i,newValue:a})}))}},{key:"mergeNodeData",value:function(t,e){var r=this;this.batch((function(){Object.entries(e).forEach((function(e){var n=f(e,2),o=n[0],i=n[1];r.updateNodeDataProperty(t,o,i)}))}))}},{key:"updateNodeData",value:function(){var t,e=this,r=arguments.length<=0?void 0:arguments[0],n=this.getNode(r);"string"!=typeof(arguments.length<=1?void 0:arguments[1])?("function"==typeof(arguments.length<=1?void 0:arguments[1])?t=(arguments.length<=1?void 0:arguments[1])(n.data):"object"==typeof(arguments.length<=1?void 0:arguments[1])&&(t=arguments.length<=1?void 0:arguments[1]),this.batch((function(){var o=n.data,i=t;n.data=t,e.changes.push({type:"NodeDataUpdated",id:r,oldValue:o,newValue:i})}))):this.updateNodeDataProperty(r,arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2])}},{key:"checkEdgeExistence",value:function(t){if(!this.hasEdge(t))throw new Error("Edge not found for id: "+t)}},{key:"hasEdge",value:function(t){return this.edgeMap.has(t)}},{key:"getEdge",value:function(t){return this.checkEdgeExistence(t),this.edgeMap.get(t)}},{key:"getEdgeDetail",value:function(t){var e=this.getEdge(t);return{edge:e,source:this.getNode(e.source),target:this.getNode(e.target)}}},{key:"doAddEdge",value:function(t){if(this.hasEdge(t.id))throw new Error("Edge already exists: "+t.id);this.checkNodeExistence(t.source),this.checkNodeExistence(t.target),this.edgeMap.set(t.id,t);var e=this.inEdgesMap.get(t.target),r=this.outEdgesMap.get(t.source),n=this.bothEdgesMap.get(t.source),o=this.bothEdgesMap.get(t.target);e.add(t),r.add(t),n.add(t),o.add(t),this.changes.push({type:"EdgeAdded",value:t})}},{key:"addEdges",value:function(t){var e=this;this.batch((function(){var r,n=d(t);try{for(n.s();!(r=n.n()).done;){var o=r.value;e.doAddEdge(o)}}catch(t){n.e(t)}finally{n.f()}}))}},{key:"addEdge",value:function(t){this.addEdges([t])}},{key:"doRemoveEdge",value:function(t){var e=this.getEdge(t),r=this.outEdgesMap.get(e.source),n=this.inEdgesMap.get(e.target),o=this.bothEdgesMap.get(e.source),i=this.bothEdgesMap.get(e.target);r.delete(e),n.delete(e),o.delete(e),i.delete(e),this.edgeMap.delete(t),this.changes.push({type:"EdgeRemoved",value:e})}},{key:"removeEdges",value:function(t){var e=this;this.batch((function(){t.forEach((function(t){return e.doRemoveEdge(t)}))}))}},{key:"removeEdge",value:function(t){this.removeEdges([t])}},{key:"updateEdgeSource",value:function(t,e){var r=this,n=this.getEdge(t);this.checkNodeExistence(e);var o=n.source,i=e;this.outEdgesMap.get(o).delete(n),this.bothEdgesMap.get(o).delete(n),this.outEdgesMap.get(i).add(n),this.bothEdgesMap.get(i).add(n),n.source=e,this.batch((function(){r.changes.push({type:"EdgeUpdated",id:t,propertyName:"source",oldValue:o,newValue:i})}))}},{key:"updateEdgeTarget",value:function(t,e){var r=this,n=this.getEdge(t);this.checkNodeExistence(e);var o=n.target,i=e;this.inEdgesMap.get(o).delete(n),this.bothEdgesMap.get(o).delete(n),this.inEdgesMap.get(i).add(n),this.bothEdgesMap.get(i).add(n),n.target=e,this.batch((function(){r.changes.push({type:"EdgeUpdated",id:t,propertyName:"target",oldValue:o,newValue:i})}))}},{key:"updateEdgeDataProperty",value:function(t,e,r){var n=this,o=this.getEdge(t);this.batch((function(){var i=o.data[e],a=r;o.data[e]=a,n.changes.push({type:"EdgeDataUpdated",id:t,propertyName:e,oldValue:i,newValue:a})}))}},{key:"updateEdgeData",value:function(){var t,e=this,r=arguments.length<=0?void 0:arguments[0],n=this.getEdge(r);"string"!=typeof(arguments.length<=1?void 0:arguments[1])?("function"==typeof(arguments.length<=1?void 0:arguments[1])?t=(arguments.length<=1?void 0:arguments[1])(n.data):"object"==typeof(arguments.length<=1?void 0:arguments[1])&&(t=arguments.length<=1?void 0:arguments[1]),this.batch((function(){var o=n.data,i=t;n.data=t,e.changes.push({type:"EdgeDataUpdated",id:r,oldValue:o,newValue:i})}))):this.updateEdgeDataProperty(r,arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2])}},{key:"mergeEdgeData",value:function(t,e){var r=this;this.batch((function(){Object.entries(e).forEach((function(e){var n=f(e,2),o=n[0],i=n[1];r.updateEdgeDataProperty(t,o,i)}))}))}},{key:"checkTreeExistence",value:function(t){if(!this.hasTreeStructure(t))throw new Error("Tree structure not found for treeKey: "+t)}},{key:"hasTreeStructure",value:function(t){return this.treeIndices.has(t)}},{key:"attachTreeStructure",value:function(t){var e=this;this.treeIndices.has(t)||(this.treeIndices.set(t,{parentMap:new Map,childrenMap:new Map}),this.batch((function(){e.changes.push({type:"TreeStructureAttached",treeKey:t})})))}},{key:"detachTreeStructure",value:function(t){var e=this;this.checkTreeExistence(t),this.treeIndices.delete(t),this.batch((function(){e.changes.push({type:"TreeStructureDetached",treeKey:t})}))}},{key:"addTree",value:function(t,e){var r=this;this.batch((function(){r.attachTreeStructure(e);for(var n=[],o=Array.isArray(t)?t:[t];o.length;){var i=o.shift();n.push(i),i.children&&o.push.apply(o,l(i.children))}r.addNodes(n),n.forEach((function(t){var n;null===(n=t.children)||void 0===n||n.forEach((function(n){r.setParent(n.id,t.id,e)}))}))}))}},{key:"getRoots",value:function(t){var e=this;return this.checkTreeExistence(t),this.getAllNodes().filter((function(r){return!e.getParent(r.id,t)}))}},{key:"getChildren",value:function(t,e){this.checkNodeExistence(t),this.checkTreeExistence(e);var r=this.treeIndices.get(e).childrenMap.get(t);return Array.from(r||[])}},{key:"getParent",value:function(t,e){return this.checkNodeExistence(t),this.checkTreeExistence(e),this.treeIndices.get(e).parentMap.get(t)||null}},{key:"getAncestors",value:function(t,e){for(var r,n=[],o=this.getNode(t);r=this.getParent(o.id,e);)n.push(r),o=r;return n}},{key:"setParent",value:function(t,e,r){var n=this;this.checkTreeExistence(r);var o=this.treeIndices.get(r);if(o){var i=this.getNode(t),a=o.parentMap.get(t);if((null==a?void 0:a.id)!==e)if(null!=e){var s,u=this.getNode(e);o.parentMap.set(t,u),a&&(null===(s=o.childrenMap.get(a.id))||void 0===s||s.delete(i));var c=o.childrenMap.get(u.id);c||(c=new Set,o.childrenMap.set(u.id,c)),c.add(i),this.batch((function(){n.changes.push({type:"TreeStructureChanged",treeKey:r,nodeId:t,oldParentId:null==a?void 0:a.id,newParentId:u.id})}))}else{var h;a&&(null===(h=o.childrenMap.get(a.id))||void 0===h||h.delete(i)),o.parentMap.delete(t)}}}},{key:"dfsTree",value:function(t,e,r){var n=this;return N(this.getNode(t),new Set,e,(function(t){return n.getChildren(t,r)}))}},{key:"bfsTree",value:function(t,e,r){var n=this;return M([this.getNode(t)],new Set,e,(function(t){return n.getChildren(t,r)}))}},{key:"getAllNodes",value:function(){return Array.from(this.nodeMap.values())}},{key:"getAllEdges",value:function(){return Array.from(this.edgeMap.values())}},{key:"bfs",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"out",n={in:this.getPredecessors.bind(this),out:this.getSuccessors.bind(this),both:this.getNeighbors.bind(this)}[r];return M([this.getNode(t)],new Set,e,n)}},{key:"dfs",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"out",n={in:this.getPredecessors.bind(this),out:this.getSuccessors.bind(this),both:this.getNeighbors.bind(this)}[r];return N(this.getNode(t),new Set,e,n)}},{key:"clone",value:function(){var t=new e({nodes:this.getAllNodes().map((function(t){return u(u({},t),{},{data:u({},t.data)})})),edges:this.getAllEdges().map((function(t){return u(u({},t),{},{data:u({},t.data)})}))});return this.treeIndices.forEach((function(e,r){var n=e.parentMap,o=e.childrenMap,i=new Map;n.forEach((function(e,r){i.set(r,t.getNode(e.id))}));var a=new Map;o.forEach((function(e,r){a.set(r,new Set(Array.from(e).map((function(e){return t.getNode(e.id)}))))})),t.treeIndices.set(r,{parentMap:i,childrenMap:a})})),t}},{key:"toJSON",value:function(){return JSON.stringify({nodes:this.getAllNodes(),edges:this.getAllEdges()})}},{key:"createView",value:function(t){return new j(u({graph:this},t))}}])}(k),O=Symbol("Comlink.proxy"),S=Symbol("Comlink.endpoint"),z=Symbol("Comlink.releaseProxy"),R=Symbol("Comlink.finalizer"),I=Symbol("Comlink.thrown"),P=function(t){return"object"==typeof t&&null!==t||"function"==typeof t},T={canHandle:function(t){return P(t)&&t[O]},serialize:function(t){var e=new MessageChannel,r=e.port1,n=e.port2;return D(t,r),[n,[n]]},deserialize:function(t){return t.start(),e=t,r=new Map,e.addEventListener("message",(function(t){var e=t.data;if(e&&e.id){var n=r.get(e.id);if(n)try{n(e)}finally{r.delete(e.id)}}})),B(e,r,[],undefined);var e,r}},C=new Map([["proxy",T],["throw",{canHandle:function(t){return P(t)&&I in t},serialize:function(t){var e=t.value;return[e instanceof Error?{isError:!0,value:{message:e.message,name:e.name,stack:e.stack}}:{isError:!1,value:e},[]]},deserialize:function(t){if(t.isError)throw Object.assign(new Error(t.value.message),t.value);throw t.value}}]]);function D(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:globalThis,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:["*"];e.addEventListener("message",(function n(o){if(o&&o.data)if(function(t,e){var r,n=d(t);try{for(n.s();!(r=n.n()).done;){var o=r.value;if(e===o||"*"===o)return!0;if(o instanceof RegExp&&o.test(e))return!0}}catch(t){n.e(t)}finally{n.f()}return!1}(r,o.origin)){var i,s=Object.assign({path:[]},o.data),u=s.id,c=s.type,h=s.path,v=(o.data.argumentList||[]).map(H);try{var g=h.slice(0,-1).reduce((function(t,e){return t[e]}),t),p=h.reduce((function(t,e){return t[e]}),t);switch(c){case"GET":i=p;break;case"SET":g[h.slice(-1)[0]]=H(o.data.value),i=!0;break;case"APPLY":i=p.apply(g,v);break;case"CONSTRUCT":var y=function(t,e,r){if(w())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,e);var o=new(t.bind.apply(t,n));return r&&b(o,r.prototype),o}(p,l(v));i=function(t){return Object.assign(t,a({},O,!0))}(y);break;case"ENDPOINT":var m=new MessageChannel,x=m.port1,E=m.port2;D(t,E),i=function(t,e){return W.set(t,e),t}(x,[x]);break;case"RELEASE":i=void 0;break;default:return}}catch(y){i=a({value:y},I,0)}Promise.resolve(i).catch((function(t){return a({value:t},I,0)})).then((function(r){var o=f(Y(r),2),i=o[0],a=o[1];e.postMessage(Object.assign(Object.assign({},i),{id:u}),a),"RELEASE"===c&&(e.removeEventListener("message",n),L(e),R in t&&"function"==typeof t[R]&&t[R]())})).catch((function(t){var r=f(Y(a({value:new TypeError("Unserializable return value")},I,0)),2),n=r[0],o=r[1];e.postMessage(Object.assign(Object.assign({},n),{id:u}),o)}))}else console.warn("Invalid origin '".concat(o.origin,"' for comlink proxy"))})),e.start&&e.start()}function L(t){(function(t){return"MessagePort"===t.constructor.name})(t)&&t.close()}function F(t){if(t)throw new Error("Proxy has been released and is not useable")}function q(t){return K(t,new Map,{type:"RELEASE"}).then((function(){L(t)}))}var V=new WeakMap,G="FinalizationRegistry"in globalThis&&new FinalizationRegistry((function(t){var e=(V.get(t)||0)-1;V.set(t,e),0===e&&q(t)}));function B(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=!1,o=new Proxy(arguments.length>3&&void 0!==arguments[3]?arguments[3]:function(){},{get:function(i,a){if(F(n),a===z)return function(){!function(t){G&&G.unregister(t)}(o),q(t),e.clear(),n=!0};if("then"===a){if(0===r.length)return{then:function(){return o}};var s=K(t,e,{type:"GET",path:r.map((function(t){return t.toString()}))}).then(H);return s.then.bind(s)}return B(t,e,[].concat(l(r),[a]))},set:function(o,i,a){F(n);var s=f(Y(a),2),u=s[0],c=s[1];return K(t,e,{type:"SET",path:[].concat(l(r),[i]).map((function(t){return t.toString()})),value:u},c).then(H)},apply:function(o,i,a){F(n);var s=r[r.length-1];if(s===S)return K(t,e,{type:"ENDPOINT"}).then(H);if("bind"===s)return B(t,e,r.slice(0,-1));var u=f(U(a),2),c=u[0],h=u[1];return K(t,e,{type:"APPLY",path:r.map((function(t){return t.toString()})),argumentList:c},h).then(H)},construct:function(o,i){F(n);var a=f(U(i),2),s=a[0],u=a[1];return K(t,e,{type:"CONSTRUCT",path:r.map((function(t){return t.toString()})),argumentList:s},u).then(H)}});return function(t,e){var r=(V.get(e)||0)+1;V.set(e,r),G&&G.register(t,e,t)}(o,t),o}function U(t){var e,r=t.map(Y);return[r.map((function(t){return t[0]})),(e=r.map((function(t){return t[1]})),Array.prototype.concat.apply([],e))]}var W=new WeakMap;function Y(t){var e,r=d(C);try{for(r.s();!(e=r.n()).done;){var n=f(e.value,2),o=n[0],i=n[1];if(i.canHandle(t)){var a=f(i.serialize(t),2);return[{type:"HANDLER",name:o,value:a[0]},a[1]]}}}catch(t){r.e(t)}finally{r.f()}return[{type:"RAW",value:t},W.get(t)||[]]}function H(t){switch(t.type){case"HANDLER":return C.get(t.name).deserialize(t.value);case"RAW":return t.value}}function K(t,e,r,n){return new Promise((function(o){var i=new Array(4).fill(0).map((function(){return Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16)})).join("-");e.set(i,o),t.start&&t.start(),t.postMessage(Object.assign({id:i},r),n)}))}function $(t){return"number"==typeof t}var J=function(t,e){if("next"!==t&&"prev"!==t)return e},Q=function(t){t.prev.next=t.next,t.next.prev=t.prev,delete t.next,delete t.prev},X=function(t){function e(){return v(this,e),x(this,e,arguments)}return E(e,t),p(e)}(function(){return p((function t(){v(this,t);var e={};e.prev=e,e.next=e.prev,this.shortcut=e}),[{key:"dequeue",value:function(){var t=this.shortcut,e=t.prev;if(e&&e!==t)return Q(e),e}},{key:"enqueue",value:function(t){var e=this.shortcut;t.prev&&t.next&&Q(t),t.next=e.next,e.next.prev=t,e.next=t,t.prev=e}},{key:"toString",value:function(){for(var t=[],e=this.shortcut,r=e.prev;r!==e;)t.push(JSON.stringify(r,J)),r=null==r?void 0:r.prev;return"[".concat(t.join(", "),"]")}}])}()),Z=function(){return 1},tt=function(t,e,r,n,o){var i,a,s=[];return t.hasNode(n.v)&&(null===(i=t.getRelatedEdges(n.v,"in"))||void 0===i||i.forEach((function(n){var i=n.data.weight,a=t.getNode(n.source);o&&s.push({v:n.source,w:n.target,in:0,out:0}),void 0===a.data.out&&(a.data.out=0),a.data.out-=i,et(e,r,Object.assign({v:a.id},a.data))})),null===(a=t.getRelatedEdges(n.v,"out"))||void 0===a||a.forEach((function(n){var o=n.data.weight,i=n.target,a=t.getNode(i);void 0===a.data.in&&(a.data.in=0),a.data.in-=o,et(e,r,Object.assign({v:a.id},a.data))})),t.removeNode(n.v)),o?s:void 0},et=function(t,e,r){r.out?r.in?t[r.out-r.in+e].enqueue(r):t[t.length-1].enqueue(r):t[0].enqueue(r)},rt=function(t,e){var r="greedy"===e?function(t){var e;if(t.getAllNodes().length<=1)return[];var r=function(t,e){var r=new A,n=0,o=0;t.getAllNodes().forEach((function(t){r.addNode({id:t.id,data:{v:t.id,in:0,out:0}})})),t.getAllEdges().forEach((function(t){var i=r.getRelatedEdges(t.source,"out").find((function(e){return e.target===t.target})),a=(null==e?void 0:e(t))||1;i?r.updateEdgeData(null==i?void 0:i.id,Object.assign(Object.assign({},i.data),{weight:i.data.weight+a})):r.addEdge({id:t.id,source:t.source,target:t.target,data:{weight:a}}),o=Math.max(o,r.getNode(t.source).data.out+=a),n=Math.max(n,r.getNode(t.target).data.in+=a)}));for(var i=[],a=o+n+3,s=0;s<a;s++)i.push(new X);var u=n+1;return r.getAllNodes().forEach((function(t){et(i,u,Object.assign({v:t.id},r.getNode(t.id).data))})),{buckets:i,zeroIdx:u,graph:r}}(t,function(t){return t.data.weight||1}||Z);return null===(e=function(t,e,r){for(var n,o=[],i=e[e.length-1],a=e[0];t.getAllNodes().length;){for(;n=a.dequeue();)tt(t,e,r,n);for(;n=i.dequeue();)tt(t,e,r,n);if(t.getAllNodes().length)for(var s=e.length-2;s>0;--s)if(n=e[s].dequeue()){o=o.concat(tt(t,e,r,n,!0));break}}return o}(r.graph,r.buckets,r.zeroIdx).map((function(e){return t.getRelatedEdges(e.v,"out").filter((function(t){return t.target===e.w}))})))||void 0===e?void 0:e.flat()}(t):nt(t);null==r||r.forEach((function(e){var r=e.data;t.removeEdge(e.id),r.forwardName=e.data.name,r.reversed=!0,t.addEdge({id:e.id,source:e.target,target:e.source,data:Object.assign({},r)})}))},nt=function(t){var e=[],r={},n={},o=function(i){n[i]||(n[i]=!0,r[i]=!0,t.getRelatedEdges(i,"out").forEach((function(t){r[t.target]?e.push(t):o(t.target)})),delete r[i])};return t.getAllNodes().forEach((function(t){return o(t.id)})),e},ot=function(t,e,r,n){var o;do{o="".concat(n).concat(Math.random())}while(t.hasNode(o));return r.dummy=e,t.addNode({id:o,data:r}),o},it=function(t){var e=new A;return t.getAllNodes().forEach((function(r){t.getChildren(r.id).length||e.addNode(Object.assign({},r))})),t.getAllEdges().forEach((function(t){e.addEdge(t)})),e},at=function(t,e){var r,n,o=Number(t.x),i=Number(t.y),a=Number(e.x)-o,s=Number(e.y)-i,u=Number(t.width)/2,c=Number(t.height)/2;return a||s?(Math.abs(s)*u>Math.abs(a)*c?(s<0&&(c=-c),r=c*a/s,n=c):(a<0&&(u=-u),r=u,n=u*s/a),{x:o+r,y:i+n}):{x:0,y:0}},st=function(t){for(var e=[],r=ct(t)+1,n=0;n<r;n++)e.push([]);t.getAllNodes().forEach((function(t){var r=t.data.rank;void 0!==r&&e[r]&&e[r].push(t.id)}));for(var o=0;o<r;o++)e[o]=e[o].sort((function(e,r){return n=t.getNode(e).data.order,o=t.getNode(r).data.order,Number(n)-Number(o);var n,o}));return e},ut=function(t,e,r,n){var o={width:0,height:0};return $(r)&&$(n)&&(o.rank=r,o.order=n),ot(t,"border",o,e)},ct=function(t){var e;return t.getAllNodes().forEach((function(t){var r=t.data.rank;void 0!==r&&(void 0===e||r>e)&&(e=r)})),e||(e=0),e},ht=function(t,e){return t.reduce((function(t,r){return e(t)>e(r)?r:t}))},ft=function(t,e,r,n,o,i){n.includes(e.id)||(n.push(e.id),r||i.push(e.id),o(e.id).forEach((function(e){return ft(t,e,r,n,o,i)})),r&&i.push(e.id))},dt=function(t,e,r,n){var o=Array.isArray(e)?e:[e],i=function(e){return n?t.getSuccessors(e):t.getNeighbors(e)},a=[],s=[];return o.forEach((function(e){if(!t.hasNode(e.id))throw new Error("Graph does not have node: ".concat(e));ft(t,e,"post"===r,s,i,a)})),a},lt=function(t,e,r,n,o,i){var a={rank:i,borderType:e,width:0,height:0},s=o.data[e][i-1],u=ot(t,"border",a,r);o.data[e][i]=u,t.setParent(u,n),s&&t.addEdge({id:"e".concat(Math.random()),source:s,target:u,data:{weight:1}})},vt=function(t){t.getAllNodes().forEach((function(t){gt(t)})),t.getAllEdges().forEach((function(t){gt(t)}))},gt=function(t){var e=t.data.width;t.data.width=t.data.height,t.data.height=e},pt=function(t){(null==t?void 0:t.y)&&(t.y=-t.y)},yt=function(t){var e=t.x;t.x=t.y,t.y=e},mt=function(t,e,r,n,o,i,a){var s=t.getChildren(a);if(null==s?void 0:s.length){var u=ut(t,"_bt"),c=ut(t,"_bb"),h=t.getNode(a);t.setParent(u,a),h.data.borderTop=u,t.setParent(c,a),h.data.borderBottom=c,null==s||s.forEach((function(s){mt(t,e,r,n,o,i,s.id);var h=s.data.borderTop?s.data.borderTop:s.id,f=s.data.borderBottom?s.data.borderBottom:s.id,d=s.data.borderTop?n:2*n,l=h!==f?1:o-i[a]+1;t.addEdge({id:"e".concat(Math.random()),source:u,target:h,data:{minlen:l,weight:d,nestingEdge:!0}}),t.addEdge({id:"e".concat(Math.random()),source:f,target:c,data:{minlen:l,weight:d,nestingEdge:!0}})})),t.getParent(a)||t.addEdge({id:"e".concat(Math.random()),source:e,target:u,data:{weight:0,minlen:o+i[a]}})}else a!==e&&t.addEdge({id:"e".concat(Math.random()),source:e,target:a,data:{weight:0,minlen:r}})},wt="edge-label";function xt(t){return Array.isArray(t)}var bt=function(t){if("object"!=typeof t||null===t)return t;var e;if(xt(t)){e=[];for(var r=0,n=t.length;r<n;r++)"object"==typeof t[r]&&null!=t[r]?e[r]=bt(t[r]):e[r]=t[r]}else for(var o in e={},t)"object"==typeof t[o]&&null!=t[o]?e[o]=bt(t[o]):e[o]=t[o];return e};const Et=bt;var kt=function(t,e,r){for(var n=function(t,e){return null==t?void 0:t.reduce((function(t,r,n){return t[r]=e[n],t}),{})}(r,r.map((function(t,e){return e}))),o=e.map((function(e){var r=t.getRelatedEdges(e,"out").map((function(t){return{pos:n[t.target]||0,weight:t.data.weight}}));return null==r?void 0:r.sort((function(t,e){return t.pos-e.pos}))})).flat().filter((function(t){return void 0!==t})),i=1;i<r.length;)i<<=1;var a=2*i-1;i-=1;var s=Array(a).fill(0,0,a),u=0;return null==o||o.forEach((function(t){if(t){var e=t.pos+i;s[e]+=t.weight;for(var r=0;e>0;)e%2&&(r+=s[e+1]),s[e=e-1>>1]+=t.weight;u+=t.weight*r}})),u},Mt=function(t,e){for(var r=0,n=1;n<(null==e?void 0:e.length);n+=1)r+=kt(t,e[n-1],e[n]);return r},Nt=function(t){for(var e={},r=t.getAllNodes(),n=r.map((function(t){var e;return null!==(e=t.data.rank)&&void 0!==e?e:-1/0})),o=Math.max.apply(Math,l(n)),i=[],a=0;a<o+1;a++)i.push([]);var s=r.sort((function(e,r){return t.getNode(e.id).data.rank-t.getNode(r.id).data.rank})),u=s.filter((function(e){return void 0!==t.getNode(e.id).data.fixorder})).sort((function(e,r){return t.getNode(e.id).data.fixorder-t.getNode(r.id).data.fixorder}));return null==u||u.forEach((function(r){isNaN(t.getNode(r.id).data.rank)||i[t.getNode(r.id).data.rank].push(r.id),e[r.id]=!0})),null==s||s.forEach((function(r){return t.dfsTree(r.id,(function(t){if(e.hasOwnProperty(t.id))return!0;e[t.id]=!0,isNaN(t.data.rank)||i[t.data.rank].push(t.id)}))})),i},_t=function(t,e){var r,n=0,o=0;t.weight&&(n+=t.barycenter*t.weight,o+=t.weight),e.weight&&(n+=e.barycenter*e.weight,o+=e.weight),t.vs=null===(r=e.vs)||void 0===r?void 0:r.concat(t.vs),t.barycenter=n/o,t.weight=o,t.i=Math.min(e.i,t.i),e.merged=!0};const jt=function(t,e){var r,n,o,i={};return null==t||t.forEach((function(t,e){i[t.v]={i:e,indegree:0,in:[],out:[],vs:[t.v]};var r=i[t.v];void 0!==t.barycenter&&(r.barycenter=t.barycenter,r.weight=t.weight)})),null===(r=e.getAllEdges())||void 0===r||r.forEach((function(t){var e=i[t.source],r=i[t.target];void 0!==e&&void 0!==r&&(r.indegree++,e.out.push(i[t.target]))})),function(t){for(var e,r,n=[],o=function(){var o=t.pop();n.push(o),null===(e=o.in.reverse())||void 0===e||e.forEach((function(t){return(e=o,function(t){t.merged||(void 0===t.barycenter||void 0===e.barycenter||t.barycenter>=e.barycenter)&&_t(e,t)})(t);var e})),null===(r=o.out)||void 0===r||r.forEach((function(e){return(r=o,function(e){e.in.push(r),0===--e.indegree&&t.push(e)})(e);var r}))};null==t?void 0:t.length;)o();var i=n.filter((function(t){return!t.merged})),a=["vs","i","barycenter","weight"];return i.map((function(t){var e={};return null==a||a.forEach((function(r){void 0!==t[r]&&(e[r]=t[r])})),e}))}(null===(o=(n=Object.values(i)).filter)||void 0===o?void 0:o.call(n,(function(t){return!t.indegree})))};var At=function(t,e,r){for(var n,o=r;e.length&&(n=e[e.length-1]).i<=o;)e.pop(),null==t||t.push(n.vs),o++;return o},Ot=function(t,e,r,n,o,i){var a,s,u,c,h=t.getChildren(e).map((function(t){return t.id})),f=t.getNode(e),d=f?f.data.borderLeft:void 0,l=f?f.data.borderRight:void 0,v={};d&&(h=null==h?void 0:h.filter((function(t){return t!==d&&t!==l})));var g=function(t,e){return e.map((function(e){var r=t.getRelatedEdges(e,"in");if(!(null==r?void 0:r.length))return{v:e};var n={sum:0,weight:0};return null==r||r.forEach((function(e){var r=t.getNode(e.source);n.sum+=e.data.weight*r.data.order,n.weight+=e.data.weight})),{v:e,barycenter:n.sum/n.weight,weight:n.weight}}))}(t,h||[]);null==g||g.forEach((function(e){var o;if(null===(o=t.getChildren(e.v))||void 0===o?void 0:o.length){var a=Ot(t,e.v,r,n,i);v[e.v]=a,a.hasOwnProperty("barycenter")&&zt(e,a)}}));var p=jt(g,r);St(p,v),null===(a=p.filter((function(t){return t.vs.length>0})))||void 0===a||a.forEach((function(e){var r=t.getNode(e.vs[0]);r&&(e.fixorder=r.data.fixorder,e.order=r.data.order)}));var y=function(t,e,r,n){var o=function(t){var e={lhs:[],rhs:[]};return null==t||t.forEach((function(t){var r,o;o=(r=t).hasOwnProperty("fixorder")&&!isNaN(r.fixorder),(n?!o&&r.hasOwnProperty("barycenter"):o||r.hasOwnProperty("barycenter"))?e.lhs.push(t):e.rhs.push(t)})),e}(t),i=o.lhs,a=o.rhs.sort((function(t,e){return-t.i- -e.i})),s=[],u=0,c=0,h=0;null==i||i.sort(function(t,e){return function(r,n){if(void 0!==r.fixorder&&void 0!==n.fixorder)return r.fixorder-n.fixorder;if(r.barycenter<n.barycenter)return-1;if(r.barycenter>n.barycenter)return 1;if(e&&void 0!==r.order&&void 0!==n.order){if(r.order<n.order)return-1;if(r.order>n.order)return 1}return t?n.i-r.i:r.i-n.i}}(!!e,!!r)),h=At(s,a,h),null==i||i.forEach((function(t){var e;h+=null===(e=t.vs)||void 0===e?void 0:e.length,s.push(t.vs),u+=t.barycenter*t.weight,c+=t.weight,h=At(s,a,h)}));var f={vs:s.flat()};return c&&(f.barycenter=u/c,f.weight=c),f}(p,n,o,i);if(d&&(y.vs=[d,y.vs,l].flat(),null===(s=t.getPredecessors(d))||void 0===s?void 0:s.length)){var m=t.getNode((null===(u=t.getPredecessors(d))||void 0===u?void 0:u[0].id)||""),w=t.getNode((null===(c=t.getPredecessors(l))||void 0===c?void 0:c[0].id)||"");y.hasOwnProperty("barycenter")||(y.barycenter=0,y.weight=0),y.barycenter=(y.barycenter*y.weight+m.data.order+w.data.order)/(y.weight+2),y.weight+=2}return y},St=function(t,e){null==t||t.forEach((function(t){var r,n=null===(r=t.vs)||void 0===r?void 0:r.map((function(t){return e[t]?e[t].vs:t}));t.vs=n.flat()}))},zt=function(t,e){void 0!==t.barycenter?(t.barycenter=(t.barycenter*t.weight+e.barycenter*e.weight)/(t.weight+e.weight),t.weight+=e.weight):(t.barycenter=e.barycenter,t.weight=e.weight)},Rt=function(t,e,r){return e.map((function(e){return function(t,e,r){var n=function(t){for(var e;t.hasNode(e="_root".concat(Math.random())););return e}(t),o=new A({tree:[{id:n,children:[],data:{}}]});return t.getAllNodes().forEach((function(i){var a=t.getParent(i.id);(i.data.rank===e||i.data.minRank<=e&&e<=i.data.maxRank)&&(o.hasNode(i.id)||o.addNode(Object.assign({},i)),(null==a?void 0:a.id)&&!o.hasNode(null==a?void 0:a.id)&&o.addNode(Object.assign({},a)),o.setParent(i.id,(null==a?void 0:a.id)||n),t.getRelatedEdges(i.id,r).forEach((function(e){var r=e.source===i.id?e.target:e.source;o.hasNode(r)||o.addNode(Object.assign({},t.getNode(r)));var n=o.getRelatedEdges(r,"out").find((function(t){return t.target===i.id})),a=void 0!==n?n.data.weight:0;n?o.updateEdgeData(n.id,Object.assign(Object.assign({},n.data),{weight:e.data.weight+a})):o.addEdge({id:e.id,source:r,target:i.id,data:{weight:e.data.weight+a}})})),i.data.hasOwnProperty("minRank")&&o.updateNodeData(i.id,Object.assign(Object.assign({},i.data),{borderLeft:i.data.borderLeft[e],borderRight:i.data.borderRight[e]})))})),o}(t,e,r)}))},It=function(t,e,r,n){var o=new A;null==t||t.forEach((function(t){for(var i,a=t.getRoots()[0].id,s=Ot(t,a,o,e,r,n),u=0;u<(null===(i=s.vs)||void 0===i?void 0:i.length);u++){var c=t.getNode(s.vs[u]);c&&(c.data.order=u)}!function(t,e,r){var n,o={};null==r||r.forEach((function(r){for(var i,a,s=t.getParent(r);s;){if((i=t.getParent(s.id))?(a=o[i.id],o[i.id]=s.id):(a=n,n=s.id),a&&a!==s.id)return e.hasNode(a)||e.addNode({id:a,data:{}}),e.hasNode(s.id)||e.addNode({id:s.id,data:{}}),void(e.hasEdge("e".concat(a,"-").concat(s.id))||e.addEdge({id:"e".concat(a,"-").concat(s.id),source:a,target:s.id,data:{}}));s=i}}))}(t,o,s.vs)}))},Pt=function(t,e){null==e||e.forEach((function(e){null==e||e.forEach((function(e,r){t.getNode(e).data.order=r}))}))},Tt=function(t,e){var r={};function n(e,n,o,i,a){for(var s,u,c,h=n;h<o;h++)c=e[h],(null===(s=t.getNode(c))||void 0===s?void 0:s.data.dummy)&&(null===(u=t.getPredecessors(c))||void 0===u||u.forEach((function(e){var n=t.getNode(e.id);n.data.dummy&&(n.data.order<i||n.data.order>a)&&Dt(r,e.id,c)})))}function o(t,e){var r=function(t){return JSON.stringify(t.slice(1))}(t);e.get(r)||(n.apply(void 0,l(t)),e.set(r,!0))}return(null==e?void 0:e.length)&&e.reduce((function(e,r){var n,i=-1,a=0,s=new Map;return null==r||r.forEach((function(u,c){var h;if("border"===(null===(h=t.getNode(u))||void 0===h?void 0:h.data.dummy)){var f=t.getPredecessors(u)||[];f.length&&(n=t.getNode(f[0].id).data.order,o([r,a,c,i,n],s),a=c,i=n)}o([r,a,r.length,n,e.length],s)})),r})),r},Ct=function(t,e){var r,n;if(null===(r=t.getNode(e))||void 0===r?void 0:r.data.dummy)return null===(n=t.getPredecessors(e))||void 0===n?void 0:n.find((function(e){return t.getNode(e.id).data.dummy}))},Dt=function(t,e,r){var n=e,o=r;if(n>o){var i=n;n=o,o=i}var a=t[n];a||(t[n]=a={}),a[o]=!0},Lt=function(t,e,r){var n=e;return n>r&&(n=r),!!t[n]},Ft=function(t,e,r,n,o,i){var a=new A,s=qt(n,o,i);return null==e||e.forEach((function(e){var n;null==e||e.forEach((function(e){var o=r[e];if(a.hasNode(o)||a.addNode({id:o,data:{}}),n){var i=r[n],u=a.getRelatedEdges(i,"out").find((function(t){return t.target===o}));u?a.updateEdgeData(u.id,Object.assign(Object.assign({},u.data),{weight:Math.max(s(t,e,n),u.data.weight||0)})):a.addEdge({id:"e".concat(Math.random()),source:i,target:o,data:{weight:Math.max(s(t,e,n),0)}})}n=e}))})),a};var qt=function(t,e,r){return function(n,o,i){var a=n.getNode(o),s=n.getNode(i),u=0,c=0;if(u+=a.data.width/2,a.data.hasOwnProperty("labelpos"))switch((a.data.labelpos||"").toLowerCase()){case"l":c=-a.data.width/2;break;case"r":c=a.data.width/2}if(c&&(u+=r?c:-c),c=0,u+=(a.data.dummy?e:t)/2,u+=(s.data.dummy?e:t)/2,u+=s.data.width/2,s.data.labelpos)switch((s.data.labelpos||"").toLowerCase()){case"l":c=s.data.width/2;break;case"r":c=-s.data.width/2}return c&&(u+=r?c:-c),c=0,u}},Vt=function(t,e){return t.getNode(e).data.width||0},Gt=function(t,e){var r,n=it(t);!function(t,e){var r=(e||{}).ranksep,n=void 0===r?0:r,o=st(t),i=0;null==o||o.forEach((function(e){var r=e.map((function(e){return t.getNode(e).data.height})),o=Math.max.apply(Math,l(r).concat([0]));null==e||e.forEach((function(e){t.getNode(e).data.y=i+o/2})),i+=o+n}))}(n,e);var o=function(t,e){var r=e||{},n=r.align,o=r.nodesep,i=void 0===o?0:o,a=r.edgesep,s=void 0===a?0:a,u=st(t),c=Object.assign(function(t,e){var r={};return(null==e?void 0:e.length)&&e.reduce((function(e,n){var o=0,i=0,a=e.length,s=null==n?void 0:n[(null==n?void 0:n.length)-1];return null==n||n.forEach((function(e,u){var c,h=Ct(t,e),f=h?t.getNode(h.id).data.order:a;(h||e===s)&&(null===(c=n.slice(i,u+1))||void 0===c||c.forEach((function(e){var n;null===(n=t.getPredecessors(e))||void 0===n||n.forEach((function(n){var i,a=t.getNode(n.id),s=a.data.order;!(s<o||f<s)||a.data.dummy&&(null===(i=t.getNode(e))||void 0===i?void 0:i.data.dummy)||Dt(r,n.id,e)}))})),i=u+1,o=f)})),n})),r}(t,u),Tt(t,u)),h={},f=[];["u","d"].forEach((function(e){f="u"===e?u:Object.values(u).reverse(),["l","r"].forEach((function(r){"r"===r&&(f=f.map((function(t){return Object.values(t).reverse()})));var n=("u"===e?t.getPredecessors:t.getSuccessors).bind(t),o=function(t,e,r,n){var o={},i={},a={};return null==e||e.forEach((function(t){null==t||t.forEach((function(t,e){o[t]=t,i[t]=t,a[t]=e}))})),null==e||e.forEach((function(t){var e=-1;null==t||t.forEach((function(t){var s=n(t).map((function(t){return t.id}));if(s.length)for(var u=((s=s.sort((function(t,e){return a[t]-a[e]}))).length-1)/2,c=Math.floor(u),h=Math.ceil(u);c<=h;++c){var f=s[c];i[t]===t&&e<a[f]&&!Lt(r,t,f)&&(i[f]=t,i[t]=o[t]=o[f],e=a[f])}}))})),{root:o,align:i}}(0,f,c,n),a=function(t,e,r,n,o,i,a){var s,u={},c=Ft(t,e,r,o,i,a),h=a?"borderLeft":"borderRight",f=function(t,e){for(var r=c.getAllNodes(),n=r.pop(),o={};n;)o[n.id]?t(n.id):(o[n.id]=!0,r.push(n),r=r.concat(e(n.id))),n=r.pop()};return f((function(t){u[t]=(c.getRelatedEdges(t,"in")||[]).reduce((function(t,e){return Math.max(t,(u[e.source]||0)+e.data.weight)}),0)}),c.getPredecessors.bind(c)),f((function(e){var r=(c.getRelatedEdges(e,"out")||[]).reduce((function(t,e){return Math.min(t,(u[e.target]||0)-e.data.weight)}),Number.POSITIVE_INFINITY),n=t.getNode(e);r!==Number.POSITIVE_INFINITY&&n.data.borderType!==h&&(u[e]=Math.max(u[e],r))}),c.getSuccessors.bind(c)),null===(s=Object.values(n))||void 0===s||s.forEach((function(t){u[t]=u[r[t]]})),u}(t,f,o.root,o.align,i,s,"r"===r);"r"===r&&Object.keys(a).forEach((function(t){return a[t]=-a[t]})),h[e+r]=a}))}));var d=function(t,e){return ht(Object.values(e),(function(e){var r,n=Number.NEGATIVE_INFINITY,o=Number.POSITIVE_INFINITY;return null===(r=Object.keys(e))||void 0===r||r.forEach((function(r){var i=e[r],a=Vt(t,r)/2;n=Math.max(i+a,n),o=Math.min(i-a,o)})),n-o}))}(t,h);return d&&function(t,e){var r=Object.values(e),n=Math.min.apply(Math,r),o=Math.max.apply(Math,r);["u","d"].forEach((function(r){["l","r"].forEach((function(i){var a,s=r+i,u=t[s];if(u!==e){var c=Object.values(u);(a="l"===i?n-Math.min.apply(Math,c):o-Math.max.apply(Math,c))&&(t[s]={},Object.keys(u).forEach((function(e){t[s][e]=u[e]+a})))}}))}))}(h,d),function(t,e){var r={};return Object.keys(t.ul).forEach((function(n){if(e)r[n]=t[e.toLowerCase()][n];else{var o=Object.values(t).map((function(t){return t[n]}));r[n]=(o[0]+o[1])/2}})),r}(h,n)}(n,e);null===(r=Object.keys(o))||void 0===r||r.forEach((function(t){n.getNode(t).data.x=o[t]}))},Bt=function(t){var e={},r=function(n){var o,i,a=t.getNode(n);return a?e[n]?a.data.rank:(e[n]=!0,null===(o=t.getRelatedEdges(n,"out"))||void 0===o||o.forEach((function(t){var e=r(t.target)-t.data.minlen;e&&(void 0===i||e<i)&&(i=e)})),i||(i=0),a.data.rank=i,i):0};t.getAllNodes().filter((function(e){return 0===t.getRelatedEdges(e.id,"in").length})).forEach((function(t){return r(t.id)}))},Ut=function(t,e){return t.getNode(e.target).data.rank-t.getNode(e.source).data.rank-e.data.minlen},Wt=function(t,e){var r=function(n){e.getRelatedEdges(n,"both").forEach((function(o){var i=o.source,a=n===i?o.target:i;t.hasNode(a)||Ut(e,o)||(t.addNode({id:a,data:{}}),t.addEdge({id:o.id,source:n,target:a,data:{}}),r(a))}))};return t.getAllNodes().forEach((function(t){return r(t.id)})),t.getAllNodes().length},Yt=function(t,e){var r=function(n){var o;null===(o=e.getRelatedEdges(n,"both"))||void 0===o||o.forEach((function(o){var i=o.source,a=n===i?o.target:i;t.hasNode(a)||void 0===e.getNode(a).data.layer&&Ut(e,o)||(t.addNode({id:a,data:{}}),t.addEdge({id:o.id,source:n,target:a,data:{}}),r(a))}))};return t.getAllNodes().forEach((function(t){return r(t.id)})),t.getAllNodes().length},Ht=function(t,e){return ht(e.getAllEdges(),(function(r){return t.hasNode(r.source)!==t.hasNode(r.target)?Ut(e,r):1/0}))},Kt=function(t,e,r){t.getAllNodes().forEach((function(t){var n=e.getNode(t.id);n.data.rank||(n.data.rank=0),n.data.rank+=r}))},$t=function(t,e){var r=dt(t,t.getAllNodes(),"post",!1);(r=r.slice(0,(null==r?void 0:r.length)-1)).forEach((function(r){Jt(t,e,r)}))},Jt=function(t,e,r){var n=t.getNode(r).data.parent;t.getRelatedEdges(r,"both").find((function(t){return t.target===n||t.source===n})).data.cutvalue=Qt(t,e,r)},Qt=function(t,e,r){var n=t.getNode(r).data.parent,o=!0,i=e.getRelatedEdges(r,"out").find((function(t){return t.target===n})),a=0;return i||(o=!1,i=e.getRelatedEdges(n,"out").find((function(t){return t.target===r}))),a=i.data.weight,e.getRelatedEdges(r,"both").forEach((function(e){var i=e.source===r,s=i?e.target:e.source;if(s!==n){var u=i===o,c=e.data.weight;if(a+=u?c:-c,oe(t,r,s)){var h=t.getRelatedEdges(r,"both").find((function(t){return t.source===s||t.target===s})).data.cutvalue;a+=u?-h:h}}})),a},Xt=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.getAllNodes()[0].id;Zt(t,{},1,e)},Zt=function(t,e,r,n,o){var i,a=r,s=r,u=t.getNode(n);return e[n]=!0,null===(i=t.getNeighbors(n))||void 0===i||i.forEach((function(r){e[r.id]||(s=Zt(t,e,s,r.id,n))})),u.data.low=a,u.data.lim=s++,o?u.data.parent=o:delete u.data.parent,s},te=function(t){return t.getAllEdges().find((function(t){return t.data.cutvalue<0}))},ee=function(t,e,r){var n=r.source,o=r.target;e.getRelatedEdges(n,"out").find((function(t){return t.target===o}))||(n=r.target,o=r.source);var i=t.getNode(n),a=t.getNode(o),s=i,u=!1;i.data.lim>a.data.lim&&(s=a,u=!0);var c=e.getAllEdges().filter((function(e){return u===ie(t.getNode(e.source),s)&&u!==ie(t.getNode(e.target),s)}));return ht(c,(function(t){return Ut(e,t)}))},re=function(t,e,r,n){var o=t.getRelatedEdges(r.source,"both").find((function(t){return t.source===r.target||t.target===r.target}));o&&t.removeEdge(o.id),t.addEdge({id:"e".concat(Math.random()),source:n.source,target:n.target,data:{}}),Xt(t),$t(t,e),ne(t,e)},ne=function(t,e){var r=t.getAllNodes().find((function(t){return!t.data.parent})),n=dt(t,r,"pre",!1);(n=n.slice(1)).forEach((function(r){var n=t.getNode(r).data.parent,o=e.getRelatedEdges(r,"out").find((function(t){return t.target===n})),i=!1;!o&&e.hasNode(n)&&(o=e.getRelatedEdges(n,"out").find((function(t){return t.target===r})),i=!0),e.getNode(r).data.rank=(e.hasNode(n)&&e.getNode(n).data.rank||0)+(i?null==o?void 0:o.data.minlen:-(null==o?void 0:o.data.minlen))}))},oe=function(t,e,r){return t.getRelatedEdges(e,"both").find((function(t){return t.source===r||t.target===r}))},ie=function(t,e){return e.data.low<=t.data.lim&&t.data.lim<=e.data.lim},ae=Bt,se=function(t){!function(t){var e=function(t){var e=new A;return t.getAllNodes().forEach((function(t){e.addNode(Object.assign({},t))})),t.getAllEdges().forEach((function(t){var r=e.getRelatedEdges(t.source,"out").find((function(e){return e.target===t.target}));r?e.updateEdgeData(null==r?void 0:r.id,Object.assign(Object.assign({},r.data),{weight:r.data.weight+t.data.weight||0,minlen:Math.max(r.data.minlen,t.data.minlen||1)})):e.addEdge({id:t.id,source:t.source,target:t.target,data:{weight:t.data.weight||0,minlen:t.data.minlen||1}})})),e}(t);Bt(e);var r,n,o=function(t){var e,r,n=new A({tree:[]}),o=t.getAllNodes()[0],i=t.getAllNodes().length;for(n.addNode(o);Wt(n,t)<i;)e=Ht(n,t),r=n.hasNode(e.source)?Ut(t,e):-Ut(t,e),Kt(n,t,r);return n}(e);for(Xt(o),$t(o,e);r=te(o);)n=ee(o,e,r),re(o,e,r,n)}(t)},ue=function(t,e){var r=e.edgeLabelSpace,n=e.keepNodeOrder,o=e.prevGraph,i=e.rankdir,a=e.ranksep;!n&&o&&he(t,o);var s,u=ye(t);r&&(e.ranksep=me(u,{rankdir:i,ranksep:a}));try{s=ce(u,e)}catch(t){if("Not possible to find intersection inside of the rectangle"===t.message)return void console.error("The following error may be caused by improper layer setting, please make sure your manual layer setting does not violate the graph's structure:\n",t);throw t}return fe(t,u),s},ce=function(t,e){var r=e.acyclicer,n=e.ranker,o=e.rankdir,i=void 0===o?"tb":o,a=e.nodeOrder,s=e.keepNodeOrder,u=e.align,c=e.nodesep,h=void 0===c?50:c,f=e.edgesep,d=void 0===f?20:f,v=e.ranksep,g=void 0===v?50:v;je(t),rt(t,r);var p=function(t){var e=ot(t,"root",{},"_root"),r=function(t){var e={},r=function(n,o){var i=t.getChildren(n);null==i||i.forEach((function(t){return r(t.id,o+1)})),e[n]=o};return t.getRoots().forEach((function(t){return r(t.id,1)})),e}(t),n=Math.max.apply(Math,l(Object.values(r)));Math.abs(n)===1/0&&(n=1);var o=n-1,i=2*o+1;t.getAllEdges().forEach((function(t){t.data.minlen*=i}));var a=function(t){var e=0;return t.getAllEdges().forEach((function(t){e+=t.data.weight})),e}(t)+1;return t.getRoots().forEach((function(n){mt(t,e,i,a,o,r,n.id)})),{nestingRoot:e,nodeRankFactor:i}}(t),y=p.nestingRoot,m=p.nodeRankFactor;!function(t,e){switch(e){case"network-simplex":se(t);break;case"tight-tree":default:!function(t){!function(t){var e,r={},n=function(o){var i,a,s=t.getNode(o);return s?r[o]?s.data.rank:(r[o]=!0,null===(i=t.getRelatedEdges(o,"out"))||void 0===i||i.forEach((function(t){var e=n(t.target)-t.data.minlen;e&&(void 0===a||e<a)&&(a=e)})),a||(a=0),(void 0===e||a<e)&&(e=a),s.data.rank=a,a):0};t.getAllNodes().filter((function(e){return 0===t.getRelatedEdges(e.id,"in").length})).forEach((function(t){t&&n(t.id)})),void 0===e&&(e=0);var o={},i=function(e,r){var n,a=t.getNode(e),s=isNaN(a.data.layer)?r:a.data.layer;(void 0===a.data.rank||a.data.rank<s)&&(a.data.rank=s),o[e]||(o[e]=!0,null===(n=t.getRelatedEdges(e,"out"))||void 0===n||n.forEach((function(t){i(t.target,s+t.data.minlen)})))};t.getAllNodes().forEach((function(t){var r=t.data;r&&(isNaN(r.layer)?r.rank-=e:i(t.id,r.layer))}))}(t),function(t){var e,r,n=new A({tree:[]}),o=t.getAllNodes()[0],i=t.getAllNodes().length;for(n.addNode(o);Yt(n,t)<i;)e=Ht(n,t),r=n.hasNode(e.source)?Ut(t,e):-Ut(t,e),Kt(n,t,r)}(t)}(t);break;case"longest-path":ae(t)}}(it(t),n),we(t),function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=t.getAllNodes(),n=r.filter((function(t){return void 0!==t.data.rank})).map((function(t){return t.data.rank})),o=Math.min.apply(Math,l(n)),i=[];r.forEach((function(t){var e=(t.data.rank||0)-o;i[e]||(i[e]=[]),i[e].push(t.id)}));for(var a=0,s=0;s<i.length;s++){var u=i[s];void 0===u?s%e!==0&&(a-=1):a&&(null==u||u.forEach((function(e){var r=t.getNode(e);r&&(r.data.rank=r.data.rank||0,r.data.rank+=a)})))}}(t,m),function(t,e){e&&t.removeNode(e),t.getAllEdges().forEach((function(e){e.data.nestingEdge&&t.removeEdge(e.id)}))}(t,y),function(t){var e=t.getAllNodes().filter((function(t){return void 0!==t.data.rank})).map((function(t){return t.data.rank})),r=Math.min.apply(Math,l(e));t.getAllNodes().forEach((function(t){t.data.hasOwnProperty("rank")&&r!==1/0&&(t.data.rank-=r)}))}(t),xe(t),be(t);var w=[];!function(t,e){t.getAllEdges().forEach((function(r){return function(t,e,r){var n=e.source,o=t.getNode(n).data.rank,i=e.target,a=t.getNode(i).data.rank,s=e.data.labelRank;if(a!==o+1){var u,c,h;for(t.removeEdge(e.id),h=0,++o;o<a;++h,++o)e.data.points=[],u=ot(t,"edge",c={originalEdge:e,width:0,height:0,rank:o},"_d"),o===s&&(c.width=e.data.width,c.height=e.data.height,c.dummy=wt,c.labelpos=e.data.labelpos),t.addEdge({id:"e".concat(Math.random()),source:n,target:u,data:{weight:e.data.weight}}),0===h&&r.push(u),n=u;t.addEdge({id:"e".concat(Math.random()),source:n,target:i,data:{weight:e.data.weight}})}}(t,r,e)}))}(t,w),function(t,e){var r=function(t){var e={},r=0,n=function(o){var i=r;t.getChildren(o).forEach((function(t){return n(t.id)})),e[o]={low:i,lim:r++}};return t.getRoots().forEach((function(t){return n(t.id)})),e}(t);e.forEach((function(e){var n,o,i=e,a=t.getNode(i),s=a.data.originalEdge;if(s)for(var u=function(t,e,r,n){var o,i,a,s,u=[],c=[],h=Math.min(e[r].low,e[n].low),f=Math.max(e[r].lim,e[n].lim);a=r;do{a=null===(o=t.getParent(a))||void 0===o?void 0:o.id,u.push(a)}while(a&&(e[a].low>h||f>e[a].lim));for(s=a,a=n;a&&a!==s;)c.push(a),a=null===(i=t.getParent(a))||void 0===i?void 0:i.id;return{lca:s,path:u.concat(c.reverse())}}(t,r,s.source,s.target),c=u.path,h=u.lca,f=0,d=c[f],l=!0;i!==s.target;){if(a=t.getNode(i),l){for(;d!==h&&(null===(n=t.getNode(d))||void 0===n?void 0:n.data.maxRank)<a.data.rank;)d=c[++f];d===h&&(l=!1)}if(!l){for(;f<c.length-1&&(null===(o=t.getNode(c[f+1]))||void 0===o?void 0:o.data.minRank)<=a.data.rank;)f++;d=c[f]}t.hasNode(d)&&t.setParent(i,d),i=t.getSuccessors(i)[0].id}}))}(t,w),function(t){var e=function(r){var n=t.getChildren(r),o=t.getNode(r);if((null==n?void 0:n.length)&&n.forEach((function(t){return e(t.id)})),o.data.hasOwnProperty("minRank")){o.data.borderLeft=[],o.data.borderRight=[];for(var i=o.data.minRank,a=o.data.maxRank+1;i<a;i+=1)lt(t,"borderLeft","_bl",r,o,i),lt(t,"borderRight","_br",r,o,i)}};t.getRoots().forEach((function(t){return e(t.id)}))}(t),s&&function(t,e){for(var r=t.getAllNodes().filter((function(e){var r;return!(null===(r=t.getChildren(e.id))||void 0===r?void 0:r.length)})).map((function(t){return t.data.rank})),n=Math.max.apply(Math,l(r)),o=[],i=0;i<n+1;i++)o[i]=[];null==e||e.forEach((function(e){var r=t.getNode(e);r&&!r.data.dummy&&(isNaN(r.data.rank)||(r.data.fixorder=o[r.data.rank].length,o[r.data.rank].push(e)))}))}(t,a),function(t,e){for(var r=ct(t),n=[],o=[],i=1;i<r+1;i++)n.push(i);for(var a=r-1;a>-1;a--)o.push(a);var s=Rt(t,n,"in"),u=Rt(t,o,"out"),c=Nt(t);Pt(t,c);for(var h,f=Number.POSITIVE_INFINITY,d=0,l=0;l<4;++d,++l){It(d%2?s:u,d%4>=2,!1,e),c=st(t);var v=Mt(t,c);v<f&&(l=0,h=Et(c),f=v)}c=Nt(t),Pt(t,c);for(var g=0,p=0;p<4;++g,++p){It(g%2?s:u,g%4>=2,!0,e),c=st(t);var y=Mt(t,c);y<f&&(p=0,h=Et(c),f=y)}Pt(t,h)}(t,s),Ae(t),function(t,e){var r=e.toLowerCase();"lr"!==r&&"rl"!==r||vt(t)}(t,i),Gt(t,{align:u,nodesep:h,edgesep:d,ranksep:g}),Oe(t),_e(t),function(t,e){e.forEach((function(e){var r,n=t.getNode(e),o=n.data.originalEdge;o&&t.addEdge(o);for(var i=e;n.data.dummy;)r=t.getSuccessors(i)[0],t.removeNode(i),o.data.points.push({x:n.data.x,y:n.data.y}),n.data.dummy===wt&&(o.data.x=n.data.x,o.data.y=n.data.y,o.data.width=n.data.width,o.data.height=n.data.height),i=r.id,n=t.getNode(i)}))}(t,w),Me(t),function(t,e){var r=e.toLowerCase();"bt"!==r&&"rl"!==r||function(t){t.getAllNodes().forEach((function(t){pt(t.data)})),t.getAllEdges().forEach((function(t){var e;null===(e=t.data.points)||void 0===e||e.forEach((function(t){return pt(t)})),t.data.hasOwnProperty("y")&&pt(t.data)}))}(t),"lr"!==r&&"rl"!==r||(function(t){t.getAllNodes().forEach((function(t){yt(t.data)})),t.getAllEdges().forEach((function(t){var e;null===(e=t.data.points)||void 0===e||e.forEach((function(t){return yt(t)})),t.data.hasOwnProperty("x")&&yt(t.data)}))}(t),vt(t))}(t,i);var x=Ee(t),b=x.width,E=x.height;return ke(t),Ne(t),function(t){t.getAllEdges().forEach((function(e){var r=e.data;if(r.reversed){t.removeEdge(e.id);var n=r.forwardName;delete r.reversed,delete r.forwardName,t.addEdge({id:e.id,source:e.target,target:e.source,data:Object.assign(Object.assign({},r),{forwardName:n})})}}))}(t),{width:b,height:E}},he=function(t,e){t.getAllNodes().forEach((function(r){var n=t.getNode(r.id);if(e.hasNode(r.id)){var o=e.getNode(r.id);n.data.fixorder=o.data._order,delete o.data._order}else delete n.data.fixorder}))},fe=function(t,e){t.getAllNodes().forEach((function(r){var n,o=t.getNode(r.id);if(o){var i=e.getNode(r.id);o.data.x=i.data.x,o.data.y=i.data.y,o.data._order=i.data.order,o.data._rank=i.data.rank,(null===(n=e.getChildren(r.id))||void 0===n?void 0:n.length)&&(o.data.width=i.data.width,o.data.height=i.data.height)}})),t.getAllEdges().forEach((function(r){var n=t.getEdge(r.id),o=e.getEdge(r.id);n.data.points=o?o.data.points:[],o&&o.data.hasOwnProperty("x")&&(n.data.x=o.data.x,n.data.y=o.data.y)}))},de=["width","height","layer","fixorder"],le={width:0,height:0},ve=["minlen","weight","width","height","labeloffset"],ge={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},pe=["labelpos"],ye=function(t){var e=new A({tree:[]});return t.getAllNodes().forEach((function(r){var n=ze(t.getNode(r.id).data),o=Object.assign(Object.assign({},le),n),i=Se(o,de);e.hasNode(r.id)||e.addNode({id:r.id,data:Object.assign({},i)});var a=t.hasTreeStructure("combo")?t.getParent(r.id,"combo"):t.getParent(r.id);null!=a&&(e.hasNode(a.id)||e.addNode(Object.assign({},a)),e.setParent(r.id,a.id))})),t.getAllEdges().forEach((function(r){var n=ze(t.getEdge(r.id).data),o={};null==pe||pe.forEach((function(t){void 0!==n[t]&&(o[t]=n[t])})),e.addEdge({id:r.id,source:r.source,target:r.target,data:Object.assign({},ge,Se(n,ve),o)})})),e},me=function(t,e){var r=e.ranksep,n=void 0===r?0:r,o=e.rankdir;return t.getAllNodes().forEach((function(t){isNaN(t.data.layer)||t.data.layer||(t.data.layer=0)})),t.getAllEdges().forEach((function(t){var e;t.data.minlen*=2,"c"!==(null===(e=t.data.labelpos)||void 0===e?void 0:e.toLowerCase())&&("TB"===o||"BT"===o?t.data.width+=t.data.labeloffset:t.data.height+=t.data.labeloffset)})),n/2},we=function(t){t.getAllEdges().forEach((function(e){if(e.data.width&&e.data.height){var r=t.getNode(e.source),n={e,rank:(t.getNode(e.target).data.rank-r.data.rank)/2+r.data.rank};ot(t,"edge-proxy",n,"_ep")}}))},xe=function(t){var e=0;return t.getAllNodes().forEach((function(r){var n,o;r.data.borderTop&&(r.data.minRank=null===(n=t.getNode(r.data.borderTop))||void 0===n?void 0:n.data.rank,r.data.maxRank=null===(o=t.getNode(r.data.borderBottom))||void 0===o?void 0:o.data.rank,e=Math.max(e,r.data.maxRank||-1/0))})),e},be=function(t){t.getAllNodes().forEach((function(e){"edge-proxy"===e.data.dummy&&(t.getEdge(e.data.e.id).data.labelRank=e.data.rank,t.removeNode(e.id))}))},Ee=function(t,e){var r,n,o=0,i=0,a=e||{},s=a.marginx,u=void 0===s?0:s,c=a.marginy,h=void 0===c?0:c,f=function(t){if(t.data){var e=t.data.x,a=t.data.y,s=t.data.width,u=t.data.height;isNaN(e)||isNaN(s)||(void 0===r&&(r=e-s/2),r=Math.min(r,e-s/2),o=Math.max(o,e+s/2)),isNaN(a)||isNaN(u)||(void 0===n&&(n=a-u/2),n=Math.min(n,a-u/2),i=Math.max(i,a+u/2))}};return t.getAllNodes().forEach((function(t){f(t)})),t.getAllEdges().forEach((function(t){(null==t?void 0:t.data.hasOwnProperty("x"))&&f(t)})),r-=u,n-=h,t.getAllNodes().forEach((function(t){t.data.x-=r,t.data.y-=n})),t.getAllEdges().forEach((function(t){var e;null===(e=t.data.points)||void 0===e||e.forEach((function(t){t.x-=r,t.y-=n})),t.data.hasOwnProperty("x")&&(t.data.x-=r),t.data.hasOwnProperty("y")&&(t.data.y-=n)})),{width:o-r+u,height:i-n+h}},ke=function(t){t.getAllEdges().forEach((function(e){var r,n,o=t.getNode(e.source),i=t.getNode(e.target);e.data.points?(r=e.data.points[0],n=e.data.points[e.data.points.length-1]):(e.data.points=[],r={x:i.data.x,y:i.data.y},n={x:o.data.x,y:o.data.y}),e.data.points.unshift(at(o.data,r)),e.data.points.push(at(i.data,n))}))},Me=function(t){t.getAllEdges().forEach((function(t){if(t.data.hasOwnProperty("x"))switch("l"!==t.data.labelpos&&"r"!==t.data.labelpos||(t.data.width-=t.data.labeloffset),t.data.labelpos){case"l":t.data.x-=t.data.width/2+t.data.labeloffset;break;case"r":t.data.x+=t.data.width/2+t.data.labeloffset}}))},Ne=function(t){t.getAllEdges().forEach((function(t){var e;t.data.reversed&&(null===(e=t.data.points)||void 0===e||e.reverse())}))},_e=function(t){t.getAllNodes().forEach((function(e){var r,n,o;if(null===(r=t.getChildren(e.id))||void 0===r?void 0:r.length){var i=t.getNode(e.id),a=t.getNode(i.data.borderTop),s=t.getNode(i.data.borderBottom),u=t.getNode(i.data.borderLeft[(null===(n=i.data.borderLeft)||void 0===n?void 0:n.length)-1]),c=t.getNode(i.data.borderRight[(null===(o=i.data.borderRight)||void 0===o?void 0:o.length)-1]);i.data.width=Math.abs((null==c?void 0:c.data.x)-(null==u?void 0:u.data.x))||10,i.data.height=Math.abs((null==s?void 0:s.data.y)-(null==a?void 0:a.data.y))||10,i.data.x=((null==u?void 0:u.data.x)||0)+i.data.width/2,i.data.y=((null==a?void 0:a.data.y)||0)+i.data.height/2}})),t.getAllNodes().forEach((function(e){"border"===e.data.dummy&&t.removeNode(e.id)}))},je=function(t){t.getAllEdges().forEach((function(e){if(e.source===e.target){var r=t.getNode(e.source);r.data.selfEdges||(r.data.selfEdges=[]),r.data.selfEdges.push(e),t.removeEdge(e.id)}}))},Ae=function(t){var e=st(t);null==e||e.forEach((function(e){var r=0;null==e||e.forEach((function(e,n){var o,i=t.getNode(e);i.data.order=n+r,null===(o=i.data.selfEdges)||void 0===o||o.forEach((function(e){ot(t,"selfedge",{width:e.data.width,height:e.data.height,rank:i.data.rank,order:n+ ++r,e},"_se")})),delete i.data.selfEdges}))}))},Oe=function(t){t.getAllNodes().forEach((function(e){var r=t.getNode(e.id);if("selfedge"===r.data.dummy){var n=t.getNode(r.data.e.source),o=n.data.x+n.data.width/2,i=n.data.y,a=r.data.x-o,s=n.data.height/2;t.hasEdge(r.data.e.id)?t.updateEdgeData(r.data.e.id,r.data.e.data):t.addEdge({id:r.data.e.id,source:r.data.e.source,target:r.data.e.target,data:r.data.e.data}),t.removeNode(e.id),r.data.e.data.points=[{x:o+2*a/3,y:i-s},{x:o+5*a/6,y:i-s},{y:i,x:o+a},{x:o+5*a/6,y:i+s},{x:o+2*a/3,y:i+s}],r.data.e.data.x=r.data.x,r.data.e.data.y=r.data.y}}))},Se=function(t,e){var r={};return null==e||e.forEach((function(e){void 0!==t[e]&&(r[e]=+t[e])})),r},ze=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={};return Object.keys(t).forEach((function(r){e[r.toLowerCase()]=t[r]})),e};function Re(t){return"function"==typeof t}const Ie=function(t){var e=typeof t;return null!==t&&"object"===e||"function"===e};function Pe(t){if(!t)return[0,0,0];if($(t))return[t,t,t];if(0===t.length)return[0,0,0];var e=f(t,3),r=e[0],n=e[1],o=void 0===n?r:n,i=e[2];return[r,o,void 0===i?r:i]}function Te(t,e){return Re(e)?e:$(e)?function(){return e}:function(){return t}}function Ce(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return e||0===e?Re(e)?e:$(e)?function(){return e}:Array.isArray(e)?function(){return r?Math.max.apply(Math,l(e))||t:e}:Ie(e)&&e.width&&e.height?function(){return r?Math.max(e.width,e.height)||t:[e.width,e.height]}:function(){return t}:function(e){var n=(e.data||{}).size;return n?Array.isArray(n)?r?Math.max.apply(Math,l(n))||t:n:Ie(n)&&n.width&&n.height?r?Math.max(n.width,n.height)||t:[n.width,n.height]:n:t}}var De=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,o="function"==typeof e?e:function(){return e||0};return r=t?Array.isArray(t)?function(e){return t}:Re(t)?t:function(e){return t}:function(t){var e,r,o;if(null===(e=t.data)||void 0===e?void 0:e.bboxSize)return null===(r=t.data)||void 0===r?void 0:r.bboxSize;if(null===(o=t.data)||void 0===o?void 0:o.size){var i=t.data.size;return Array.isArray(i)?i:Ie(i)?[i.width,i.height]:i}return n},function(t){var e=r(t),n=o(t);return Math.max.apply(Math,l(Pe(e)))+n}},Le=function(t){if(null===t)return t;if(t instanceof Date)return new Date(t.getTime());if(t instanceof Array){var e=[];return t.forEach((function(t){e.push(t)})),e.map((function(t){return Le(t)}))}if("object"==typeof t){var r={};return Object.keys(t).forEach((function(e){r[e]=Le(t[e])})),r}return t},Fe=function(t,e){var r=Le(t);return r.data=r.data||{},e&&($(r.data.x)||(r.data.x=Math.random()*e[0]),$(r.data.y)||(r.data.y=Math.random()*e[1])),r},qe={rankdir:"TB",nodesep:50,ranksep:50,edgeLabelSpace:!0,ranker:"tight-tree",controlPoints:!1,radial:!1,focusNode:null},Ve=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="antv-dagre",this.options=Object.assign(Object.assign({},qe),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericDagreLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericDagreLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericDagreLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,f,d,l,v,g,p,y,m,w,x,b,E,k,M,N,_,j,O,S,z,R,I,P,T,C,D,L,F,q,V,G,B;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=Object.assign(Object.assign({},this.options),n),a=i.nodeSize,s=i.align,u=i.rankdir,c=void 0===u?"TB":u,h=i.ranksep,f=i.nodesep,d=i.ranksepFunc,l=i.nodesepFunc,v=i.edgeLabelSpace,g=i.ranker,p=i.nodeOrder,y=i.begin,m=i.controlPoints,w=i.radial,x=i.sortByCombo,b=i.preset,E=new A({tree:[]}),k=Te(h||50,d),M=Te(f||50,l),N=M,_=k,"LR"!==c&&"RL"!==c||(N=k,_=M),j=Ce(10,a,!1),O=r.getAllNodes(),S=r.getAllEdges(),O.forEach((function(t){var e=Pe(j(t)),r=_(t),n=N(t),o=e[0]+2*n,i=e[1]+2*r,a=t.data.layer;$(a)?E.addNode({id:t.id,data:{width:o,height:i,layer:a}}):E.addNode({id:t.id,data:{width:o,height:i}})})),x&&(E.attachTreeStructure("combo"),O.forEach((function(t){var e=t.data.parentId;void 0!==e&&E.hasNode(e)&&E.setParent(t.id,e,"combo")}))),S.forEach((function(t){E.addEdge({id:t.id,source:t.source,target:t.target,data:{weight:t.data.weight||1}})})),z=void 0,(null==b?void 0:b.length)&&(z=new A({nodes:b})),ue(E,{prevGraph:z,edgeLabelSpace:v,keepNodeOrder:!!p,nodeOrder:p||[],acyclicer:"greedy",ranker:g,rankdir:c,nodesep:f,align:s}),R=[0,0],y&&(I=1/0,P=1/0,E.getAllNodes().forEach((function(t){I>t.data.x&&(I=t.data.x),P>t.data.y&&(P=t.data.y)})),E.getAllEdges().forEach((function(t){var e;null===(e=t.data.points)||void 0===e||e.forEach((function(t){I>t.x&&(I=t.x),P>t.y&&(P=t.y)}))})),R[0]=y[0]-I,R[1]=y[1]-P),T="LR"===c||"RL"===c,w||(C=new Set,D="BT"===c||"RL"===c?function(t,e){return e-t}:function(t,e){return t-e},E.getAllNodes().forEach((function(t){t.data.x=t.data.x+R[0],t.data.y=t.data.y+R[1],C.add(T?t.data.x:t.data.y)})),L=Array.from(C).sort(D),F=T?function(t,e){return t.x!==e.x}:function(t,e){return t.y!==e.y},q=T?function(t,e,r){var n=Math.max(e.y,r.y),o=Math.min(e.y,r.y);return t.filter((function(t){return t.y<=n&&t.y>=o}))}:function(t,e,r){var n=Math.max(e.x,r.x),o=Math.min(e.x,r.x);return t.filter((function(t){return t.x<=n&&t.x>=o}))},E.getAllEdges().forEach((function(t,e){var r;v&&m&&"loop"!==t.data.type&&(t.data.controlPoints=Ge(null===(r=t.data.points)||void 0===r?void 0:r.map((function(t){var e=t.x,r=t.y;return{x:e+R[0],y:r+R[1]}})),E.getNode(t.source),E.getNode(t.target),L,T,F,q))}))),V=[],V=E.getAllNodes().map((function(t){return Fe(t)})),G=E.getAllEdges(),t&&(V.forEach((function(t){r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y})})),G.forEach((function(t){r.mergeEdgeData(t.id,{controlPoints:t.data.controlPoints})}))),B={nodes:V,edges:G},e.abrupt("return",B);case 27:case"end":return e.stop()}}),o,this)})))}}])}(),Ge=function(t,e,r,n,o,i,a){var s=(null==t?void 0:t.slice(1,t.length-1))||[];if(e&&r){var u=e.data,c=u.x,h=u.y,f=r.data,d=f.x,l=f.y;if(o&&(c=e.data.y,h=e.data.x,d=r.data.y,l=r.data.x),l!==h&&c!==d){var v=n.indexOf(h),g=n[v+1];if(g){var p=s[0],y=o?{x:(h+g)/2,y:(null==p?void 0:p.y)||d}:{x:(null==p?void 0:p.x)||d,y:(h+g)/2};p&&!i(p,y)||s.unshift(y)}var m=n.indexOf(l),w=Math.abs(m-v);if(1===w)(s=a(s,e.data,r.data)).length||s.push(o?{x:(h+l)/2,y:c}:{x:c,y:(h+l)/2});else if(w>1){var x=n[m-1];if(x){var b=s[s.length-1],E=o?{x:(l+x)/2,y:(null==b?void 0:b.y)||d}:{x:(null==b?void 0:b.x)||c,y:(l+x)/2};b&&!i(b,E)||s.push(E)}}}}return s},Be=function(t,e,r){var n=t.getAllNodes(),o=t.getAllEdges();return(null==n?void 0:n.length)?1===n.length?(e&&t.mergeNodeData(n[0].id,{x:r[0],y:r[1]}),{nodes:[Object.assign(Object.assign({},n[0]),{data:Object.assign(Object.assign({},n[0].data),{x:r[0],y:r[1]})})],edges:o}):void 0:{nodes:[],edges:o}},Ue={radius:null,startRadius:null,endRadius:null,startAngle:0,endAngle:2*Math.PI,clockwise:!0,divisions:1,ordering:null,angleRatio:1},We=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="circular",this.options=Object.assign(Object.assign({},Ue),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericCircularLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericCircularLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericCircularLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,d,l,v,g,p,y,m,w,x,b,E,k,M,N,_,j,A,O,S,z,R,I,P,T,C,D,L,F,q,V,G;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=Object.assign(Object.assign({},this.options),n),a=i.width,s=i.height,u=i.center,c=i.divisions,h=i.startAngle,d=void 0===h?0:h,l=i.endAngle,v=void 0===l?2*Math.PI:l,g=i.angleRatio,p=i.ordering,y=i.clockwise,m=i.nodeSpacing,w=i.nodeSize,x=r.getAllNodes(),b=r.getAllEdges(),E=Ke(a,s,u),k=f(E,3),M=k[0],N=k[1],_=k[2],(j=null==x?void 0:x.length)&&1!==j){e.next=8;break}return e.abrupt("return",Be(r,t,_));case 8:for(A=(v-d)/j,O=i.radius,S=i.startRadius,z=i.endRadius,m?(R=Te(10,m),I=Ce(10,w),P=-1/0,x.forEach((function(t){var e=I(t);P<e&&(P=e)})),T=0,x.forEach((function(t,e){T+=0===e?P||10:(R(t)||0)+(P||10)})),O=T/(2*Math.PI)):O||S||z?!S&&z?S=z:S&&!z&&(z=S):O=Math.min(N,M)/2,C=A*g,D=[],D="topology"===p?Ye(r,x):"topology-directed"===p?Ye(r,x,!0):"degree"===p?He(r,x):x.map((function(t){return Fe(t)})),L=Math.ceil(j/c),F=0;F<j;++F)(q=O)||null===S||null===z||(q=S+F*(z-S)/(j-1)),q||(q=10+100*F/(j-1)),V=d+F%L*C+2*Math.PI/c*Math.floor(F/L),y||(V=v-F%L*C-2*Math.PI/c*Math.floor(F/L)),D[F].data.x=_[0]+Math.cos(V)*q,D[F].data.y=_[1]+Math.sin(V)*q;return t&&D.forEach((function(t){r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y})})),G={nodes:D,edges:b},e.abrupt("return",G);case 19:case"end":return e.stop()}}),o,this)})))}}])}(),Ye=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=[Fe(e[0])],o={},i=e.length;o[e[0].id]=!0;var a=0;return e.forEach((function(s,u){if(0!==u)if(u!==i-1&&t.getDegree(s.id,"both")===t.getDegree(e[u+1].id,"both")&&!t.areNeighbors(n[a].id,s.id)||o[s.id]){for(var c=r?t.getSuccessors(n[a].id):t.getNeighbors(n[a].id),h=!1,f=0;f<c.length;f++){var d=c[f];if(t.getDegree(d.id)===t.getDegree(s.id)&&!o[d.id]){n.push(Fe(d)),o[d.id]=!0,h=!0;break}}for(var l=0;!h&&(o[e[l].id]||(n.push(Fe(e[l])),o[e[l].id]=!0,h=!0),++l!==i););}else n.push(Fe(s)),o[s.id]=!0,a++})),n};function He(t,e){var r=[];return e.forEach((function(t,e){r.push(Fe(t))})),r.sort((function(e,r){return t.getDegree(e.id,"both")-t.getDegree(r.id,"both")})),r}var Ke=function(t,e,r){var n=t,o=e,i=r;return n||"undefined"==typeof window||(n=window.innerWidth),o||"undefined"==typeof window||(o=window.innerHeight),i||(i=[n/2,o/2]),[n,o,i]};function $e(t){return"string"==typeof t}var Je=Array.isArray,Qe={nodeSize:30,nodeSpacing:10,preventOverlap:!1,sweep:void 0,equidistant:!1,startAngle:1.5*Math.PI,clockwise:!0,maxLevelDiff:void 0,sortBy:"degree"},Xe=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="concentric",this.options=Object.assign(Object.assign({},Qe),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericConcentricLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericConcentricLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericConcentricLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,f,d,v,g,p,y,m,w,x,b,E,k,M,N,_,j,A,O,S,z,R,I,P,T,C,D,L,F,q,V,G,B,U;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=Object.assign(Object.assign({},this.options),n),a=i.center,s=i.width,u=i.height,c=i.sortBy,h=i.maxLevelDiff,f=i.sweep,d=i.clockwise,v=i.equidistant,g=i.preventOverlap,p=i.startAngle,y=void 0===p?1.5*Math.PI:p,m=i.nodeSize,w=i.nodeSpacing,x=r.getAllNodes(),b=r.getAllEdges(),E=s||"undefined"==typeof window?s:window.innerWidth,k=u||"undefined"==typeof window?u:window.innerHeight,M=a||[E/2,k/2],(null==x?void 0:x.length)&&1!==x.length){e.next=9;break}return e.abrupt("return",Be(r,t,M));case 9:if(N=[],j=0,Je(m)?_=Math.max(m[0],m[1]):Re(m)?(_=-1/0,x.forEach((function(t){var e=Math.max.apply(Math,l(Pe(m(t))));e>_&&(_=e)}))):_=m,Je(w)?j=Math.max(w[0],w[1]):$(w)&&(j=w),x.forEach((function(t){var e=Fe(t);N.push(e);var r=_,n=e.data;Je(n.size)?r=Math.max(n.size[0],n.size[1]):$(n.size)?r=n.size:Ie(n.size)&&(r=Math.max(n.size.width,n.size.height)),_=Math.max(_,r),Re(w)&&(j=Math.max(w(t),j))})),A={},N.forEach((function(t,e){A[t.id]=e})),$e(O=c)&&void 0!==N[0].data[O]||(O="degree"),"degree"===O?N.sort((function(t,e){return r.getDegree(e.id,"both")-r.getDegree(t.id,"both")})):N.sort((function(t,e){return e.data[O]-t.data[O]})),S=N[0],z=(h||("degree"===O?r.getDegree(S.id,"both"):S.data[O]))/4,I=(R=[{nodes:[]}])[0],N.forEach((function(t){if(I.nodes.length>0){var e="degree"===O?Math.abs(r.getDegree(I.nodes[0].id,"both")-r.getDegree(t.id,"both")):Math.abs(I.nodes[0].data[O]-t.data[O]);z&&e>=z&&(I={nodes:[]},R.push(I))}I.nodes.push(t)})),P=_+j,g||(T=R.length>0&&R[0].nodes.length>1,C=Math.min(E,k)/2-P,D=C/(R.length+(T?1:0)),P=Math.min(P,D)),L=0,R.forEach((function(t){var e=void 0===f?2*Math.PI-2*Math.PI/t.nodes.length:f;if(t.dTheta=e/Math.max(1,t.nodes.length-1),t.nodes.length>1&&g){var r=Math.cos(t.dTheta)-Math.cos(0),n=Math.sin(t.dTheta)-Math.sin(0),o=Math.sqrt(P*P/(r*r+n*n));L=Math.max(o,L)}t.r=L,L+=P})),v){for(F=0,q=0,V=0;V<R.length;V++)G=R[V],B=(G.r||0)-q,F=Math.max(F,B);q=0,R.forEach((function(t,e){0===e&&(q=t.r||0),t.r=q,q+=F}))}return R.forEach((function(t){var e=t.dTheta||0,r=t.r||0;t.nodes.forEach((function(t,n){var o=y+(d?1:-1)*e*n;t.data.x=M[0]+r*Math.cos(o),t.data.y=M[1]+r*Math.sin(o)}))})),t&&N.forEach((function(t){return r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y})})),U={nodes:N,edges:b},e.abrupt("return",U);case 33:case"end":return e.stop()}}),o,this)})))}}])}();function Ze(t,e,r,n){if(isNaN(e)||isNaN(r))return t;var o,i,a,s,u,c,h,f,d,l=t._root,v={data:n},g=t._x0,p=t._y0,y=t._x1,m=t._y1;if(!l)return t._root=v,t;for(;l.length;)if((c=e>=(i=(g+y)/2))?g=i:y=i,(h=r>=(a=(p+m)/2))?p=a:m=a,o=l,!(l=l[f=h<<1|c]))return o[f]=v,t;if(s=+t._x.call(null,l.data),u=+t._y.call(null,l.data),e===s&&r===u)return v.next=l,o?o[f]=v:t._root=v,t;do{o=o?o[f]=new Array(4):t._root=new Array(4),(c=e>=(i=(g+y)/2))?g=i:y=i,(h=r>=(a=(p+m)/2))?p=a:m=a}while((f=h<<1|c)==(d=(u>=a)<<1|s>=i));return o[d]=l,o[f]=v,t}function tr(t,e,r,n,o){this.node=t,this.x0=e,this.y0=r,this.x1=n,this.y1=o}function er(t){return t[0]}function rr(t){return t[1]}function nr(t,e,r){var n=new or(null==e?er:e,null==r?rr:r,NaN,NaN,NaN,NaN);return null==t?n:n.addAll(t)}function or(t,e,r,n,o,i){this._x=t,this._y=e,this._x0=r,this._y0=n,this._x1=o,this._y1=i,this._root=void 0}function ir(t){for(var e={data:t.data},r=e;t=t.next;)r=r.next={data:t.data};return e}var ar=nr.prototype=or.prototype;function sr(t,e,r,n,o){if(isNaN(e)||isNaN(r)||isNaN(n))return t;var i,a,s,u,c,h,f,d,l,v,g,p,y=t._root,m={data:o},w=t._x0,x=t._y0,b=t._z0,E=t._x1,k=t._y1,M=t._z1;if(!y)return t._root=m,t;for(;y.length;)if((d=e>=(a=(w+E)/2))?w=a:E=a,(l=r>=(s=(x+k)/2))?x=s:k=s,(v=n>=(u=(b+M)/2))?b=u:M=u,i=y,!(y=y[g=v<<2|l<<1|d]))return i[g]=m,t;if(c=+t._x.call(null,y.data),h=+t._y.call(null,y.data),f=+t._z.call(null,y.data),e===c&&r===h&&n===f)return m.next=y,i?i[g]=m:t._root=m,t;do{i=i?i[g]=new Array(8):t._root=new Array(8),(d=e>=(a=(w+E)/2))?w=a:E=a,(l=r>=(s=(x+k)/2))?x=s:k=s,(v=n>=(u=(b+M)/2))?b=u:M=u}while((g=v<<2|l<<1|d)==(p=(f>=u)<<2|(h>=s)<<1|c>=a));return i[p]=y,i[g]=m,t}function ur(t,e,r,n,o,i,a){this.node=t,this.x0=e,this.y0=r,this.z0=n,this.x1=o,this.y1=i,this.z1=a}ar.copy=function(){var t,e,r=new or(this._x,this._y,this._x0,this._y0,this._x1,this._y1),n=this._root;if(!n)return r;if(!n.length)return r._root=ir(n),r;for(t=[{source:n,target:r._root=new Array(4)}];n=t.pop();)for(var o=0;o<4;++o)(e=n.source[o])&&(e.length?t.push({source:e,target:n.target[o]=new Array(4)}):n.target[o]=ir(e));return r},ar.add=function(t){var e=+this._x.call(null,t),r=+this._y.call(null,t);return Ze(this.cover(e,r),e,r,t)},ar.addAll=function(t){var e,r,n,o,i=t.length,a=new Array(i),s=new Array(i),u=1/0,c=1/0,h=-1/0,f=-1/0;for(r=0;r<i;++r)isNaN(n=+this._x.call(null,e=t[r]))||isNaN(o=+this._y.call(null,e))||(a[r]=n,s[r]=o,n<u&&(u=n),n>h&&(h=n),o<c&&(c=o),o>f&&(f=o));if(u>h||c>f)return this;for(this.cover(u,c).cover(h,f),r=0;r<i;++r)Ze(this,a[r],s[r],t[r]);return this},ar.cover=function(t,e){if(isNaN(t=+t)||isNaN(e=+e))return this;var r=this._x0,n=this._y0,o=this._x1,i=this._y1;if(isNaN(r))o=(r=Math.floor(t))+1,i=(n=Math.floor(e))+1;else{for(var a,s,u=o-r||1,c=this._root;r>t||t>=o||n>e||e>=i;)switch(s=(e<n)<<1|t<r,(a=new Array(4))[s]=c,c=a,u*=2,s){case 0:o=r+u,i=n+u;break;case 1:r=o-u,i=n+u;break;case 2:o=r+u,n=i-u;break;case 3:r=o-u,n=i-u}this._root&&this._root.length&&(this._root=c)}return this._x0=r,this._y0=n,this._x1=o,this._y1=i,this},ar.data=function(){var t=[];return this.visit((function(e){if(!e.length)do{t.push(e.data)}while(e=e.next)})),t},ar.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},ar.find=function(t,e,r){var n,o,i,a,s,u,c,h=this._x0,f=this._y0,d=this._x1,l=this._y1,v=[],g=this._root;for(g&&v.push(new tr(g,h,f,d,l)),null==r?r=1/0:(h=t-r,f=e-r,d=t+r,l=e+r,r*=r);u=v.pop();)if(!(!(g=u.node)||(o=u.x0)>d||(i=u.y0)>l||(a=u.x1)<h||(s=u.y1)<f))if(g.length){var p=(o+a)/2,y=(i+s)/2;v.push(new tr(g[3],p,y,a,s),new tr(g[2],o,y,p,s),new tr(g[1],p,i,a,y),new tr(g[0],o,i,p,y)),(c=(e>=y)<<1|t>=p)&&(u=v[v.length-1],v[v.length-1]=v[v.length-1-c],v[v.length-1-c]=u)}else{var m=t-+this._x.call(null,g.data),w=e-+this._y.call(null,g.data),x=m*m+w*w;if(x<r){var b=Math.sqrt(r=x);h=t-b,f=e-b,d=t+b,l=e+b,n=g.data}}return n},ar.remove=function(t){if(isNaN(i=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t)))return this;var e,r,n,o,i,a,s,u,c,h,f,d,l=this._root,v=this._x0,g=this._y0,p=this._x1,y=this._y1;if(!l)return this;if(l.length)for(;;){if((c=i>=(s=(v+p)/2))?v=s:p=s,(h=a>=(u=(g+y)/2))?g=u:y=u,e=l,!(l=l[f=h<<1|c]))return this;if(!l.length)break;(e[f+1&3]||e[f+2&3]||e[f+3&3])&&(r=e,d=f)}for(;l.data!==t;)if(n=l,!(l=l.next))return this;return(o=l.next)&&delete l.next,n?(o?n.next=o:delete n.next,this):e?(o?e[f]=o:delete e[f],(l=e[0]||e[1]||e[2]||e[3])&&l===(e[3]||e[2]||e[1]||e[0])&&!l.length&&(r?r[d]=l:this._root=l),this):(this._root=o,this)},ar.removeAll=function(t){for(var e=0,r=t.length;e<r;++e)this.remove(t[e]);return this},ar.root=function(){return this._root},ar.size=function(){var t=0;return this.visit((function(e){if(!e.length)do{++t}while(e=e.next)})),t},ar.visit=function(t){var e,r,n,o,i,a,s=[],u=this._root;for(u&&s.push(new tr(u,this._x0,this._y0,this._x1,this._y1));e=s.pop();)if(!t(u=e.node,n=e.x0,o=e.y0,i=e.x1,a=e.y1)&&u.length){var c=(n+i)/2,h=(o+a)/2;(r=u[3])&&s.push(new tr(r,c,h,i,a)),(r=u[2])&&s.push(new tr(r,n,h,c,a)),(r=u[1])&&s.push(new tr(r,c,o,i,h)),(r=u[0])&&s.push(new tr(r,n,o,c,h))}return this},ar.visitAfter=function(t){var e,r=[],n=[];for(this._root&&r.push(new tr(this._root,this._x0,this._y0,this._x1,this._y1));e=r.pop();){var o=e.node;if(o.length){var i,a=e.x0,s=e.y0,u=e.x1,c=e.y1,h=(a+u)/2,f=(s+c)/2;(i=o[0])&&r.push(new tr(i,a,s,h,f)),(i=o[1])&&r.push(new tr(i,h,s,u,f)),(i=o[2])&&r.push(new tr(i,a,f,h,c)),(i=o[3])&&r.push(new tr(i,h,f,u,c))}n.push(e)}for(;e=n.pop();)t(e.node,e.x0,e.y0,e.x1,e.y1);return this},ar.x=function(t){return arguments.length?(this._x=t,this):this._x},ar.y=function(t){return arguments.length?(this._y=t,this):this._y};var cr=function(t,e,r,n,o,i){return Math.sqrt(Math.pow(t-n,2)+Math.pow(e-o,2)+Math.pow(r-i,2))};function hr(t){return t[0]}function fr(t){return t[1]}function dr(t){return t[2]}function lr(t,e,r,n){var o=new vr(null==e?hr:e,null==r?fr:r,null==n?dr:n,NaN,NaN,NaN,NaN,NaN,NaN);return null==t?o:o.addAll(t)}function vr(t,e,r,n,o,i,a,s,u){this._x=t,this._y=e,this._z=r,this._x0=n,this._y0=o,this._z0=i,this._x1=a,this._y1=s,this._z1=u,this._root=void 0}function gr(t){for(var e={data:t.data},r=e;t=t.next;)r=r.next={data:t.data};return e}var pr=lr.prototype=vr.prototype;function yr(t){var e=0,r=0,n=0,o=0,i=0,a=t.length;if(a){for(var s=0;s<a;s++){var u=t[s];u&&u.weight&&(e+=u.weight,r+=u.x*u.weight,n+=u.y*u.weight,o+=u.z*u.weight,i+=u.size*u.weight)}t.x=r/e,t.y=n/e,t.z=o/e,t.size=i/e,t.weight=e}else{var c=t;t.x=c.data.x,t.y=c.data.y,t.z=c.data.z,t.size=c.data.size,t.weight=c.data.weight}}pr.copy=function(){var t,e,r=new vr(this._x,this._y,this._z,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1),n=this._root;if(!n)return r;if(!n.length)return r._root=gr(n),r;for(t=[{source:n,target:r._root=new Array(8)}];n=t.pop();)for(var o=0;o<8;++o)(e=n.source[o])&&(e.length?t.push({source:e,target:n.target[o]=new Array(8)}):n.target[o]=gr(e));return r},pr.add=function(t){var e=+this._x.call(null,t),r=+this._y.call(null,t),n=+this._z.call(null,t);return sr(this.cover(e,r,n),e,r,n,t)},pr.addAll=function(t){Array.isArray(t)||(t=Array.from(t));for(var e,r,n,o,i=t.length,a=new Float64Array(i),s=new Float64Array(i),u=new Float64Array(i),c=1/0,h=1/0,f=1/0,d=-1/0,l=-1/0,v=-1/0,g=0;g<i;++g)isNaN(r=+this._x.call(null,e=t[g]))||isNaN(n=+this._y.call(null,e))||isNaN(o=+this._z.call(null,e))||(a[g]=r,s[g]=n,u[g]=o,r<c&&(c=r),r>d&&(d=r),n<h&&(h=n),n>l&&(l=n),o<f&&(f=o),o>v&&(v=o));if(c>d||h>l||f>v)return this;this.cover(c,h,f).cover(d,l,v);for(var p=0;p<i;++p)sr(this,a[p],s[p],u[p],t[p]);return this},pr.cover=function(t,e,r){if(isNaN(t=+t)||isNaN(e=+e)||isNaN(r=+r))return this;var n=this._x0,o=this._y0,i=this._z0,a=this._x1,s=this._y1,u=this._z1;if(isNaN(n))a=(n=Math.floor(t))+1,s=(o=Math.floor(e))+1,u=(i=Math.floor(r))+1;else{for(var c,h,f=a-n||1,d=this._root;n>t||t>=a||o>e||e>=s||i>r||r>=u;)switch(h=(r<i)<<2|(e<o)<<1|t<n,(c=new Array(8))[h]=d,d=c,f*=2,h){case 0:a=n+f,s=o+f,u=i+f;break;case 1:n=a-f,s=o+f,u=i+f;break;case 2:a=n+f,o=s-f,u=i+f;break;case 3:n=a-f,o=s-f,u=i+f;break;case 4:a=n+f,s=o+f,i=u-f;break;case 5:n=a-f,s=o+f,i=u-f;break;case 6:a=n+f,o=s-f,i=u-f;break;case 7:n=a-f,o=s-f,i=u-f}this._root&&this._root.length&&(this._root=d)}return this._x0=n,this._y0=o,this._z0=i,this._x1=a,this._y1=s,this._z1=u,this},pr.data=function(){var t=[];return this.visit((function(e){if(!e.length)do{t.push(e.data)}while(e=e.next)})),t},pr.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1],+t[0][2]).cover(+t[1][0],+t[1][1],+t[1][2]):isNaN(this._x0)?void 0:[[this._x0,this._y0,this._z0],[this._x1,this._y1,this._z1]]},pr.find=function(t,e,r,n){var o,i,a,s,u,c,h,f,d,l=this._x0,v=this._y0,g=this._z0,p=this._x1,y=this._y1,m=this._z1,w=[],x=this._root;for(x&&w.push(new ur(x,l,v,g,p,y,m)),null==n?n=1/0:(l=t-n,v=e-n,g=r-n,p=t+n,y=e+n,m=r+n,n*=n);f=w.pop();)if(!(!(x=f.node)||(i=f.x0)>p||(a=f.y0)>y||(s=f.z0)>m||(u=f.x1)<l||(c=f.y1)<v||(h=f.z1)<g))if(x.length){var b=(i+u)/2,E=(a+c)/2,k=(s+h)/2;w.push(new ur(x[7],b,E,k,u,c,h),new ur(x[6],i,E,k,b,c,h),new ur(x[5],b,a,k,u,E,h),new ur(x[4],i,a,k,b,E,h),new ur(x[3],b,E,s,u,c,k),new ur(x[2],i,E,s,b,c,k),new ur(x[1],b,a,s,u,E,k),new ur(x[0],i,a,s,b,E,k)),(d=(r>=k)<<2|(e>=E)<<1|t>=b)&&(f=w[w.length-1],w[w.length-1]=w[w.length-1-d],w[w.length-1-d]=f)}else{var M=t-+this._x.call(null,x.data),N=e-+this._y.call(null,x.data),_=r-+this._z.call(null,x.data),j=M*M+N*N+_*_;if(j<n){var A=Math.sqrt(n=j);l=t-A,v=e-A,g=r-A,p=t+A,y=e+A,m=r+A,o=x.data}}return o},pr.findAllWithinRadius=function(t,e,r,n){var o=this,i=[],a=t-n,s=e-n,u=r-n,c=t+n,h=e+n,f=r+n;return this.visit((function(d,l,v,g,p,y,m){if(!d.length)do{var w=d.data;cr(t,e,r,o._x(w),o._y(w),o._z(w))<=n&&i.push(w)}while(d=d.next);return l>c||v>h||g>f||p<a||y<s||m<u})),i},pr.remove=function(t){if(isNaN(i=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t))||isNaN(s=+this._z.call(null,t)))return this;var e,r,n,o,i,a,s,u,c,h,f,d,l,v,g,p=this._root,y=this._x0,m=this._y0,w=this._z0,x=this._x1,b=this._y1,E=this._z1;if(!p)return this;if(p.length)for(;;){if((f=i>=(u=(y+x)/2))?y=u:x=u,(d=a>=(c=(m+b)/2))?m=c:b=c,(l=s>=(h=(w+E)/2))?w=h:E=h,e=p,!(p=p[v=l<<2|d<<1|f]))return this;if(!p.length)break;(e[v+1&7]||e[v+2&7]||e[v+3&7]||e[v+4&7]||e[v+5&7]||e[v+6&7]||e[v+7&7])&&(r=e,g=v)}for(;p.data!==t;)if(n=p,!(p=p.next))return this;return(o=p.next)&&delete p.next,n?(o?n.next=o:delete n.next,this):e?(o?e[v]=o:delete e[v],(p=e[0]||e[1]||e[2]||e[3]||e[4]||e[5]||e[6]||e[7])&&p===(e[7]||e[6]||e[5]||e[4]||e[3]||e[2]||e[1]||e[0])&&!p.length&&(r?r[g]=p:this._root=p),this):(this._root=o,this)},pr.removeAll=function(t){for(var e=0,r=t.length;e<r;++e)this.remove(t[e]);return this},pr.root=function(){return this._root},pr.size=function(){var t=0;return this.visit((function(e){if(!e.length)do{++t}while(e=e.next)})),t},pr.visit=function(t){var e,r,n,o,i,a,s,u,c=[],h=this._root;for(h&&c.push(new ur(h,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));e=c.pop();)if(!t(h=e.node,n=e.x0,o=e.y0,i=e.z0,a=e.x1,s=e.y1,u=e.z1)&&h.length){var f=(n+a)/2,d=(o+s)/2,l=(i+u)/2;(r=h[7])&&c.push(new ur(r,f,d,l,a,s,u)),(r=h[6])&&c.push(new ur(r,n,d,l,f,s,u)),(r=h[5])&&c.push(new ur(r,f,o,l,a,d,u)),(r=h[4])&&c.push(new ur(r,n,o,l,f,d,u)),(r=h[3])&&c.push(new ur(r,f,d,i,a,s,l)),(r=h[2])&&c.push(new ur(r,n,d,i,f,s,l)),(r=h[1])&&c.push(new ur(r,f,o,i,a,d,l)),(r=h[0])&&c.push(new ur(r,n,o,i,f,d,l))}return this},pr.visitAfter=function(t){var e,r=[],n=[];for(this._root&&r.push(new ur(this._root,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));e=r.pop();){var o=e.node;if(o.length){var i,a=e.x0,s=e.y0,u=e.z0,c=e.x1,h=e.y1,f=e.z1,d=(a+c)/2,l=(s+h)/2,v=(u+f)/2;(i=o[0])&&r.push(new ur(i,a,s,u,d,l,v)),(i=o[1])&&r.push(new ur(i,d,s,u,c,l,v)),(i=o[2])&&r.push(new ur(i,a,l,u,d,h,v)),(i=o[3])&&r.push(new ur(i,d,l,u,c,h,v)),(i=o[4])&&r.push(new ur(i,a,s,v,d,l,f)),(i=o[5])&&r.push(new ur(i,d,s,v,c,l,f)),(i=o[6])&&r.push(new ur(i,a,l,v,d,h,f)),(i=o[7])&&r.push(new ur(i,d,l,v,c,h,f))}n.push(e)}for(;e=n.pop();)t(e.node,e.x0,e.y0,e.z0,e.x1,e.y1,e.z1);return this},pr.x=function(t){return arguments.length?(this._x=t,this):this._x},pr.y=function(t){return arguments.length?(this._y=t,this):this._y},pr.z=function(t){return arguments.length?(this._z=t,this):this._z};var mr=function(t,e,r,n,o,i,a){var s;if((null===(s=t.data)||void 0===s?void 0:s.id)!==i.id){for(var u=[r,n,o][a-1],c=i.x-t.x||.1,h=i.y-t.y||.1,f=i.z-t.z||.1,d=[c,h,f],l=u-e,v=0,g=0;g<a;g++)v+=d[g]*d[g];var p=Math.sqrt(v)*v;if(l*l*.81<v){var y=t.weight/p;return i.vx+=c*y,i.vy+=h*y,i.vz+=f*y,!0}if(t.length)return!1;if(t.data!==i){var m=t.data.weight/p;i.vx+=c*m,i.vy+=h*m,i.vz+=f*m}}},wr={dimensions:2,maxIteration:500,gravity:10,factor:1,edgeStrength:50,nodeStrength:1e3,coulombDisScale:.005,damping:.9,maxSpeed:200,minMovement:.4,interval:.02,linkDistance:200,clusterNodeStrength:20,preventOverlap:!0,distanceThresholdMode:"mean"},xr=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="force",this.timeInterval=0,this.judgingDistance=0,this.running=!1,this.options=Object.assign(Object.assign({},wr),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericForceLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericForceLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"stop",value:function(){this.timeInterval&&"undefined"!=typeof window&&window.clearInterval(this.timeInterval),this.running=!1}},{key:"tick",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.maxIteration||1;if(this.lastResult)return this.lastResult;for(var r=0;(this.judgingDistance>this.lastOptions.minMovement||r<1)&&r<e;r++)this.runOneStep(this.lastCalcGraph,this.lastGraph,r,this.lastVelMap,this.lastOptions),this.updatePosition(this.lastGraph,this.lastCalcGraph,this.lastVelMap,this.lastOptions);var n={nodes:this.lastLayoutNodes,edges:this.lastLayoutEdges};return this.lastAssign&&n.nodes.forEach((function(e){return t.lastGraph.mergeNodeData(e.id,{x:e.data.x,y:e.data.y,z:3===t.options.dimensions?e.data.z:void 0})})),n}},{key:"genericForceLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,f,d,l,v,g,p,y,m,w,x,b,E,k,M,N=this;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=Object.assign(Object.assign({},this.options),n),a=r.getAllNodes(),s=r.getAllEdges(),u=this.formatOptions(i,r),c=u.dimensions,h=u.width,f=u.height,d=u.nodeSize,l=u.getMass,v=u.nodeStrength,g=u.edgeStrength,p=u.linkDistance,y=a.map((function(t,e){return Object.assign(Object.assign({},t),{data:Object.assign(Object.assign({},t.data),{x:$(t.data.x)?t.data.x:Math.random()*h,y:$(t.data.y)?t.data.y:Math.random()*f,z:$(t.data.z)?t.data.z:Math.random()*Math.sqrt(h*f),size:d(t)||30,mass:l(t),nodeStrength:v(t)})})})),m=s.map((function(t){return Object.assign(Object.assign({},t),{data:Object.assign(Object.assign({},t.data),{edgeStrength:g(t),linkDistance:p(t,r.getNode(t.source),r.getNode(t.target))})})})),null==a?void 0:a.length){e.next=10;break}return this.lastResult={nodes:[],edges:s},e.abrupt("return",{nodes:[],edges:s});case 10:if(w={},a.forEach((function(t,e){w[t.id]={x:0,y:0,z:0}})),x=new A({nodes:y,edges:m}),this.formatCentripetal(u,x),b=u.maxIteration,E=u.minMovement,k=u.onTick,this.lastLayoutNodes=y,this.lastLayoutEdges=m,this.lastAssign=t,this.lastGraph=r,this.lastCalcGraph=x,this.lastOptions=u,this.lastVelMap=w,"undefined"!=typeof window){e.next=24;break}return e.abrupt("return");case 24:return M=0,e.abrupt("return",new Promise((function(e){N.timeInterval=window.setInterval((function(){a&&N.running||e({nodes:Nr(r,y),edges:s}),N.runOneStep(x,r,M,w,u),N.updatePosition(r,x,w,u),t&&y.forEach((function(t){return r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y,z:3===c?t.data.z:void 0})})),null==k||k({nodes:Nr(r,y),edges:s}),(++M>=b||N.judgingDistance<E)&&(window.clearInterval(N.timeInterval),e({nodes:Nr(r,y),edges:s}))}),0),N.running=!0})));case 26:case"end":return e.stop()}}),o,this)})))}},{key:"formatOptions",value:function(t,e){var r=Object.assign({},t),n=t.width,o=t.height,i=t.getMass;r.width=n||"undefined"==typeof window?n:window.innerWidth,r.height=o||"undefined"==typeof window?o:window.innerHeight,t.center||(r.center=[r.width/2,r.height/2]),i||(r.getMass=function(t){var r=1;$(null==t?void 0:t.data.mass)&&(r=null==t?void 0:t.data.mass);var n=e.getDegree(t.id,"both");return!n||n<5?r:5*n*r}),r.nodeSize=De(t.nodeSize,t.nodeSpacing);var a=t.linkDistance?Te(1,t.linkDistance):function(t){return 1+r.nodeSize(e.getNode(t.source))+r.nodeSize(e.getNode(t.target))};return r.linkDistance=a,r.nodeStrength=Te(1,t.nodeStrength),r.edgeStrength=Te(1,t.edgeStrength),r}},{key:"formatCentripetal",value:function(t,e){var r,n,o=t.dimensions,i=t.centripetalOptions,a=t.center,s=t.clusterNodeStrength,u=t.leafCluster,c=t.clustering,h=t.nodeClusterBy,f=e.getAllNodes(),d=i||{leaf:2,single:2,others:1,center:function(t){return{x:a[0],y:a[1],z:3===o?a[2]:void 0}}};if("function"!=typeof s&&(t.clusterNodeStrength=function(t){return s}),u&&h&&(r=br(e,h),n=Array.from(new Set(null==f?void 0:f.map((function(t){return t.data[h]}))))||[],t.centripetalOptions=Object.assign(d,{single:100,leaf:function(e){var o=r[e.id]||{},i=o.siblingLeaves,a=o.sameTypeLeaves;return(null==a?void 0:a.length)===(null==i?void 0:i.length)||1===(null==n?void 0:n.length)?1:t.clusterNodeStrength(e)},others:1,center:function(t){var n,o=e.getDegree(t.id,"both");if(!o)return{x:100,y:100,z:0};if(1===o){var i=(r[t.id]||{}).sameTypeLeaves,a=void 0===i?[]:i;1===a.length?n=void 0:a.length>1&&(n=Mr(a))}else n=void 0;return{x:null==n?void 0:n.x,y:null==n?void 0:n.y,z:null==n?void 0:n.z}}})),c&&h){r||(r=br(e,h)),n||(n=Array.from(new Set(f.map((function(t){return t.data[h]}))))),n=n.filter((function(t){return void 0!==t}));var l={};n.forEach((function(t){var r=f.filter((function(e){return e.data[h]===t})).map((function(t){return e.getNode(t.id)}));l[t]=Mr(r)})),t.centripetalOptions=Object.assign(d,{single:function(e){return t.clusterNodeStrength(e)},leaf:function(e){return t.clusterNodeStrength(e)},others:function(e){return t.clusterNodeStrength(e)},center:function(t){var e=l[t.data[h]];return{x:null==e?void 0:e.x,y:null==e?void 0:e.y,z:null==e?void 0:e.z}}})}var v=t.centripetalOptions||{},g=v.leaf,p=v.single,y=v.others;g&&"function"!=typeof g&&(t.centripetalOptions.leaf=function(){return g}),p&&"function"!=typeof p&&(t.centripetalOptions.single=function(){return p}),y&&"function"!=typeof y&&(t.centripetalOptions.others=function(){return y})}},{key:"runOneStep",value:function(t,e,r,n,o){var i={},a=t.getAllNodes(),s=t.getAllEdges();if(null==a?void 0:a.length){var u=o.monitor;this.calRepulsive(t,i,o),s&&this.calAttractive(t,i,o),this.calGravity(t,e,i,o),this.updateVelocity(t,i,n,o),u&&u({energy:this.calTotalEnergy(i,a),nodes:e.getAllNodes(),edges:e.getAllEdges(),iterations:r})}}},{key:"calTotalEnergy",value:function(t,e){var r=this;if(!(null==e?void 0:e.length))return 0;var n=0;return e.forEach((function(e,o){var i=t[e.id].x,a=t[e.id].y,s=3===r.options.dimensions?t[e.id].z:0,u=i*i+a*a+s*s,c=e.data.mass;n+=(void 0===c?1:c)*u*.5})),n}},{key:"calRepulsive",value:function(t,e,r){var n=r.dimensions,o=r.factor,i=r.coulombDisScale;!function(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:2,i=e/r,a=t.getAllNodes(),s=a.map((function(t,e){var r=t.data,n=r.nodeStrength;return{x:r.x,y:r.y,z:r.z,size:r.size,index:e,id:t.id,vx:0,vy:0,vz:0,weight:i*n}})),u=(2===o?nr(s,(function(t){return t.x}),(function(t){return t.y})):lr(s,(function(t){return t.x}),(function(t){return t.y}),(function(t){return t.z}))).visitAfter(yr),c=new Map;s.forEach((function(t){c.set(t.id,t),function(t,e,r){e.visit((function(e,n,o,i,a){return mr(e,n,o,i,a,t,r)}))}(t,u,o)})),s.map((function(t,e){var r=a[e],o=r.id,i=r.data.mass,s=void 0===i?1:i;n[o]={x:t.vx/s,y:t.vy/s,z:t.vz/s}}))}(t,o,i*i,e,n)}},{key:"calAttractive",value:function(t,e,r){var n=r.dimensions,o=r.nodeSize;t.getAllEdges().forEach((function(r,i){var a=r.source,s=r.target,u=t.getNode(a),c=t.getNode(s);if(u&&c){var h=c.data.x-u.data.x,f=c.data.y-u.data.y,d=3===n?c.data.z-u.data.z:0;h||f||(h=.01*Math.random(),f=.01*Math.random(),3!==n||d||(d=.01*Math.random()));var l=Math.sqrt(h*h+f*f+d*d);if(!(l<o(u)+o(c))){var v=h/l,g=f/l,p=d/l,y=r.data||{},m=y.linkDistance,w=void 0===m?200:m,x=y.edgeStrength,b=(w-l)*(void 0===x?200:x),E=1/(u.data.mass||1),k=1/(c.data.mass||1),M=v*b,N=g*b,_=p*b;e[a].x-=M*E,e[a].y-=N*E,e[a].z-=_*E,e[s].x+=M*k,e[s].y+=N*k,e[s].z+=_*k}}}))}},{key:"calGravity",value:function(t,e,r,n){var o=n.getCenter,i=t.getAllNodes(),a=e.getAllNodes(),s=e.getAllEdges(),u=n.width,c=n.height,h=n.center,d=n.gravity,l=n.centripetalOptions;i&&i.forEach((function(n){var i=n.id,v=n.data,g=v.mass,p=v.x,y=v.y,m=v.z,w=e.getNode(i),x=0,b=0,E=0,k=d,M=t.getDegree(i,"in"),N=t.getDegree(i,"out"),_=t.getDegree(i,"both"),j=null==o?void 0:o(w,_);if(j){var A=f(j,3);x=p-A[0],b=y-A[1],k=A[2]}else x=p-h[0],b=y-h[1],E=m-h[2];if(k&&(r[i].x-=k*x/g,r[i].y-=k*b/g,r[i].z-=k*E/g),l){var O=l.leaf,S=l.single,z=l.others,R=l.center,I=(null==R?void 0:R(w,a,s,u,c))||{x:0,y:0,z:0,centerStrength:0},P=I.x,T=I.y,C=I.z,D=I.centerStrength;if(!$(P)||!$(T))return;var L=(p-P)/g,F=(y-T)/g,q=(m-C)/g;if(D&&(r[i].x-=D*L,r[i].y-=D*F,r[i].z-=D*q),0===_){var V=S(w);if(!V)return;return r[i].x-=V*L,r[i].y-=V*F,void(r[i].z-=V*q)}if(0===M||0===N){var G=O(w,a,s);if(!G)return;return r[i].x-=G*L,r[i].y-=G*F,void(r[i].z-=G*q)}var B=z(w);if(!B)return;r[i].x-=B*L,r[i].y-=B*F,r[i].z-=B*q}}))}},{key:"updateVelocity",value:function(t,e,r,n){var o=n.damping,i=n.maxSpeed,a=n.interval,s=n.dimensions,u=t.getAllNodes();(null==u?void 0:u.length)&&u.forEach((function(t){var n=t.id,u=(r[n].x+e[n].x*a)*o||.01,c=(r[n].y+e[n].y*a)*o||.01,h=3===s?(r[n].z+e[n].z*a)*o||.01:0,f=Math.sqrt(u*u+c*c+h*h);if(f>i){var d=i/f;u*=d,c*=d,h*=d}r[n]={x:u,y:c,z:h}}))}},{key:"updatePosition",value:function(t,e,r,n){var o=this,i=n.distanceThresholdMode,a=n.interval,s=n.dimensions,u=e.getAllNodes();if(null==u?void 0:u.length){var c=0;"max"===i?this.judgingDistance=-1/0:"min"===i&&(this.judgingDistance=1/0),u.forEach((function(n){var u=n.id,h=t.getNode(u);if($(h.data.fx)&&$(h.data.fy))e.mergeNodeData(u,{x:h.data.fx,y:h.data.fy,z:3===s?h.data.fz:void 0});else{var f=r[u].x*a,d=r[u].y*a,l=3===s?r[u].z*a:0;e.mergeNodeData(u,{x:n.data.x+f,y:n.data.y+d,z:n.data.z+l});var v=Math.sqrt(f*f+d*d+l*l);switch(i){case"max":o.judgingDistance<v&&(o.judgingDistance=v);break;case"min":o.judgingDistance>v&&(o.judgingDistance=v);break;default:c+=v}}})),i&&"mean"!==i||(this.judgingDistance=c/u.length)}else this.judgingDistance=0}}])}(),br=function(t,e){var r=t.getAllNodes();if(!(null==r?void 0:r.length))return{};var n={};return r.forEach((function(r,o){1===t.getDegree(r.id,"both")&&(n[r.id]=Er(t,"leaf",r,e))})),n},Er=function(t,e,r,n){var o=t.getDegree(r.id,"in"),i=t.getDegree(r.id,"out"),a=r,s=[];return 0===o?(a=t.getSuccessors(r.id)[0],s=t.getNeighbors(a.id)):0===i&&(a=t.getPredecessors(r.id)[0],s=t.getNeighbors(a.id)),s=s.filter((function(e){return 0===t.getDegree(e.id,"in")||0===t.getDegree(e.id,"out")})),{coreNode:a,siblingLeaves:s,sameTypeLeaves:kr(t,e,n,r,s)}},kr=function(t,e,r,n,o){var i=n.data[r]||"",a=(null==o?void 0:o.filter((function(t){return t.data[r]===i})))||[];return"leaf"===e&&(a=a.filter((function(e){return 0===t.getDegree(e.id,"in")||0===t.getDegree(e.id,"out")}))),a},Mr=function(t){var e={x:0,y:0};t.forEach((function(t){var r=t.data,n=r.x,o=r.y;e.x+=n||0,e.y+=o||0}));var r=t.length||1;return{x:e.x/r,y:e.y/r}},Nr=function(t,e){return e.map((function(e){var r=e.id,n=e.data,o=t.getNode(r);return Object.assign(Object.assign({},o),{data:Object.assign(Object.assign({},o.data),{x:n.x,y:n.y,z:n.z})})}))},_r=r(6727),jr=(_r.y3,_r.jy,_r.oN,_r.Hc,_r.cg,_r.hj,_r.LU,_r.Tb,_r.uq),Ar=(_r.Zm,_r.Dq,_r.__,_r.q0,_r.lh,_r.pI,_r.zC,_r.zg,_r.g6,_r.OL,_r.ks,_r.QR,_r.jp,_r.mk,_r.W2);_r.l,_r.KY,_r.dv,_r.BR,_r.Wu,_r.uq,_r.uq,_r.a4,_r.DI,_r.Jo,_r.Zi,_r.kH,_r.LV;var Or=function(t){for(var e=[],r=t.length,n=0;n<r;n+=1){e[n]=[];for(var o=0;o<r;o+=1)n===o?e[n][o]=0:0!==t[n][o]&&t[n][o]?e[n][o]=t[n][o]:e[n][o]=1/0}for(var i=0;i<r;i+=1)for(var a=0;a<r;a+=1)for(var s=0;s<r;s+=1)e[a][s]>e[a][i]+e[i][s]&&(e[a][s]=e[a][i]+e[i][s]);return e},Sr=function(t,e){var r=t.nodes,n=t.edges,o=[],i={};if(!r)throw new Error("invalid nodes data!");return r&&r.forEach((function(t,e){i[t.id]=e,o.push([])})),null==n||n.forEach((function(t){var r=t.source,n=t.target,a=i[r],s=i[n];void 0!==a&&void 0!==s&&(o[a][s]=1,e||(o[s][a]=1))})),o},zr=function(t,e){var r=[];return t.forEach((function(t){var n=[];t.forEach((function(t){n.push(t*e)})),r.push(n)})),r},Rr=function(t){var e=1/0,r=1/0,n=-1/0,o=-1/0;return t.forEach((function(t){var i=t.data.size;Je(i)?1===i.length&&(i=[i[0],i[0]]):$(i)?i=[i,i]:(void 0===i||isNaN(i))&&(i=[30,30]);var a=[i[0]/2,i[1]/2],s=t.data.x-a[0],u=t.data.x+a[0],c=t.data.y-a[1],h=t.data.y+a[1];e>s&&(e=s),r>c&&(r=c),n<u&&(n=u),o<h&&(o=h)})),{minX:e,minY:r,maxX:n,maxY:o}},Ir=function(t,e){return Math.sqrt((t.x-e.x)*(t.x-e.x)+(t.y-e.y)*(t.y-e.y))},Pr=function(t,e,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"TB",o=arguments.length>4?arguments[4]:void 0,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{};if(null==e?void 0:e.length)for(var a=i.stopBranchFn,s=i.stopAllFn,u=0;u<e.length;u++){var c=e[u];if(t.hasNode(c.id)&&!(null==a?void 0:a(c))){if(null==s?void 0:s(c))return;"TB"===n&&r(c),Pr(t,t.getChildren(c.id,o),r,n,o,i),"TB"!==n&&r(c)}}},Tr={center:[0,0],linkDistance:50},Cr=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="mds",this.options=Object.assign(Object.assign({},Tr),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericMDSLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericMDSLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericMDSLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,f,d,l,v,g,p,y;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=Object.assign(Object.assign({},this.options),n),a=i.center,s=void 0===a?[0,0]:a,u=i.linkDistance,c=void 0===u?50:u,h=r.getAllNodes(),f=r.getAllEdges(),(null==h?void 0:h.length)&&1!==h.length){e.next=6;break}return e.abrupt("return",Be(r,t,s));case 6:return d=Sr({nodes:h,edges:f},!1),l=Or(d),Dr(l),v=zr(l,c),g=Lr(v),p=[],g.forEach((function(t,e){var r=Fe(h[e]);r.data.x=t[0]+s[0],r.data.y=t[1]+s[1],p.push(r)})),t&&p.forEach((function(t){return r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y})})),y={nodes:p,edges:f},e.abrupt("return",y);case 16:case"end":return e.stop()}}),o,this)})))}}])}(),Dr=function(t){var e=-999999;t.forEach((function(t){t.forEach((function(t){t!==1/0&&e<t&&(e=t)}))})),t.forEach((function(r,n){r.forEach((function(r,o){r===1/0&&(t[n][o]=e)}))}))},Lr=function(t){var e=jr.mul(jr.pow(t,2),-.5),r=e.mean("row"),n=e.mean("column"),o=e.mean();e.add(o).subRowVector(r).subColumnVector(n);var i=new Ar(e),a=jr.sqrt(i.diagonalMatrix).diagonal();return i.leftSingularVectors.toJSON().map((function(t){return jr.mul([t],[a]).toJSON()[0].splice(0,2)}))};function Fr(t){return!!t.tick&&!!t.stop}var qr={gForce:!0,force2:!0,d3force:!0,fruchterman:!0,forceAtlas2:!0,force:!0,"graphin-force":!0},Vr={center:[0,0],comboPadding:10,treeKey:"combo"},Gr=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="comboCombined",this.options=Object.assign(Object.assign({},Vr),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericComboCombinedLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericComboCombinedLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericComboCombinedLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,f,d,v,g,p,y,m,w,x,b,E,k,M,N,_,j,O,S;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=this.initVals(Object.assign(Object.assign({},this.options),n)),a=i.center,s=i.treeKey,u=i.outerLayout,c=r.getAllNodes().filter((function(t){return!t.data._isCombo})),h=r.getAllNodes().filter((function(t){return t.data._isCombo})),f=r.getAllEdges(),(d=null==c?void 0:c.length)&&1!==d){e.next=8;break}return e.abrupt("return",Be(r,t,a));case 8:return v=[],g=new Map,c.forEach((function(t){g.set(t.id,t)})),p=new Map,h.forEach((function(t){p.set(t.id,t)})),y=new Map,m=this.getInnerGraphs(r,s,g,p,f,i,y),e.next=17,Promise.all(m);case 17:if(w=new Map,x=[],b=new Map,E=!0,r.getRoots(s).forEach((function(t){var e=y.get(t.id),n=p.get(t.id)||g.get(t.id),o={id:t.id,data:Object.assign(Object.assign({},t.data),{x:e.data.x||n.data.x,y:e.data.y||n.data.y,fx:e.data.fx||n.data.fx,fy:e.data.fy||n.data.fy,mass:e.data.mass||n.data.mass,size:e.data.size})};x.push(o),w.set(t.id,!0),isNaN(o.data.x)||0===o.data.x||isNaN(o.data.y)||0===o.data.y?(o.data.x=100*Math.random(),o.data.y=100*Math.random()):E=!1,Pr(r,[t],(function(e){e.id!==t.id&&b.set(e.id,t.id)}),"TB",s)})),k=[],f.forEach((function(t){var e=b.get(t.source)||t.source,r=b.get(t.target)||t.target;e!==r&&w.has(e)&&w.has(r)&&k.push({id:t.id,source:e,target:r,data:{}})})),!(null==x?void 0:x.length)){e.next=42;break}if(1!==x.length){e.next=30;break}x[0].data.x=a[0],x[0].data.y=a[1],e.next=40;break;case 30:if(N=new A({nodes:x,edges:k}),_=u||new xr,!E||!qr[_.id]){e.next=36;break}return j=x.length<100?new Cr:new Xe,e.next=36,j.assign(N);case 36:return O=Object.assign({center:a,kg:5,preventOverlap:!0,animate:!1},"force"===_.id?{gravity:1,factor:4,linkDistance:function(t,e,r){return(Math.max.apply(Math,l(e.data.size))||32)/2+(Math.max.apply(Math,l(r.data.size))||32)/2+200}}:{}),e.next=39,Br(_,N,O);case 39:M=e.sent;case 40:y.forEach((function(t){var e,r=M.nodes.find((function(e){return e.id===t.id}));if(r){var n=r.data,o=n.x,i=n.y;t.data.visited=!0,t.data.x=o,t.data.y=i,v.push({id:t.id,data:{x:o,y:i}})}var a=t.data,s=a.x,u=a.y;null===(e=t.data.nodes)||void 0===e||e.forEach((function(t){v.push({id:t.id,data:{x:t.data.x+s,y:t.data.y+u}})}))})),y.forEach((function(t){var e=t.data,r=e.x,n=e.y,o=e.visited,i=e.nodes;null==i||i.forEach((function(t){if(!o){var e=v.find((function(e){return e.id===t.id}));e.data.x+=r||0,e.data.y+=n||0}}))}));case 42:return t&&v.forEach((function(t){r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y})})),S={nodes:v,edges:f},e.abrupt("return",S);case 45:case"end":return e.stop()}}),o,this)})))}},{key:"initVals",value:function(t){var e,r,n,o=Object.assign({},t),i=t.nodeSize,a=t.spacing,s=t.comboPadding;if(r=$(a)?function(){return a}:Re(a)?a:function(){return 0},o.spacing=r,i)if(Re(i))e=function(t){var e=i(t),n=r(t);return Je(t.size)?((t.size[0]>t.size[1]?t.size[0]:t.size[1])+n)/2:((e||32)+n)/2};else if(Je(i)){var u=(i[0]>i[1]?i[0]:i[1])/2;e=function(t){return u+r(t)/2}}else{var c=i/2;e=function(t){return c+r(t)/2}}else e=function(t){var e=r(t);return t.size?Je(t.size)?((t.size[0]>t.size[1]?t.size[0]:t.size[1])+e)/2:Ie(t.size)?((t.size.width>t.size.height?t.size.width:t.size.height)+e)/2:(t.size+e)/2:32+e/2};return o.nodeSize=e,n=$(s)?function(){return s}:Je(s)?function(){return Math.max.apply(null,s)}:Re(s)?s:function(){return 0},o.comboPadding=n,o}},{key:"getInnerGraphs",value:function(t,r,n,i,a,s,u){var c=this,h=s.nodeSize,d=s.comboPadding,v=s.spacing,g=s.innerLayout||new Xe({}),p={center:[0,0],preventOverlap:!0,nodeSpacing:v},y=[],m=function(t){var e=(null==d?void 0:d(t))||10;return Je(e)&&(e=Math.max.apply(Math,l(e))),{size:e?[2*e,2*e]:[30,30],padding:e}};return t.getRoots(r).forEach((function(s){u.set(s.id,{id:s.id,data:{nodes:[],size:m(s).size}});var d=Promise.resolve();Pr(t,[s],(function(s){var l;if(s.data._isCombo){var v=m(s),y=v.size,w=v.padding;if(null===(l=t.getChildren(s.id,r))||void 0===l?void 0:l.length){var x=u.get(s.id);u.set(s.id,{id:s.id,data:Object.assign({nodes:[]},null==x?void 0:x.data)});var b=new Map,E=t.getChildren(s.id,r).map((function(t){if(t.data._isCombo)return u.has(t.id)||u.set(t.id,{id:t.id,data:Object.assign({},t.data)}),b.set(t.id,!0),u.get(t.id);var e=n.get(t.id)||i.get(t.id);return b.set(t.id,!0),{id:t.id,data:Object.assign(Object.assign({},e.data),t.data)}})),k={nodes:E,edges:a.filter((function(t){return b.has(t.source)&&b.has(t.target)}))},M=1/0;E.forEach((function(t){var e,r=t.data.size;r||(r=(null===(e=u.get(t.id))||void 0===e?void 0:e.data.size)||(null==h?void 0:h(t))||[30,30]),$(r)&&(r=[r,r]);var n=f(r,2),o=n[0],i=n[1];M>o&&(M=o),M>i&&(M=i),t.data.size=r})),d=d.then((function(){return o(c,void 0,void 0,e().mark((function t(){var r,n,o,i,a,c,h,f;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=new A(k),t.next=3,Br(g,r,p,!0);case 3:n=Rr(E),o=n.minX,i=n.minY,a=n.maxX,c=n.maxY,h={x:(a+o)/2,y:(c+i)/2},k.nodes.forEach((function(t){t.data.x-=h.x,t.data.y-=h.y})),f=[Math.max(a-o,M)+2*w,Math.max(c-i,M)+2*w],u.get(s.id).data.size=f,u.get(s.id).data.nodes=E;case 9:case"end":return t.stop()}}),t)})))}))}else u.set(s.id,{id:s.id,data:Object.assign(Object.assign({},s.data),{size:y})});return!0}}),"BT",r),y.push(d)})),y}}])}();function Br(t,r,n,i){var a;return o(this,void 0,void 0,e().mark((function o(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Fr(t)){e.next=4;break}return t.execute(r,n),t.stop(),e.abrupt("return",t.tick(null!==(a=n.iterations)&&void 0!==a?a:300));case 4:if(!i){e.next=8;break}return e.next=7,t.assign(r,n);case 7:case 10:return e.abrupt("return",e.sent);case 8:return e.next=10,t.execute(r,n);case 11:case"end":return e.stop()}}),o)})))}var Ur={}.toString;const Wr=function(t){if(!function(t){return"object"==typeof t&&null!==t}(t)||!function(t,e){return Ur.call(t)==="[object "+e+"]"}(t,"Object"))return!1;if(null===Object.getPrototypeOf(t))return!0;for(var e=t;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e};function Yr(t,e){if(Object.hasOwn)return Object.hasOwn(t,e);if(null==t)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(t),e)}function Hr(t,e,r,n){for(var o in r=r||0,n=n||5,e)if(Yr(e,o)){var i=e[o];null!==i&&Wr(i)?(Wr(t[o])||(t[o]={}),r<n?Hr(t[o],i,r+1,n):t[o]=e[o]):xt(i)?(t[o]=[],t[o]=t[o].concat(i)):void 0!==i&&(t[o]=i)}}const Kr=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];for(var n=0;n<e.length;n+=1)Hr(t,e[n]);return t};var $r=Object.prototype.hasOwnProperty;const Jr=function(t,e){if(null===t||!Wr(t))return{};var r={};return function(t,e){if(t)if(xt(t))for(var r=0,n=t.length;r<n&&!1!==e(t[r],r);r++);else if(Ie(t))for(var o in t)if(t.hasOwnProperty(o)&&!1===e(t[o],o))break}(e,(function(e){$r.call(t,e)&&(r[e]=t[e])})),r};function Qr(t){return function(){return t}}function Xr(t){return 1e-6*(t()-.5)}function Zr(t){return t.index}function tn(t,e){var r=t.get(e);if(!r)throw new Error("node not found: "+e);return r}function en(t){var e,r,n,o,i,a,s=Zr,u=function(t){return 1/Math.min(o[t.source.index],o[t.target.index])},c=Qr(30),h=1;function f(n){for(var o=0,s=t.length;o<h;++o)for(var u,c,f,d,l,v,g,p=0;p<s;++p)c=(u=t[p]).source,d=(f=u.target).x+f.vx-c.x-c.vx||Xr(a),l=f.y+f.vy-c.y-c.vy||Xr(a),d*=v=((v=Math.sqrt(d*d+l*l))-r[p])/v*n*e[p],l*=v,f.vx-=d*(g=i[p]),f.vy-=l*g,c.vx+=d*(g=1-g),c.vy+=l*g}function d(){if(n){var a,u,c=n.length,h=t.length,f=new Map(n.map((function(t,e){return[s(t,e,n),t]})));for(a=0,o=new Array(c);a<h;++a)(u=t[a]).index=a,"object"!=typeof u.source&&(u.source=tn(f,u.source)),"object"!=typeof u.target&&(u.target=tn(f,u.target)),o[u.source.index]=(o[u.source.index]||0)+1,o[u.target.index]=(o[u.target.index]||0)+1;for(a=0,i=new Array(h);a<h;++a)u=t[a],i[a]=o[u.source.index]/(o[u.source.index]+o[u.target.index]);e=new Array(h),l(),r=new Array(h),v()}}function l(){if(n)for(var r=0,o=t.length;r<o;++r)e[r]=+u(t[r],r,t)}function v(){if(n)for(var e=0,o=t.length;e<o;++e)r[e]=+c(t[e],e,t)}return null==t&&(t=[]),f.initialize=function(t,e){n=t,a=e,d()},f.links=function(e){return arguments.length?(t=e,d(),f):t},f.id=function(t){return arguments.length?(s=t,f):s},f.iterations=function(t){return arguments.length?(h=+t,f):h},f.strength=function(t){return arguments.length?(u="function"==typeof t?t:Qr(+t),l(),f):u},f.distance=function(t){return arguments.length?(c="function"==typeof t?t:Qr(+t),v(),f):c},f}var rn={value:function(){}};function nn(){for(var t,e=0,r=arguments.length,n={};e<r;++e){if(!(t=arguments[e]+"")||t in n||/[\s.]/.test(t))throw new Error("illegal type: "+t);n[t]=[]}return new on(n)}function on(t){this._=t}function an(t,e){for(var r,n=0,o=t.length;n<o;++n)if((r=t[n]).name===e)return r.value}function sn(t,e,r){for(var n=0,o=t.length;n<o;++n)if(t[n].name===e){t[n]=rn,t=t.slice(0,n).concat(t.slice(n+1));break}return null!=r&&t.push({name:e,value:r}),t}on.prototype=nn.prototype={constructor:on,on:function(t,e){var r,n,o=this._,i=(n=o,(t+"").trim().split(/^|\s+/).map((function(t){var e="",r=t.indexOf(".");if(r>=0&&(e=t.slice(r+1),t=t.slice(0,r)),t&&!n.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))),a=-1,s=i.length;if(!(arguments.length<2)){if(null!=e&&"function"!=typeof e)throw new Error("invalid callback: "+e);for(;++a<s;)if(r=(t=i[a]).type)o[r]=sn(o[r],t.name,e);else if(null==e)for(r in o)o[r]=sn(o[r],t.name,null);return this}for(;++a<s;)if((r=(t=i[a]).type)&&(r=an(o[r],t.name)))return r},copy:function(){var t={},e=this._;for(var r in e)t[r]=e[r].slice();return new on(t)},call:function(t,e){if((r=arguments.length-2)>0)for(var r,n,o=new Array(r),i=0;i<r;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(i=0,r=(n=this._[t]).length;i<r;++i)n[i].value.apply(e,o)},apply:function(t,e,r){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var n=this._[t],o=0,i=n.length;o<i;++o)n[o].value.apply(e,r)}};const un=nn;var cn,hn,fn=0,dn=0,ln=0,vn=0,gn=0,pn=0,yn="object"==typeof performance&&performance.now?performance:Date,mn="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function wn(){return gn||(mn(xn),gn=yn.now()+pn)}function xn(){gn=0}function bn(){this._call=this._time=this._next=null}function En(t,e,r){var n=new bn;return n.restart(t,e,r),n}function kn(){gn=(vn=yn.now())+pn,fn=dn=0;try{!function(){wn(),++fn;for(var t,e=cn;e;)(t=gn-e._time)>=0&&e._call.call(void 0,t),e=e._next;--fn}()}finally{fn=0,function(){for(var t,e,r=cn,n=1/0;r;)r._call?(n>r._time&&(n=r._time),t=r,r=r._next):(e=r._next,r._next=null,r=t?t._next=e:cn=e);hn=t,Nn(n)}(),gn=0}}function Mn(){var t=yn.now(),e=t-vn;e>1e3&&(pn-=e,vn=t)}function Nn(t){fn||(dn&&(dn=clearTimeout(dn)),t-gn>24?(t<1/0&&(dn=setTimeout(kn,t-yn.now()-pn)),ln&&(ln=clearInterval(ln))):(ln||(vn=yn.now(),ln=setInterval(Mn,1e3)),fn=1,mn(kn)))}bn.prototype=En.prototype={constructor:bn,restart:function(t,e,r){if("function"!=typeof t)throw new TypeError("callback is not a function");r=(null==r?wn():+r)+(null==e?0:+e),this._next||hn===this||(hn?hn._next=this:cn=this,hn=this),this._call=t,this._time=r,Nn()},stop:function(){this._call&&(this._call=null,this._time=1/0,Nn())}};var _n=4294967296;function jn(t){return t.x}function An(t){return t.y}var On=Math.PI*(3-Math.sqrt(5));function Sn(){var t,e,r,n,o,i=Qr(-30),a=1,s=1/0,u=.81;function c(r){var o,i=t.length,a=nr(t,jn,An).visitAfter(f);for(n=r,o=0;o<i;++o)e=t[o],a.visit(d)}function h(){if(t){var e,r,n=t.length;for(o=new Array(n),e=0;e<n;++e)r=t[e],o[r.index]=+i(r,e,t)}}function f(t){var e,r,n,i,a,s=0,u=0;if(t.length){for(n=i=a=0;a<4;++a)(e=t[a])&&(r=Math.abs(e.value))&&(s+=e.value,u+=r,n+=r*e.x,i+=r*e.y);t.x=n/u,t.y=i/u}else{(e=t).x=e.data.x,e.y=e.data.y;do{s+=o[e.data.index]}while(e=e.next)}t.value=s}function d(t,i,c,h){if(!t.value)return!0;var f=t.x-e.x,d=t.y-e.y,l=h-i,v=f*f+d*d;if(l*l/u<v)return v<s&&(0===f&&(v+=(f=Xr(r))*f),0===d&&(v+=(d=Xr(r))*d),v<a&&(v=Math.sqrt(a*v)),e.vx+=f*t.value*n/v,e.vy+=d*t.value*n/v),!0;if(!(t.length||v>=s)){(t.data!==e||t.next)&&(0===f&&(v+=(f=Xr(r))*f),0===d&&(v+=(d=Xr(r))*d),v<a&&(v=Math.sqrt(a*v)));do{t.data!==e&&(l=o[t.data.index]*n/v,e.vx+=f*l,e.vy+=d*l)}while(t=t.next)}}return c.initialize=function(e,n){t=e,r=n,h()},c.strength=function(t){return arguments.length?(i="function"==typeof t?t:Qr(+t),h(),c):i},c.distanceMin=function(t){return arguments.length?(a=t*t,c):Math.sqrt(a)},c.distanceMax=function(t){return arguments.length?(s=t*t,c):Math.sqrt(s)},c.theta=function(t){return arguments.length?(u=t*t,c):Math.sqrt(u)},c}function zn(t,e){var r,n=1;function o(){var o,i,a=r.length,s=0,u=0;for(o=0;o<a;++o)s+=(i=r[o]).x,u+=i.y;for(s=(s/a-t)*n,u=(u/a-e)*n,o=0;o<a;++o)(i=r[o]).x-=s,i.y-=u}return null==t&&(t=0),null==e&&(e=0),o.initialize=function(t){r=t},o.x=function(e){return arguments.length?(t=+e,o):t},o.y=function(t){return arguments.length?(e=+t,o):e},o.strength=function(t){return arguments.length?(n=+t,o):n},o}function Rn(t){return t.x+t.vx}function In(t){return t.y+t.vy}function Pn(t){var e,r,n,o=1,i=1;function a(){for(var t,a,u,c,h,f,d,l=e.length,v=0;v<i;++v)for(a=nr(e,Rn,In).visitAfter(s),t=0;t<l;++t)u=e[t],f=r[u.index],d=f*f,c=u.x+u.vx,h=u.y+u.vy,a.visit(g);function g(t,e,r,i,a){var s=t.data,l=t.r,v=f+l;if(!s)return e>c+v||i<c-v||r>h+v||a<h-v;if(s.index>u.index){var g=c-s.x-s.vx,p=h-s.y-s.vy,y=g*g+p*p;y<v*v&&(0===g&&(y+=(g=Xr(n))*g),0===p&&(y+=(p=Xr(n))*p),y=(v-(y=Math.sqrt(y)))/y*o,u.vx+=(g*=y)*(v=(l*=l)/(d+l)),u.vy+=(p*=y)*v,s.vx-=g*(v=1-v),s.vy-=p*v)}}}function s(t){if(t.data)return t.r=r[t.data.index];for(var e=t.r=0;e<4;++e)t[e]&&t[e].r>t.r&&(t.r=t[e].r)}function u(){if(e){var n,o,i=e.length;for(r=new Array(i),n=0;n<i;++n)o=e[n],r[o.index]=+t(o,n,e)}}return"function"!=typeof t&&(t=Qr(null==t?1:+t)),a.initialize=function(t,r){e=t,n=r,u()},a.iterations=function(t){return arguments.length?(i=+t,a):i},a.strength=function(t){return arguments.length?(o=+t,a):o},a.radius=function(e){return arguments.length?(t="function"==typeof e?e:Qr(+e),u(),a):t},a}function Tn(t,e,r){var n,o,i,a=Qr(.1);function s(t){for(var a=0,s=n.length;a<s;++a){var u=n[a],c=u.x-e||1e-6,h=u.y-r||1e-6,f=Math.sqrt(c*c+h*h),d=(i[a]-f)*o[a]*t/f;u.vx+=c*d,u.vy+=h*d}}function u(){if(n){var e,r=n.length;for(o=new Array(r),i=new Array(r),e=0;e<r;++e)i[e]=+t(n[e],e,n),o[e]=isNaN(i[e])?0:+a(n[e],e,n)}}return"function"!=typeof t&&(t=Qr(+t)),null==e&&(e=0),null==r&&(r=0),s.initialize=function(t){n=t,u()},s.strength=function(t){return arguments.length?(a="function"==typeof t?t:Qr(+t),u(),s):a},s.radius=function(e){return arguments.length?(t="function"==typeof e?e:Qr(+e),u(),s):t},s.x=function(t){return arguments.length?(e=+t,s):e},s.y=function(t){return arguments.length?(r=+t,s):r},s}function Cn(t){var e,r,n,o=Qr(.1);function i(t){for(var o,i=0,a=e.length;i<a;++i)(o=e[i]).vx+=(n[i]-o.x)*r[i]*t}function a(){if(e){var i,a=e.length;for(r=new Array(a),n=new Array(a),i=0;i<a;++i)r[i]=isNaN(n[i]=+t(e[i],i,e))?0:+o(e[i],i,e)}}return"function"!=typeof t&&(t=Qr(null==t?0:+t)),i.initialize=function(t){e=t,a()},i.strength=function(t){return arguments.length?(o="function"==typeof t?t:Qr(+t),a(),i):o},i.x=function(e){return arguments.length?(t="function"==typeof e?e:Qr(+e),a(),i):t},i}function Dn(t){var e,r,n,o=Qr(.1);function i(t){for(var o,i=0,a=e.length;i<a;++i)(o=e[i]).vy+=(n[i]-o.y)*r[i]*t}function a(){if(e){var i,a=e.length;for(r=new Array(a),n=new Array(a),i=0;i<a;++i)r[i]=isNaN(n[i]=+t(e[i],i,e))?0:+o(e[i],i,e)}}return"function"!=typeof t&&(t=Qr(null==t?0:+t)),i.initialize=function(t){e=t,a()},i.strength=function(t){return arguments.length?(o="function"==typeof t?t:Qr(+t),a(),i):o},i.y=function(e){return arguments.length?(t="function"==typeof e?e:Qr(+e),a(),i):t},i}var Ln=function(){return p((function t(e){v(this,t),this.id="d3-force",this.config={inputNodeAttrs:["x","y","vx","vy","fx","fy"],outputNodeAttrs:["x","y","vx","vy"],simulationAttrs:["alpha","alphaMin","alphaDecay","alphaTarget","velocityDecay","randomSource"]},this.forceMap={link:en,manyBody:Sn,center:zn,collide:Pn,radial:Tn,x:Cn,y:Dn},this.options={link:{id:function(t){return t.id}},manyBody:{},center:{x:0,y:0}},this.context={options:{},assign:!1,nodes:[],edges:[]},Kr(this.options,e),this.options.forceSimulation&&(this.simulation=this.options.forceSimulation)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"stop",value:function(){this.simulation.stop()}},{key:"tick",value:function(t){return this.simulation.tick(t),this.getResult()}},{key:"restart",value:function(){this.simulation.restart()}},{key:"setFixedPosition",value:function(t,e){var r=this.context.nodes.find((function(e){return e.id===t}));r&&e.forEach((function(t,e){"number"!=typeof t&&null!==t||(r[["fx","fy","fz"][e]]=t)}))}},{key:"getOptions",value:function(t){var e,r,n=Kr({},this.options,t);return n.collide&&void 0===(null===(e=n.collide)||void 0===e?void 0:e.radius)&&(n.collide=n.collide||{},n.collide.radius=null!==(r=n.nodeSize)&&void 0!==r?r:10),void 0===n.iterations&&(n.link&&void 0===n.link.iterations&&(n.iterations=n.link.iterations),n.collide&&void 0===n.collide.iterations&&(n.iterations=n.collide.iterations)),this.context.options=n,n}},{key:"genericLayout",value:function(t,r,n){var i;return o(this,void 0,void 0,e().mark((function o(){var a,s,u,c,h,f=this;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=this.getOptions(n),s=r.getAllNodes().map((function(t){var e=t.id,r=t.data;return Object.assign(Object.assign({id:e},r),Jr(r.data,f.config.inputNodeAttrs))})),u=r.getAllEdges().map((function(t){return Object.assign({},t)})),Object.assign(this.context,{assign:t,nodes:s,edges:u,graph:r}),c=new Promise((function(t){f.resolver=t})),(h=this.setSimulation(a)).nodes(s),null===(i=h.force("link"))||void 0===i||i.links(u),e.abrupt("return",c);case 9:case"end":return e.stop()}}),o,this)})))}},{key:"getResult",value:function(){var t=this,e=this.context,r=e.assign,n=e.nodes,o=e.edges,i=e.graph,a=n.map((function(e){return{id:e.id,data:Object.assign(Object.assign({},e.data),Jr(e,t.config.outputNodeAttrs))}})),s=o.map((function(t){var e=t.id,r=t.source,n=t.target,o=t.data;return{id:e,source:"object"==typeof r?r.id:r,target:"object"==typeof n?n.id:n,data:o}}));return r&&a.forEach((function(t){return i.mergeNodeData(t.id,t.data)})),{nodes:a,edges:s}}},{key:"initSimulation",value:function(){return function(t){var e,r,n=1,o=.001,i=1-Math.pow(o,1/300),a=0,s=.6,u=new Map,c=En(d),h=un("tick","end"),f=(r=1,function(){return(r=(1664525*r+1013904223)%_n)/_n});function d(){l(),h.call("tick",e),n<o&&(c.stop(),h.call("end",e))}function l(r){var o,c,h=t.length;void 0===r&&(r=1);for(var f=0;f<r;++f)for(n+=(a-n)*i,u.forEach((function(t){t(n)})),o=0;o<h;++o)null==(c=t[o]).fx?c.x+=c.vx*=s:(c.x=c.fx,c.vx=0),null==c.fy?c.y+=c.vy*=s:(c.y=c.fy,c.vy=0);return e}function v(){for(var e,r=0,n=t.length;r<n;++r){if((e=t[r]).index=r,null!=e.fx&&(e.x=e.fx),null!=e.fy&&(e.y=e.fy),isNaN(e.x)||isNaN(e.y)){var o=10*Math.sqrt(.5+r),i=r*On;e.x=o*Math.cos(i),e.y=o*Math.sin(i)}(isNaN(e.vx)||isNaN(e.vy))&&(e.vx=e.vy=0)}}function g(e){return e.initialize&&e.initialize(t,f),e}return null==t&&(t=[]),v(),e={tick:l,restart:function(){return c.restart(d),e},stop:function(){return c.stop(),e},nodes:function(r){return arguments.length?(t=r,v(),u.forEach(g),e):t},alpha:function(t){return arguments.length?(n=+t,e):n},alphaMin:function(t){return arguments.length?(o=+t,e):o},alphaDecay:function(t){return arguments.length?(i=+t,e):+i},alphaTarget:function(t){return arguments.length?(a=+t,e):a},velocityDecay:function(t){return arguments.length?(s=1-t,e):1-s},randomSource:function(t){return arguments.length?(f=t,u.forEach(g),e):f},force:function(t,r){return arguments.length>1?(null==r?u.delete(t):u.set(t,g(r)),e):u.get(t)},find:function(e,r,n){var o,i,a,s,u,c=0,h=t.length;for(null==n?n=1/0:n*=n,c=0;c<h;++c)(a=(o=e-(s=t[c]).x)*o+(i=r-s.y)*i)<n&&(u=s,n=a);return u},on:function(t,r){return arguments.length>1?(h.on(t,r),e):h.on(t)}}}()}},{key:"setSimulation",value:function(t){var e=this,r=this.simulation||this.options.forceSimulation||this.initSimulation();return this.simulation||(this.simulation=r.on("tick",(function(){var r;return null===(r=t.onTick)||void 0===r?void 0:r.call(t,e.getResult())})).on("end",(function(){var t;return null===(t=e.resolver)||void 0===t?void 0:t.call(e,e.getResult())}))),Fn(r,this.config.simulationAttrs.map((function(e){return[e,t[e]]}))),Object.entries(this.forceMap).forEach((function(e){var n=f(e,2),o=n[0],i=n[1],a=o;if(t[o]){var s=r.force(a);s||(s=i(),r.force(a,s)),Fn(s,Object.entries(t[a]))}else r.force(a,null)})),r}}])}(),Fn=function(t,e){return e.reduce((function(e,r){var n=f(r,2),o=n[0],i=n[1];return e[o]&&void 0!==i?e[o].call(t,i):e}),t)};function qn(t){return function(){return t}}function Vn(t){return 1e-6*(t()-.5)}function Gn(t){return t.index}function Bn(t,e){var r=t.get(e);if(!r)throw new Error("node not found: "+e);return r}function Un(t){var e,r,n,o,i,a,s,u=Gn,c=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},h=qn(30),f=1;function d(n){for(var i=0,u=t.length;i<f;++i)for(var c,h,d,l,v,g=0,p=0,y=0,m=0;g<u;++g)h=(c=t[g]).source,p=(d=c.target).x+d.vx-h.x-h.vx||Vn(s),o>1&&(y=d.y+d.vy-h.y-h.vy||Vn(s)),o>2&&(m=d.z+d.vz-h.z-h.vz||Vn(s)),p*=l=((l=Math.sqrt(p*p+y*y+m*m))-r[g])/l*n*e[g],y*=l,m*=l,d.vx-=p*(v=a[g]),o>1&&(d.vy-=y*v),o>2&&(d.vz-=m*v),h.vx+=p*(v=1-v),o>1&&(h.vy+=y*v),o>2&&(h.vz+=m*v)}function l(){if(n){var o,s,c=n.length,h=t.length,f=new Map(n.map((function(t,e){return[u(t,e,n),t]})));for(o=0,i=new Array(c);o<h;++o)(s=t[o]).index=o,"object"!=typeof s.source&&(s.source=Bn(f,s.source)),"object"!=typeof s.target&&(s.target=Bn(f,s.target)),i[s.source.index]=(i[s.source.index]||0)+1,i[s.target.index]=(i[s.target.index]||0)+1;for(o=0,a=new Array(h);o<h;++o)s=t[o],a[o]=i[s.source.index]/(i[s.source.index]+i[s.target.index]);e=new Array(h),v(),r=new Array(h),g()}}function v(){if(n)for(var r=0,o=t.length;r<o;++r)e[r]=+c(t[r],r,t)}function g(){if(n)for(var e=0,o=t.length;e<o;++e)r[e]=+h(t[e],e,t)}return null==t&&(t=[]),d.initialize=function(t){n=t;for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];s=r.find((function(t){return"function"==typeof t}))||Math.random,o=r.find((function(t){return[1,2,3].includes(t)}))||2,l()},d.links=function(e){return arguments.length?(t=e,l(),d):t},d.id=function(t){return arguments.length?(u=t,d):u},d.iterations=function(t){return arguments.length?(f=+t,d):f},d.strength=function(t){return arguments.length?(c="function"==typeof t?t:qn(+t),v(),d):c},d.distance=function(t){return arguments.length?(h="function"==typeof t?t:qn(+t),g(),d):h},d}function Wn(t,e,r){if(isNaN(e))return t;var n,o,i,a,s,u,c=t._root,h={data:r},f=t._x0,d=t._x1;if(!c)return t._root=h,t;for(;c.length;)if((a=e>=(o=(f+d)/2))?f=o:d=o,n=c,!(c=c[s=+a]))return n[s]=h,t;if(e===(i=+t._x.call(null,c.data)))return h.next=c,n?n[s]=h:t._root=h,t;do{n=n?n[s]=new Array(2):t._root=new Array(2),(a=e>=(o=(f+d)/2))?f=o:d=o}while((s=+a)===(u=+(i>=o)));return n[u]=c,n[s]=h,t}function Yn(t,e,r){this.node=t,this.x0=e,this.x1=r}function Hn(t){return t[0]}function Kn(t,e){var r=new $n(null==e?Hn:e,NaN,NaN);return null==t?r:r.addAll(t)}function $n(t,e,r){this._x=t,this._x0=e,this._x1=r,this._root=void 0}function Jn(t){for(var e={data:t.data},r=e;t=t.next;)r=r.next={data:t.data};return e}var Qn=Kn.prototype=$n.prototype;Qn.copy=function(){var t,e,r=new $n(this._x,this._x0,this._x1),n=this._root;if(!n)return r;if(!n.length)return r._root=Jn(n),r;for(t=[{source:n,target:r._root=new Array(2)}];n=t.pop();)for(var o=0;o<2;++o)(e=n.source[o])&&(e.length?t.push({source:e,target:n.target[o]=new Array(2)}):n.target[o]=Jn(e));return r},Qn.add=function(t){var e=+this._x.call(null,t);return Wn(this.cover(e),e,t)},Qn.addAll=function(t){Array.isArray(t)||(t=Array.from(t));for(var e,r=t.length,n=new Float64Array(r),o=1/0,i=-1/0,a=0;a<r;++a)isNaN(e=+this._x.call(null,t[a]))||(n[a]=e,e<o&&(o=e),e>i&&(i=e));if(o>i)return this;this.cover(o).cover(i);for(var s=0;s<r;++s)Wn(this,n[s],t[s]);return this},Qn.cover=function(t){if(isNaN(t=+t))return this;var e=this._x0,r=this._x1;if(isNaN(e))r=(e=Math.floor(t))+1;else{for(var n,o,i=r-e||1,a=this._root;e>t||t>=r;)switch(o=+(t<e),(n=new Array(2))[o]=a,a=n,i*=2,o){case 0:r=e+i;break;case 1:e=r-i}this._root&&this._root.length&&(this._root=a)}return this._x0=e,this._x1=r,this},Qn.data=function(){var t=[];return this.visit((function(e){if(!e.length)do{t.push(e.data)}while(e=e.next)})),t},Qn.extent=function(t){return arguments.length?this.cover(+t[0][0]).cover(+t[1][0]):isNaN(this._x0)?void 0:[[this._x0],[this._x1]]},Qn.find=function(t,e){var r,n,o,i,a,s=this._x0,u=this._x1,c=[],h=this._root;for(h&&c.push(new Yn(h,s,u)),null==e?e=1/0:(s=t-e,u=t+e);i=c.pop();)if(!(!(h=i.node)||(n=i.x0)>u||(o=i.x1)<s))if(h.length){var f=(n+o)/2;c.push(new Yn(h[1],f,o),new Yn(h[0],n,f)),(a=+(t>=f))&&(i=c[c.length-1],c[c.length-1]=c[c.length-1-a],c[c.length-1-a]=i)}else{var d=Math.abs(t-+this._x.call(null,h.data));d<e&&(e=d,s=t-d,u=t+d,r=h.data)}return r},Qn.remove=function(t){if(isNaN(i=+this._x.call(null,t)))return this;var e,r,n,o,i,a,s,u,c,h=this._root,f=this._x0,d=this._x1;if(!h)return this;if(h.length)for(;;){if((s=i>=(a=(f+d)/2))?f=a:d=a,e=h,!(h=h[u=+s]))return this;if(!h.length)break;e[u+1&1]&&(r=e,c=u)}for(;h.data!==t;)if(n=h,!(h=h.next))return this;return(o=h.next)&&delete h.next,n?(o?n.next=o:delete n.next,this):e?(o?e[u]=o:delete e[u],(h=e[0]||e[1])&&h===(e[1]||e[0])&&!h.length&&(r?r[c]=h:this._root=h),this):(this._root=o,this)},Qn.removeAll=function(t){for(var e=0,r=t.length;e<r;++e)this.remove(t[e]);return this},Qn.root=function(){return this._root},Qn.size=function(){var t=0;return this.visit((function(e){if(!e.length)do{++t}while(e=e.next)})),t},Qn.visit=function(t){var e,r,n,o,i=[],a=this._root;for(a&&i.push(new Yn(a,this._x0,this._x1));e=i.pop();)if(!t(a=e.node,n=e.x0,o=e.x1)&&a.length){var s=(n+o)/2;(r=a[1])&&i.push(new Yn(r,s,o)),(r=a[0])&&i.push(new Yn(r,n,s))}return this},Qn.visitAfter=function(t){var e,r=[],n=[];for(this._root&&r.push(new Yn(this._root,this._x0,this._x1));e=r.pop();){var o=e.node;if(o.length){var i,a=e.x0,s=e.x1,u=(a+s)/2;(i=o[0])&&r.push(new Yn(i,a,u)),(i=o[1])&&r.push(new Yn(i,u,s))}n.push(e)}for(;e=n.pop();)t(e.node,e.x0,e.x1);return this},Qn.x=function(t){return arguments.length?(this._x=t,this):this._x};var Xn=4294967296;function Zn(t){return t.x}function to(t){return t.y}function eo(t){return t.z}var ro=Math.PI*(3-Math.sqrt(5)),no=20*Math.PI/(9+Math.sqrt(221));function oo(){var t,e,r,n,o,i,a=qn(-30),s=1,u=1/0,c=.81;function h(n){var i,a=t.length,s=(1===e?Kn(t,Zn):2===e?nr(t,Zn,to):3===e?lr(t,Zn,to,eo):null).visitAfter(d);for(o=n,i=0;i<a;++i)r=t[i],s.visit(l)}function f(){if(t){var e,r,n=t.length;for(i=new Array(n),e=0;e<n;++e)r=t[e],i[r.index]=+a(r,e,t)}}function d(t){var r,n,o,a,s,u,c=0,h=0,f=t.length;if(f){for(o=a=s=u=0;u<f;++u)(r=t[u])&&(n=Math.abs(r.value))&&(c+=r.value,h+=n,o+=n*(r.x||0),a+=n*(r.y||0),s+=n*(r.z||0));c*=Math.sqrt(4/f),t.x=o/h,e>1&&(t.y=a/h),e>2&&(t.z=s/h)}else{(r=t).x=r.data.x,e>1&&(r.y=r.data.y),e>2&&(r.z=r.data.z);do{c+=i[r.data.index]}while(r=r.next)}t.value=c}function l(t,a,h,f,d){if(!t.value)return!0;var l=[h,f,d][e-1],v=t.x-r.x,g=e>1?t.y-r.y:0,p=e>2?t.z-r.z:0,y=l-a,m=v*v+g*g+p*p;if(y*y/c<m)return m<u&&(0===v&&(m+=(v=Vn(n))*v),e>1&&0===g&&(m+=(g=Vn(n))*g),e>2&&0===p&&(m+=(p=Vn(n))*p),m<s&&(m=Math.sqrt(s*m)),r.vx+=v*t.value*o/m,e>1&&(r.vy+=g*t.value*o/m),e>2&&(r.vz+=p*t.value*o/m)),!0;if(!(t.length||m>=u)){(t.data!==r||t.next)&&(0===v&&(m+=(v=Vn(n))*v),e>1&&0===g&&(m+=(g=Vn(n))*g),e>2&&0===p&&(m+=(p=Vn(n))*p),m<s&&(m=Math.sqrt(s*m)));do{t.data!==r&&(y=i[t.data.index]*o/m,r.vx+=v*y,e>1&&(r.vy+=g*y),e>2&&(r.vz+=p*y))}while(t=t.next)}}return h.initialize=function(r){t=r;for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];n=i.find((function(t){return"function"==typeof t}))||Math.random,e=i.find((function(t){return[1,2,3].includes(t)}))||2,f()},h.strength=function(t){return arguments.length?(a="function"==typeof t?t:qn(+t),f(),h):a},h.distanceMin=function(t){return arguments.length?(s=t*t,h):Math.sqrt(s)},h.distanceMax=function(t){return arguments.length?(u=t*t,h):Math.sqrt(u)},h.theta=function(t){return arguments.length?(c=t*t,h):Math.sqrt(c)},h}function io(t,e,r){var n,o=1;function i(){var i,a,s=n.length,u=0,c=0,h=0;for(i=0;i<s;++i)u+=(a=n[i]).x||0,c+=a.y||0,h+=a.z||0;for(u=(u/s-t)*o,c=(c/s-e)*o,h=(h/s-r)*o,i=0;i<s;++i)a=n[i],u&&(a.x-=u),c&&(a.y-=c),h&&(a.z-=h)}return null==t&&(t=0),null==e&&(e=0),null==r&&(r=0),i.initialize=function(t){n=t},i.x=function(e){return arguments.length?(t=+e,i):t},i.y=function(t){return arguments.length?(e=+t,i):e},i.z=function(t){return arguments.length?(r=+t,i):r},i.strength=function(t){return arguments.length?(o=+t,i):o},i}function ao(t){return t.x+t.vx}function so(t){return t.y+t.vy}function uo(t){return t.z+t.vz}function co(t){var e,r,n,o,i=1,a=1;function s(){for(var t,s,c,h,f,d,l,v,g=e.length,p=0;p<a;++p)for(s=(1===r?Kn(e,ao):2===r?nr(e,ao,so):3===r?lr(e,ao,so,uo):null).visitAfter(u),t=0;t<g;++t)c=e[t],l=n[c.index],v=l*l,h=c.x+c.vx,r>1&&(f=c.y+c.vy),r>2&&(d=c.z+c.vz),s.visit(y);function y(t,e,n,a,s,u,g){var p=[e,n,a,s,u,g],y=p[0],m=p[1],w=p[2],x=p[r],b=p[r+1],E=p[r+2],k=t.data,M=t.r,N=l+M;if(!k)return y>h+N||x<h-N||r>1&&(m>f+N||b<f-N)||r>2&&(w>d+N||E<d-N);if(k.index>c.index){var _=h-k.x-k.vx,j=r>1?f-k.y-k.vy:0,A=r>2?d-k.z-k.vz:0,O=_*_+j*j+A*A;O<N*N&&(0===_&&(O+=(_=Vn(o))*_),r>1&&0===j&&(O+=(j=Vn(o))*j),r>2&&0===A&&(O+=(A=Vn(o))*A),O=(N-(O=Math.sqrt(O)))/O*i,c.vx+=(_*=O)*(N=(M*=M)/(v+M)),r>1&&(c.vy+=(j*=O)*N),r>2&&(c.vz+=(A*=O)*N),k.vx-=_*(N=1-N),r>1&&(k.vy-=j*N),r>2&&(k.vz-=A*N))}}}function u(t){if(t.data)return t.r=n[t.data.index];for(var e=t.r=0;e<Math.pow(2,r);++e)t[e]&&t[e].r>t.r&&(t.r=t[e].r)}function c(){if(e){var r,o,i=e.length;for(n=new Array(i),r=0;r<i;++r)o=e[r],n[o.index]=+t(o,r,e)}}return"function"!=typeof t&&(t=qn(null==t?1:+t)),s.initialize=function(t){e=t;for(var n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];o=i.find((function(t){return"function"==typeof t}))||Math.random,r=i.find((function(t){return[1,2,3].includes(t)}))||2,c()},s.iterations=function(t){return arguments.length?(a=+t,s):a},s.strength=function(t){return arguments.length?(i=+t,s):i},s.radius=function(e){return arguments.length?(t="function"==typeof e?e:qn(+e),c(),s):t},s}function ho(t,e,r,n){var o,i,a,s,u=qn(.1);function c(t){for(var u=0,c=o.length;u<c;++u){var h=o[u],f=h.x-e||1e-6,d=(h.y||0)-r||1e-6,l=(h.z||0)-n||1e-6,v=Math.sqrt(f*f+d*d+l*l),g=(s[u]-v)*a[u]*t/v;h.vx+=f*g,i>1&&(h.vy+=d*g),i>2&&(h.vz+=l*g)}}function h(){if(o){var e,r=o.length;for(a=new Array(r),s=new Array(r),e=0;e<r;++e)s[e]=+t(o[e],e,o),a[e]=isNaN(s[e])?0:+u(o[e],e,o)}}return"function"!=typeof t&&(t=qn(+t)),null==e&&(e=0),null==r&&(r=0),null==n&&(n=0),c.initialize=function(t){o=t;for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];i=r.find((function(t){return[1,2,3].includes(t)}))||2,h()},c.strength=function(t){return arguments.length?(u="function"==typeof t?t:qn(+t),h(),c):u},c.radius=function(e){return arguments.length?(t="function"==typeof e?e:qn(+e),h(),c):t},c.x=function(t){return arguments.length?(e=+t,c):e},c.y=function(t){return arguments.length?(r=+t,c):r},c.z=function(t){return arguments.length?(n=+t,c):n},c}function fo(t){var e,r,n,o=qn(.1);function i(t){for(var o,i=0,a=e.length;i<a;++i)(o=e[i]).vx+=(n[i]-o.x)*r[i]*t}function a(){if(e){var i,a=e.length;for(r=new Array(a),n=new Array(a),i=0;i<a;++i)r[i]=isNaN(n[i]=+t(e[i],i,e))?0:+o(e[i],i,e)}}return"function"!=typeof t&&(t=qn(null==t?0:+t)),i.initialize=function(t){e=t,a()},i.strength=function(t){return arguments.length?(o="function"==typeof t?t:qn(+t),a(),i):o},i.x=function(e){return arguments.length?(t="function"==typeof e?e:qn(+e),a(),i):t},i}function lo(t){var e,r,n,o=qn(.1);function i(t){for(var o,i=0,a=e.length;i<a;++i)(o=e[i]).vy+=(n[i]-o.y)*r[i]*t}function a(){if(e){var i,a=e.length;for(r=new Array(a),n=new Array(a),i=0;i<a;++i)r[i]=isNaN(n[i]=+t(e[i],i,e))?0:+o(e[i],i,e)}}return"function"!=typeof t&&(t=qn(null==t?0:+t)),i.initialize=function(t){e=t,a()},i.strength=function(t){return arguments.length?(o="function"==typeof t?t:qn(+t),a(),i):o},i.y=function(e){return arguments.length?(t="function"==typeof e?e:qn(+e),a(),i):t},i}function vo(t){var e,r,n,o=qn(.1);function i(t){for(var o,i=0,a=e.length;i<a;++i)(o=e[i]).vz+=(n[i]-o.z)*r[i]*t}function a(){if(e){var i,a=e.length;for(r=new Array(a),n=new Array(a),i=0;i<a;++i)r[i]=isNaN(n[i]=+t(e[i],i,e))?0:+o(e[i],i,e)}}return"function"!=typeof t&&(t=qn(null==t?0:+t)),i.initialize=function(t){e=t,a()},i.strength=function(t){return arguments.length?(o="function"==typeof t?t:qn(+t),a(),i):o},i.z=function(e){return arguments.length?(t="function"==typeof e?e:qn(+e),a(),i):t},i}var go=function(t){function e(){var t;return v(this,e),(t=x(this,e,arguments)).id="d3-force-3d",t.config={inputNodeAttrs:["x","y","z","vx","vy","vz","fx","fy","fz"],outputNodeAttrs:["x","y","z","vx","vy","vz"],simulationAttrs:["alpha","alphaMin","alphaDecay","alphaTarget","velocityDecay","randomSource","numDimensions"]},t.forceMap={link:Un,manyBody:oo,center:io,collide:co,radial:ho,x:fo,y:lo,z:vo},t.options={numDimensions:3,link:{id:function(t){return t.id}},manyBody:{},center:{x:0,y:0,z:0}},t}return E(e,t),p(e,[{key:"initSimulation",value:function(){return function(t,e){e=e||2;var r,n,o=Math.min(3,Math.max(1,Math.round(e))),i=1,a=.001,s=1-Math.pow(a,1/300),u=0,c=.6,h=new Map,f=En(v),d=un("tick","end"),l=(n=1,function(){return(n=(1664525*n+1013904223)%Xn)/Xn});function v(){g(),d.call("tick",r),i<a&&(f.stop(),d.call("end",r))}function g(e){var n,a,f=t.length;void 0===e&&(e=1);for(var d=0;d<e;++d)for(i+=(u-i)*s,h.forEach((function(t){t(i)})),n=0;n<f;++n)null==(a=t[n]).fx?a.x+=a.vx*=c:(a.x=a.fx,a.vx=0),o>1&&(null==a.fy?a.y+=a.vy*=c:(a.y=a.fy,a.vy=0)),o>2&&(null==a.fz?a.z+=a.vz*=c:(a.z=a.fz,a.vz=0));return r}function p(){for(var e,r=0,n=t.length;r<n;++r){if((e=t[r]).index=r,null!=e.fx&&(e.x=e.fx),null!=e.fy&&(e.y=e.fy),null!=e.fz&&(e.z=e.fz),isNaN(e.x)||o>1&&isNaN(e.y)||o>2&&isNaN(e.z)){var i=10*(o>2?Math.cbrt(.5+r):o>1?Math.sqrt(.5+r):r),a=r*ro,s=r*no;1===o?e.x=i:2===o?(e.x=i*Math.cos(a),e.y=i*Math.sin(a)):(e.x=i*Math.sin(a)*Math.cos(s),e.y=i*Math.cos(a),e.z=i*Math.sin(a)*Math.sin(s))}(isNaN(e.vx)||o>1&&isNaN(e.vy)||o>2&&isNaN(e.vz))&&(e.vx=0,o>1&&(e.vy=0),o>2&&(e.vz=0))}}function y(e){return e.initialize&&e.initialize(t,l,o),e}return null==t&&(t=[]),p(),r={tick:g,restart:function(){return f.restart(v),r},stop:function(){return f.stop(),r},numDimensions:function(t){return arguments.length?(o=Math.min(3,Math.max(1,Math.round(t))),h.forEach(y),r):o},nodes:function(e){return arguments.length?(t=e,p(),h.forEach(y),r):t},alpha:function(t){return arguments.length?(i=+t,r):i},alphaMin:function(t){return arguments.length?(a=+t,r):a},alphaDecay:function(t){return arguments.length?(s=+t,r):+s},alphaTarget:function(t){return arguments.length?(u=+t,r):u},velocityDecay:function(t){return arguments.length?(c=1-t,r):1-c},randomSource:function(t){return arguments.length?(l=t,h.forEach(y),r):l},force:function(t,e){return arguments.length>1?(null==e?h.delete(t):h.set(t,y(e)),r):h.get(t)},find:function(){var e,r,n,i,a,s,u=Array.prototype.slice.call(arguments),c=u.shift()||0,h=(o>1?u.shift():null)||0,f=(o>2?u.shift():null)||0,d=u.shift()||1/0,l=0,v=t.length;for(d*=d,l=0;l<v;++l)(i=(e=c-(a=t[l]).x)*e+(r=h-(a.y||0))*r+(n=f-(a.z||0))*n)<d&&(s=a,d=i);return s},on:function(t,e){return arguments.length>1?(d.on(t,e),r):d.on(t)}}}()}}])}(Ln),po=r(3637),yo=r.n(po),mo=function(){return p((function t(e){v(this,t),this.id="dagre",this.options={},Object.assign(this.options,t.defaultOptions,e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericDagreLayout(!1,t,Object.assign(Object.assign({},this.options),r)));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericDagreLayout(!0,t,Object.assign(Object.assign({},this.options),r));case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericDagreLayout",value:function(t,r,i){return o(this,void 0,void 0,e().mark((function o(){var a,s,u,c,h;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=i.nodeSize,(s=new po.graphlib.Graph).setGraph(i),s.setDefaultEdgeLabel((function(){return{}})),u=r.getAllNodes(),c=r.getAllEdges(),[].concat(l(u),l(c)).some((function(t){return $(t.id)}))&&console.error("Dagre layout only support string id, it will convert number to string."),r.getAllNodes().forEach((function(t){var e=t.id,r=Object.assign({},t.data);if(void 0!==a){var n=f(Pe(Re(a)?a(t):a),2),o=n[0],i=n[1];Object.assign(r,{width:o,height:i})}s.setNode(e.toString(),r)})),r.getAllEdges().forEach((function(t){var e=t.id,r=t.source,n=t.target;s.setEdge(r.toString(),n.toString(),{id:e})})),yo().layout(s),h={nodes:[],edges:[]},s.nodes().forEach((function(e){var n=s.node(e);h.nodes.push({id:e,data:n}),t&&r.mergeNodeData(e,n)})),s.edges().forEach((function(e){var o=s.edge(e),i=o.id,a=n(o,["id"]),u=e.v,c=e.w;h.edges.push({id:i,source:u,target:c,data:a}),t&&r.mergeEdgeData(i,a)})),e.abrupt("return",h);case 14:case"end":return e.stop()}}),o)})))}}])}();mo.defaultOptions={};var wo,xo=function(){function t(e){v(this,t),this.id=e.id||0,this.rx=e.rx,this.ry=e.ry,this.fx=0,this.fy=0,this.mass=e.mass,this.degree=e.degree,this.g=e.g||0}return p(t,[{key:"distanceTo",value:function(t){var e=this.rx-t.rx,r=this.ry-t.ry;return Math.hypot(e,r)}},{key:"setPos",value:function(t,e){this.rx=t,this.ry=e}},{key:"resetForce",value:function(){this.fx=0,this.fy=0}},{key:"addForce",value:function(t){var e=t.rx-this.rx,r=t.ry-this.ry,n=Math.hypot(e,r);n=n<1e-4?1e-4:n;var o=this.g*(this.degree+1)*(t.degree+1)/n;this.fx+=o*e/n,this.fy+=o*r/n}},{key:"in",value:function(t){return t.contains(this.rx,this.ry)}},{key:"add",value:function(e){var r=this.mass+e.mass;return new t({rx:(this.rx*this.mass+e.rx*e.mass)/r,ry:(this.ry*this.mass+e.ry*e.mass)/r,mass:r,degree:this.degree+e.degree})}}])}(),bo=function(){function t(e){v(this,t),this.xmid=e.xmid,this.ymid=e.ymid,this.length=e.length,this.massCenter=e.massCenter||[0,0],this.mass=e.mass||1}return p(t,[{key:"getLength",value:function(){return this.length}},{key:"contains",value:function(t,e){var r=this.length/2;return t<=this.xmid+r&&t>=this.xmid-r&&e<=this.ymid+r&&e>=this.ymid-r}},{key:"NW",value:function(){return new t({xmid:this.xmid-this.length/4,ymid:this.ymid+this.length/4,length:this.length/2})}},{key:"NE",value:function(){return new t({xmid:this.xmid+this.length/4,ymid:this.ymid+this.length/4,length:this.length/2})}},{key:"SW",value:function(){return new t({xmid:this.xmid-this.length/4,ymid:this.ymid-this.length/4,length:this.length/2})}},{key:"SE",value:function(){return new t({xmid:this.xmid+this.length/4,ymid:this.ymid-this.length/4,length:this.length/2})}}])}(),Eo=function(){function t(e){v(this,t),this.body=null,this.quad=null,this.NW=null,this.NE=null,this.SW=null,this.SE=null,this.theta=.5,null!=e&&(this.quad=e)}return p(t,[{key:"insert",value:function(e){null!=this.body?this._isExternal()?(this.quad&&(this.NW=new t(this.quad.NW()),this.NE=new t(this.quad.NE()),this.SW=new t(this.quad.SW()),this.SE=new t(this.quad.SE())),this._putBody(this.body),this._putBody(e),this.body=this.body.add(e)):(this.body=this.body.add(e),this._putBody(e)):this.body=e}},{key:"_putBody",value:function(t){this.quad&&(t.in(this.quad.NW())&&this.NW?this.NW.insert(t):t.in(this.quad.NE())&&this.NE?this.NE.insert(t):t.in(this.quad.SW())&&this.SW?this.SW.insert(t):t.in(this.quad.SE())&&this.SE&&this.SE.insert(t))}},{key:"_isExternal",value:function(){return null==this.NW&&null==this.NE&&null==this.SW&&null==this.SE}},{key:"updateForce",value:function(t){null!=this.body&&t!==this.body&&(this._isExternal()||(this.quad?this.quad.getLength():0)/this.body.distanceTo(t)<this.theta?t.addForce(this.body):(this.NW&&this.NW.updateForce(t),this.NE&&this.NE.updateForce(t),this.SW&&this.SW.updateForce(t),this.SE&&this.SE.updateForce(t)))}}])}(),ko={center:[0,0],width:300,height:300,kr:5,kg:1,mode:"normal",preventOverlap:!1,dissuadeHubs:!1,maxIteration:0,ks:.1,ksmax:10,tao:.1},Mo=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="forceAtlas2",this.options=Object.assign(Object.assign({},ko),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericForceAtlas2Layout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericForceAtlas2Layout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericForceAtlas2Layout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,f,d,l,v,g,p,y,m,w,x,b,E,k,M,N,_;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=r.getAllEdges(),a=r.getAllNodes(),s=this.formatOptions(n,a.length),u=s.width,c=s.height,h=s.prune,f=s.maxIteration,d=s.nodeSize,l=s.center,(null==a?void 0:a.length)&&1!==a.length){e.next=6;break}return e.abrupt("return",Be(r,t,l));case 6:if(v=a.map((function(t){return Fe(t,[u,c])})),g=i.filter((function(t){return t.source!==t.target})),p=new A({nodes:v,edges:g}),y=this.getSizes(p,d),this.run(p,r,f,y,t,s),h){for(m=0;m<g.length;m+=1)w=g[m],x=w.source,b=w.target,E=p.getDegree(x),k=p.getDegree(x),E<=1?(M=p.getNode(b),p.mergeNodeData(x,{x:M.data.x,y:M.data.y})):k<=1&&(N=p.getNode(x),p.mergeNodeData(b,{x:N.data.x,y:N.data.y}));_=Object.assign(Object.assign({},s),{prune:!1,barnesHut:!1}),this.run(p,r,100,y,t,_)}return e.abrupt("return",{nodes:v,edges:i});case 13:case"end":return e.stop()}}),o,this)})))}},{key:"getSizes",value:function(t,e){for(var r=t.getAllNodes(),n={},o=0;o<r.length;o+=1){var i=r[o];n[i.id]=De(e,void 0)(i)}return n}},{key:"formatOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,r=Object.assign(Object.assign({},this.options),t),n=r.center,o=r.width,i=r.height,a=r.barnesHut,s=r.prune,u=r.maxIteration,c=r.kr,h=r.kg;return r.width=o||"undefined"==typeof window?o:window.innerWidth,r.height=i||"undefined"==typeof window?i:window.innerHeight,r.center=n||[r.width/2,r.height/2],void 0===a&&e>250&&(r.barnesHut=!0),void 0===s&&e>100&&(r.prune=!0),0!==u||s?0===u&&s&&(r.maxIteration=100,e<=200&&e>100?r.maxIteration=500:e>200&&(r.maxIteration=950)):(r.maxIteration=250,e<=200&&e>100?r.maxIteration=1e3:e>200&&(r.maxIteration=1200)),c||(r.kr=50,e>100&&e<=500?r.kr=20:e>500&&(r.kr=1)),h||(r.kg=20,e>100&&e<=500?r.kg=10:e>500&&(r.kg=1)),r}},{key:"run",value:function(t,e,r,n,o,i){for(var a=i.kr,s=i.barnesHut,u=i.onTick,c=t.getAllNodes(),h=0,f=r,d={},l={},v={},g=0;g<c.length;g+=1){var p=c[g],y=p.data,m=p.id;if(d[m]=[0,0],s){var w={id:g,rx:y.x,ry:y.y,mass:1,g:a,degree:t.getDegree(m)};v[m]=new xo(w)}}for(;f>0;)h=this.oneStep(t,{iter:f,preventOverlapIters:50,krPrime:100,sg:h,forces:d,preForces:l,bodies:v,sizes:n},i),f--,null==u||u({nodes:c,edges:e.getAllEdges()});return t}},{key:"oneStep",value:function(t,e,r){for(var n=e.iter,o=e.preventOverlapIters,i=e.krPrime,a=e.sg,s=e.preForces,u=e.bodies,c=e.sizes,h=e.forces,f=r.preventOverlap,d=r.barnesHut,v=t.getAllNodes(),g=0;g<v.length;g+=1){var p=v[g].id;s[p]=l(h[p]),h[p]=[0,0]}return h=this.getAttrForces(t,n,o,c,h,r),h=d&&(f&&n>o||!f)?this.getOptRepGraForces(t,h,u,r):this.getRepGraForces(t,n,o,h,i,c,r),this.updatePos(t,h,s,a,r)}},{key:"getAttrForces",value:function(t,e,r,n,o,i){for(var a=i.preventOverlap,s=i.dissuadeHubs,u=i.mode,c=i.prune,h=t.getAllEdges(),f=0;f<h.length;f+=1){var d=h[f],l=d.source,v=d.target,g=t.getNode(l),p=t.getNode(v),y=t.getDegree(l),m=t.getDegree(v);if(!c||!(y<=1||m<=1)){var w=[p.data.x-g.data.x,p.data.y-g.data.y],x=Math.hypot(w[0],w[1]);x=x<1e-4?1e-4:x,w[0]=w[0]/x,w[1]=w[1]/x,a&&e<r&&(x=x-n[l]-n[v]);var b=x,E=b;"linlog"===u&&(E=b=Math.log(1+x)),s&&(b=x/y,E=x/m),a&&e<r&&x<=0?(b=0,E=0):a&&e<r&&x>0&&(b=x,E=x),o[l][0]+=b*w[0],o[v][0]-=E*w[0],o[l][1]+=b*w[1],o[v][1]-=E*w[1]}}return o}},{key:"getOptRepGraForces",value:function(t,e,r,n){for(var o=n.kg,i=n.center,a=n.prune,s=t.getAllNodes(),u=s.length,c=9e10,h=-9e10,f=9e10,d=-9e10,l=0;l<u;l+=1){var v=s[l],g=v.id,p=v.data;a&&t.getDegree(g)<=1||(r[g].setPos(p.x,p.y),p.x>=h&&(h=p.x),p.x<=c&&(c=p.x),p.y>=d&&(d=p.y),p.y<=f&&(f=p.y))}for(var y=Math.max(h-c,d-f),m=new bo({xmid:(h+c)/2,ymid:(d+f)/2,length:y,massCenter:i,mass:u}),w=new Eo(m),x=0;x<u;x+=1){var b=s[x].id;a&&t.getDegree(b)<=1||r[b].in(m)&&w.insert(r[b])}for(var E=0;E<u;E+=1){var k=s[E],M=k.id,N=k.data,_=t.getDegree(M);if(!(a&&_<=1)){r[M].resetForce(),w.updateForce(r[M]),e[M][0]-=r[M].fx,e[M][1]-=r[M].fy;var j=[N.x-i[0],N.y-i[1]],A=Math.hypot(j[0],j[1]);A=A<1e-4?1e-4:A,j[0]=j[0]/A,j[1]=j[1]/A;var O=o*(_+1);e[M][0]-=O*j[0],e[M][1]-=O*j[1]}}return e}},{key:"getRepGraForces",value:function(t,e,r,n,o,i,a){for(var s=a.preventOverlap,u=a.kr,c=a.kg,h=a.center,f=a.prune,d=t.getAllNodes(),l=d.length,v=0;v<l;v+=1){for(var g=d[v],p=t.getDegree(g.id),y=v+1;y<l;y+=1){var m=d[y],w=t.getDegree(m.id);if(!f||!(p<=1||w<=1)){var x=[m.data.x-g.data.x,m.data.y-g.data.y],b=Math.hypot(x[0],x[1]);b=b<1e-4?1e-4:b,x[0]=x[0]/b,x[1]=x[1]/b,s&&e<r&&(b=b-i[g.id]-i[m.id]);var E=u*(p+1)*(w+1)/b;s&&e<r&&b<0?E=o*(p+1)*(w+1):s&&e<r&&0===b?E=0:s&&e<r&&b>0&&(E=u*(p+1)*(w+1)/b),n[g.id][0]-=E*x[0],n[m.id][0]+=E*x[0],n[g.id][1]-=E*x[1],n[m.id][1]+=E*x[1]}}var k=[g.data.x-h[0],g.data.y-h[1]],M=Math.hypot(k[0],k[1]);k[0]=k[0]/M,k[1]=k[1]/M;var N=c*(p+1);n[g.id][0]-=N*k[0],n[g.id][1]-=N*k[1]}return n}},{key:"updatePos",value:function(t,e,r,n,o){for(var i=o.ks,a=o.tao,s=o.prune,u=o.ksmax,c=t.getAllNodes(),h=c.length,f=[],d=[],l=0,v=0,g=n,p=0;p<h;p+=1){var y=c[p].id,m=t.getDegree(y);if(!(s&&m<=1)){var w=[e[y][0]-r[y][0],e[y][1]-r[y][1]],x=Math.hypot(w[0],w[1]),b=[e[y][0]+r[y][0],e[y][1]+r[y][1]],E=Math.hypot(b[0],b[1]);f[p]=x,d[p]=E/2,l+=(m+1)*f[p],v+=(m+1)*d[p]}}var k=g;g=a*v/l,0!==k&&(g=g>1.5*k?1.5*k:g);for(var M=0;M<h;M+=1){var N=c[M],_=N.id,j=N.data,A=t.getDegree(_);if(!(s&&A<=1||$(j.fx)&&$(j.fy))){var O=i*g/(1+g*Math.sqrt(f[M])),S=Math.hypot(e[_][0],e[_][1]),z=u/(S=S<1e-4?1e-4:S),R=(O=O>z?z:O)*e[_][0],I=O*e[_][1];t.mergeNodeData(_,{x:j.x+R,y:j.y+I})}}return g}}])}(),No={maxIteration:1e3,gravity:10,speed:5,clustering:!1,clusterGravity:10,width:300,height:300,nodeClusterBy:"cluster"},_o=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="fruchterman",this.timeInterval=0,this.running=!1,this.options=Object.assign(Object.assign({},No),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericFruchtermanLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericFruchtermanLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"stop",value:function(){this.timeInterval&&"undefined"!=typeof window&&window.clearInterval(this.timeInterval),this.running=!1}},{key:"tick",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.maxIteration||1;if(this.lastResult)return this.lastResult;for(var r=0;r<e;r++)this.runOneStep(this.lastGraph,this.lastClusterMap,this.lastOptions);var n={nodes:this.lastLayoutNodes,edges:this.lastLayoutEdges};return this.lastAssign&&n.nodes.forEach((function(e){return t.lastGraph.mergeNodeData(e.id,{x:e.data.x,y:e.data.y,z:3===t.options.dimensions?e.data.z:void 0})})),n}},{key:"genericFruchtermanLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,f,d,l,v,g,p,y,m,w,x,b,E=this;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.running){e.next=2;break}return e.abrupt("return");case 2:if(i=this.formatOptions(n),a=i.dimensions,s=i.width,u=i.height,c=i.center,h=i.clustering,f=i.nodeClusterBy,d=i.maxIteration,l=i.onTick,v=r.getAllNodes(),g=r.getAllEdges(),null==v?void 0:v.length){e.next=10;break}return p={nodes:[],edges:g},this.lastResult=p,e.abrupt("return",p);case 10:if(1!==v.length){e.next=15;break}return t&&r.mergeNodeData(v[0].id,{x:c[0],y:c[1],z:3===a?c[2]:void 0}),y={nodes:[Object.assign(Object.assign({},v[0]),{data:Object.assign(Object.assign({},v[0].data),{x:c[0],y:c[1],z:3===a?c[2]:void 0})})],edges:g},this.lastResult=y,e.abrupt("return",y);case 15:if(m=v.map((function(t){return Fe(t,[s,u])})),w=new A({nodes:m,edges:g}),x={},h&&m.forEach((function(t){var e=t.data[f];x[e]||(x[e]={name:e,cx:0,cy:0,count:0})})),this.lastLayoutNodes=m,this.lastLayoutEdges=g,this.lastAssign=t,this.lastGraph=w,this.lastOptions=i,this.lastClusterMap=x,"undefined"!=typeof window){e.next=27;break}return e.abrupt("return");case 27:return b=0,e.abrupt("return",new Promise((function(e){E.timeInterval=window.setInterval((function(){E.running?(E.runOneStep(w,x,i),t&&m.forEach((function(t){var e=t.id,n=t.data;return r.mergeNodeData(e,{x:n.x,y:n.y,z:3===a?n.z:void 0})})),null==l||l({nodes:m,edges:g}),++b>=d&&(window.clearInterval(E.timeInterval),e({nodes:m,edges:g}))):e({nodes:m,edges:g})}),0),E.running=!0})));case 29:case"end":return e.stop()}}),o,this)})))}},{key:"formatOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=Object.assign(Object.assign({},this.options),t),r=e.clustering,n=e.nodeClusterBy,o=e.center,i=e.width,a=e.height;return e.width=i||"undefined"==typeof window?i:window.innerWidth,e.height=a||"undefined"==typeof window?a:window.innerHeight,e.center=o||[e.width/2,e.height/2],e.clustering=r&&!!n,e}},{key:"runOneStep",value:function(t,e,r){var n=r.dimensions,o=r.height,i=r.width,a=r.gravity,s=r.center,u=r.speed,c=r.clustering,h=r.nodeClusterBy,f=r.clusterGravity,d=o*i,l=Math.sqrt(d)/10,v=t.getAllNodes(),g=d/(v.length+1),p=Math.sqrt(g),y={};if(this.applyCalculate(t,y,p,g),c){for(var m in e)e[m].cx=0,e[m].cy=0,e[m].count=0;for(var w in v.forEach((function(t){var r=t.data,n=e[r[h]];$(r.x)&&(n.cx+=r.x),$(r.y)&&(n.cy+=r.y),n.count++})),e)e[w].cx/=e[w].count,e[w].cy/=e[w].count;var x=f||a;v.forEach((function(t,r){var n=t.id,o=t.data;if($(o.x)&&$(o.y)){var i=e[o[h]],a=Math.sqrt((o.x-i.cx)*(o.x-i.cx)+(o.y-i.cy)*(o.y-i.cy)),s=p*x;y[n].x-=s*(o.x-i.cx)/a,y[n].y-=s*(o.y-i.cy)/a}}))}v.forEach((function(t,e){var r=t.id,o=t.data;if($(o.x)&&$(o.y)){var i=.01*p*a;y[r].x-=i*(o.x-s[0]),y[r].y-=i*(o.y-s[1]),3===n&&(y[r].z-=i*(o.z-s[2]))}})),v.forEach((function(e,r){var o=e.id,i=e.data;if($(i.fx)&&$(i.fy))return i.x=i.fx,i.y=i.fy,void(3===n&&(i.z=i.fz));if($(i.x)&&$(i.y)){var a=Math.sqrt(y[o].x*y[o].x+y[o].y*y[o].y+(3===n?y[o].z*y[o].z:0));if(a>0){var s=Math.min(l*(u/800),a);t.mergeNodeData(o,{x:i.x+y[o].x/a*s,y:i.y+y[o].y/a*s,z:3===n?i.z+y[o].z/a*s:void 0})}}}))}},{key:"applyCalculate",value:function(t,e,r,n){this.calRepulsive(t,e,n),this.calAttractive(t,e,r)}},{key:"calRepulsive",value:function(t,e,r){var n=this,o=t.getAllNodes();o.forEach((function(t,i){var a=t.data,s=t.id;e[s]={x:0,y:0,z:0},o.forEach((function(t,o){var u=t.data,c=t.id;if(!(i<=o)&&$(a.x)&&$(u.x)&&$(a.y)&&$(u.y)){var h=a.x-u.x,f=a.y-u.y,d=3===n.options.dimensions?a.z-u.z:0,l=h*h+f*f+d*d;0===l&&(l=1,h=.01,f=.01,d=.01);var v=r/l,g=h*v,p=f*v,y=d*v;e[s].x+=g,e[s].y+=p,e[c].x-=g,e[c].y-=p,3===n.options.dimensions&&(e[s].z+=y,e[c].z-=y)}}))}))}},{key:"calAttractive",value:function(t,e,r){var n=this;t.getAllEdges().forEach((function(o){var i=o.source,a=o.target;if(i&&a&&i!==a){var s=t.getNode(i).data,u=t.getNode(a).data;if($(u.x)&&$(s.x)&&$(u.y)&&$(s.y)){var c=u.x-s.x,h=u.y-s.y,f=3===n.options.dimensions?u.z-s.z:0,d=Math.sqrt(c*c+h*h+f*f)/r,l=c*d,v=h*d,g=f*d;e[i].x+=l,e[i].y+=v,e[a].x-=l,e[a].y-=v,3===n.options.dimensions&&(e[i].z+=g,e[a].z-=g)}}}))}}])}(),jo={begin:[0,0],preventOverlap:!0,preventOverlapPadding:10,condense:!1,rows:void 0,cols:void 0,position:void 0,sortBy:"degree",nodeSize:30,width:300,height:300},Ao=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="grid",this.options=Object.assign(Object.assign({},jo),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericGridLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericGridLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericGridLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,d,l,v,g,p,y,m,w,x,b,E,k,M,N,_,j,A,O,S,z,R,I,P,T,C,D,L,F,q,V,G,B,U;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=Object.assign(Object.assign({},this.options),n),a=i.begin,s=void 0===a?[0,0]:a,u=i.condense,c=i.preventOverlapPadding,h=i.preventOverlap,d=i.rows,l=i.cols,v=i.nodeSpacing,g=i.nodeSize,p=i.width,y=i.height,m=i.position,w=i.sortBy,x=r.getAllNodes(),b=r.getAllEdges(),(E=null==x?void 0:x.length)&&1!==E){e.next=8;break}return e.abrupt("return",Be(r,t,s));case 8:if(k=x.map((function(t){return Fe(t)})),"id"===w||$e(w)&&void 0!==k[0].data[w]||(w="degree"),"degree"===w?k.sort((function(t,e){return r.getDegree(e.id,"both")-r.getDegree(t.id,"both")})):"id"===w?k.sort((function(t,e){return $(e.id)&&$(t.id)?e.id-t.id:"".concat(t.id).localeCompare("".concat(e.id))})):k.sort((function(t,e){return e.data[w]-t.data[w]})),M=p||"undefined"==typeof window?p:window.innerWidth,N=y||"undefined"==typeof window?y:window.innerHeight,_=E,j={rows:d,cols:l},null!=d&&null!=l?(j.rows=d,j.cols=l):null!=d&&null==l?(j.rows=d,j.cols=Math.ceil(_/j.rows)):null==d&&null!=l?(j.cols=l,j.rows=Math.ceil(_/j.cols)):(A=Math.sqrt(_*N/M),j.rows=Math.round(A),j.cols=Math.round(M/N*A)),j.rows=Math.max(j.rows,1),j.cols=Math.max(j.cols,1),j.cols*j.rows>_)O=Oo(j),S=So(j),(O-1)*S>=_?Oo(j,O-1):(S-1)*O>=_&&So(j,S-1);else for(;j.cols*j.rows<_;)z=Oo(j),((R=So(j))+1)*z>=_?So(j,R+1):Oo(j,z+1);for(I=u?0:M/j.cols,P=u?0:N/j.rows,(h||v)&&(T=Te(10,v),C=Ce(30,g,!1),k.forEach((function(t){t.data.x&&t.data.y||(t.data.x=0,t.data.y=0);var e=r.getNode(t.id),n=f(Pe(C(e)||30),2),o=n[0],i=n[1],a=void 0!==T?T(t):c,s=o+a,u=i+a;I=Math.max(I,s),P=Math.max(P,u)}))),D={},L={row:0,col:0},F={},q=0;q<k.length;q++){if(V=k[q],G=void 0,m&&(G=m(r.getNode(V.id))),G&&(void 0!==G.row||void 0!==G.col)){if(void 0===(B={row:G.row,col:G.col}).col)for(B.col=0;zo(D,B);)B.col++;else if(void 0===B.row)for(B.row=0;zo(D,B);)B.row++;F[V.id]=B,Ro(D,B)}Po(V,s,I,P,F,j,L,D)}return U={nodes:k,edges:b},t&&k.forEach((function(t){r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y})})),e.abrupt("return",U);case 29:case"end":return e.stop()}}),o,this)})))}}])}(),Oo=function(t,e){var r,n=t.rows||5,o=t.cols||5;return null==e?r=Math.min(n,o):Math.min(n,o)===t.rows?t.rows=e:t.cols=e,r},So=function(t,e){var r,n=t.rows||5,o=t.cols||5;return null==e?r=Math.max(n,o):Math.max(n,o)===t.rows?t.rows=e:t.cols=e,r},zo=function(t,e){return t["c-".concat(e.row,"-").concat(e.col)]||!1},Ro=function(t,e){return t["c-".concat(e.row,"-").concat(e.col)]=!0},Io=function(t,e){var r=t.cols||5;e.col++,e.col>=r&&(e.col=0,e.row++)},Po=function(t,e,r,n,o,i,a,s){var u,c,h=o[t.id];if(h)u=h.col*r+r/2+e[0],c=h.row*n+n/2+e[1];else{for(;zo(s,a);)Io(i,a);u=a.col*r+r/2+e[0],c=a.row*n+n/2+e[1],Ro(s,a),Io(i,a)}t.data.x=u,t.data.y=c},To=function(t,e,r){try{var n=jr.mul(jr.pow(e,2),-.5),o=n.mean("row"),i=n.mean("column"),a=n.mean();n.add(a).subRowVector(o).subColumnVector(i);var s=new Ar(n),u=jr.sqrt(s.diagonalMatrix).diagonal();return s.leftSingularVectors.toJSON().map((function(e){return jr.mul([e],[u]).toJSON()[0].splice(0,t)}))}catch(t){for(var c=[],h=0;h<e.length;h++){var f=Math.random()*r,d=Math.random()*r;c.push([f,d])}return c}},Co={iterations:10,height:10,width:10,speed:100,gravity:10,k:5},Do=function(t,e){for(var r=Object.assign(Object.assign({},Co),e),n=r.positions,o=r.iterations,i=r.width,a=r.k,s=r.speed,u=void 0===s?100:s,c=r.strictRadial,h=r.focusIdx,f=r.radii,d=void 0===f?[]:f,l=r.nodeSizeFunc,v=t.getAllNodes(),g=[],p=i/10,y=0;y<o;y++)n.forEach((function(t,e){g[e]={x:0,y:0}})),Lo(v,n,g,a,d,l),Fo(n,g,u,c,h,p,i,d);return n},Lo=function(t,e,r,n,o,i){e.forEach((function(a,s){r[s]={x:0,y:0},e.forEach((function(e,u){if(s!==u&&o[s]===o[u]){var c=a.x-e.x,h=a.y-e.y,f=Math.sqrt(c*c+h*h);if(0===f){f=1;var d=s>u?1:-1;c=.01*d,h=.01*d}if(f<i(t[s])/2+i(t[u])/2){var l=n*n/f;r[s].x+=c/f*l,r[s].y+=h/f*l}}}))}))},Fo=function(t,e,r,n,o,i,a,s){var u=i||a/10;return n&&e.forEach((function(e,r){var n=t[r].x-t[o].x,i=t[r].y-t[o].y,a=Math.sqrt(n*n+i*i),s=i/a,u=-n/a,c=Math.sqrt(e.x*e.x+e.y*e.y),h=Math.acos((s*e.x+u*e.y)/c);h>Math.PI/2&&(h-=Math.PI/2,s*=-1,u*=-1);var f=Math.cos(h)*c;e.x=s*f,e.y=u*f})),t.forEach((function(i,a){if(a!==o){var c=Math.sqrt(e[a].x*e[a].x+e[a].y*e[a].y);if(c>0&&a!==o){var h=Math.min(u*(r/800),c);if(i.x+=e[a].x/c*h,i.y+=e[a].y/c*h,n){var f=i.x-t[o].x,d=i.y-t[o].y,l=Math.sqrt(f*f+d*d);f=f/l*s[a],d=d/l*s[a],i.x=t[o].x+f,i.y=t[o].y+d}}}})),t},qo={maxIteration:1e3,focusNode:null,unitRadius:null,linkDistance:50,preventOverlap:!1,strictRadial:!0,maxPreventOverlapIteration:200,sortStrength:10},Vo=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="radial",this.options=Object.assign(Object.assign({},qo),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericRadialLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericRadialLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericRadialLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,d,v,g,p,y,m,w,x,b,E,k,M,N,_,j,A,O,S,z,R,I,P,T,C,D,L,F,q,V,G,B,U,W,Y,H,K,$,J;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=Object.assign(Object.assign({},this.options),n),a=i.width,s=i.height,u=i.center,c=i.focusNode,h=i.unitRadius,d=i.nodeSize,v=i.nodeSpacing,g=i.strictRadial,p=i.preventOverlap,y=i.maxPreventOverlapIteration,m=i.sortBy,w=i.linkDistance,x=void 0===w?50:w,b=i.sortStrength,E=void 0===b?10:b,k=i.maxIteration,M=void 0===k?1e3:k,N=r.getAllNodes(),_=r.getAllEdges(),j=a||"undefined"==typeof window?a:window.innerWidth,A=s||"undefined"==typeof window?s:window.innerHeight,O=u||[j/2,A/2],(null==N?void 0:N.length)&&1!==N.length){e.next=9;break}return e.abrupt("return",Be(r,t,O));case 9:if(S=N[0],!$e(c)){e.next=21;break}z=0;case 12:if(!(z<N.length)){e.next=19;break}if(N[z].id!==c){e.next=16;break}return S=N[z],e.abrupt("break",19);case 16:z++,e.next=12;break;case 19:e.next=22;break;case 21:S=c||N[0];case 22:return R=Uo(N,S.id),I=Sr({nodes:N,edges:_},!1),P=Or(I),T=Yo(P,R),Wo(P,R,T+1),C=P[R],D=j-O[0]>O[0]?O[0]:j-O[0],L=A-O[1]>O[1]?O[1]:A-O[1],0===D&&(D=j/2),0===L&&(L=A/2),F=Math.min(D,L),q=Math.max.apply(Math,l(C)),V=[],G=h||F/q,C.forEach((function(t,e){V[e]=t*G})),B=Go(N,P,x,V,G,m,E),U=Bo(B),W=To(x,B,x),Y=W.map((function(t){var e=f(t,2),r=e[0],n=e[1];return{x:(isNaN(r)?Math.random()*x:r)-W[R][0],y:(isNaN(n)?Math.random()*x:n)-W[R][1]}})),this.run(M,Y,U,B,V,R),p&&(H=De(d,v),K={nodes:N,nodeSizeFunc:H,positions:Y,radii:V,height:A,width:j,strictRadial:Boolean(g),focusIdx:R,iterations:y||200,k:Y.length/4.5},Y=Do(r,K)),$=[],Y.forEach((function(t,e){var r=Fe(N[e]);r.data.x=t.x+O[0],r.data.y=t.y+O[1],$.push(r)})),t&&$.forEach((function(t){return r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y})})),J={nodes:$,edges:_},e.abrupt("return",J);case 48:case"end":return e.stop()}}),o,this)})))}},{key:"run",value:function(t,e,r,n,o,i){for(var a=0;a<=t;a++){var s=a/t;this.oneIteration(s,e,o,n,r,i)}}},{key:"oneIteration",value:function(t,e,r,n,o,i){var a=1-t;e.forEach((function(s,u){var c=Ir(s,{x:0,y:0}),h=0===c?0:1/c;if(u!==i){var f=0,d=0,l=0;e.forEach((function(t,e){if(u!==e){var r=Ir(s,t),i=0===r?0:1/r,a=n[e][u];l+=o[u][e],f+=o[u][e]*(t.x+a*(s.x-t.x)*i),d+=o[u][e]*(t.y+a*(s.y-t.y)*i)}}));var v=0===r[u]?0:1/r[u];l*=a,l+=t*v*v,f*=a,f+=t*v*s.x*h,s.x=f/l,d*=a,d+=t*v*s.y*h,s.y=d/l}}))}}])}(),Go=function(t,e,r,n,o,i,a){if(!t)return[];var s=[];if(e){var u={};e.forEach((function(e,c){var h=[];e.forEach((function(e,s){var f,d;if(c===s)h.push(0);else if(n[c]===n[s])if("data"===i)h.push(e*(Math.abs(c-s)*a)/(n[c]/o));else if(i){var l,v;if(u[t[c].id])l=u[t[c].id];else{var g=("id"===i?t[c].id:null===(f=t[c].data)||void 0===f?void 0:f[i])||0;l=$e(g)?g.charCodeAt(0):g,u[t[c].id]=l}if(u[t[s].id])v=u[t[s].id];else{var p=("id"===i?t[s].id:null===(d=t[s].data)||void 0===d?void 0:d[i])||0;v=$e(p)?p.charCodeAt(0):p,u[t[s].id]=v}h.push(e*(Math.abs(l-v)*a)/(n[c]/o))}else h.push(e*r/(n[c]/o));else{var y=(r+o)/2;h.push(e*y)}})),s.push(h)}))}return s},Bo=function(t){for(var e=t.length,r=t[0].length,n=[],o=0;o<e;o++){for(var i=[],a=0;a<r;a++)0!==t[o][a]?i.push(1/(t[o][a]*t[o][a])):i.push(0);n.push(i)}return n},Uo=function(t,e){var r=-1;return t.forEach((function(t,n){t.id===e&&(r=n)})),Math.max(r,0)},Wo=function(t,e,r){for(var n=t.length,o=0;o<n;o++)if(t[e][o]===1/0){t[e][o]=r,t[o][e]=r;for(var i=0;i<n;i++)t[o][i]!==1/0&&t[e][i]===1/0&&(t[e][i]=r+t[o][i],t[i][e]=r+t[o][i])}for(var a=0;a<n;a++)if(a!==e)for(var s=0;s<n;s++)if(t[a][s]===1/0){var u=Math.abs(t[e][a]-t[e][s]);u=0===u?1:u,t[a][s]=u}},Yo=function(t,e){for(var r=0,n=0;n<t[e].length;n++)t[e][n]!==1/0&&(r=t[e][n]>r?t[e][n]:r);return r},Ho={center:[0,0],width:300,height:300},Ko=function(){return p((function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(this,t),this.options=e,this.id="random",this.options=Object.assign(Object.assign({},Ho),e)}),[{key:"execute",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.genericRandomLayout(!1,t,r));case 1:case"end":return e.stop()}}),n,this)})))}},{key:"assign",value:function(t,r){return o(this,void 0,void 0,e().mark((function n(){return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.genericRandomLayout(!0,t,r);case 2:case"end":return e.stop()}}),n,this)})))}},{key:"genericRandomLayout",value:function(t,r,n){return o(this,void 0,void 0,e().mark((function o(){var i,a,s,u,c,h,f,d,l,v;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=Object.assign(Object.assign({},this.options),n),a=i.center,s=i.width,u=i.height,c=r.getAllNodes(),h=s||"undefined"==typeof window?s:window.innerWidth,f=u||"undefined"==typeof window?u:window.innerHeight,d=a||[h/2,f/2],l=[],c&&c.forEach((function(t){l.push({id:t.id,data:{x:.9*(Math.random()-.5)*h+d[0],y:.9*(Math.random()-.5)*f+d[1]}})})),t&&l.forEach((function(t){return r.mergeNodeData(t.id,{x:t.data.x,y:t.data.y})})),v={nodes:l,edges:r.getAllEdges()},e.abrupt("return",v);case 12:case"end":return e.stop()}}),o,this)})))}}])}(),$o={circular:We,concentric:Xe,mds:Cr,random:Ko,grid:Ao,radial:Vo,force:xr,d3force:Ln,"d3-force-3d":go,fruchterman:_o,forceAtlas2:Mo,dagre:mo,antvDagre:Ve,comboCombined:Gr};D({stopLayout:function(){(null==wo?void 0:wo.stop)&&wo.stop()},calculateLayout:function(t,r){return o(this,void 0,void 0,e().mark((function n(){var o,i,a,s,u,c,h,f,d;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=t.layout,i=o.id,a=o.options,s=o.iterations,u=t.nodes,c=t.edges,h=new A({nodes:u,edges:c}),!(f=$o[i])){e.next=7;break}wo=new f(a),e.next=8;break;case 7:throw new Error("Unknown layout id: ".concat(i));case 8:return e.next=10,wo.execute(h);case 10:return d=e.sent,Fr(wo)&&(wo.stop(),d=wo.tick(s)),e.abrupt("return",[d,r]);case 13:case"end":return e.stop()}}),n)})))}})})(),{}})()));
//# sourceMappingURL=598.min.js.map