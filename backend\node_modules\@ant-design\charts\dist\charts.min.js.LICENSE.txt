/*!
 * @antv/g-lite
 * @description A core module for rendering engine implements DOM API.
 * @version 2.2.16
 * @date 1/23/2025, 8:31:51 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*!
 * @antv/g-lite
 * @description A core module for rendering engine implements DOM API.
 * @version 2.2.19
 * @date 5/9/2025, 8:18:56 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*!
 * @antv/g-math
 * @description Geometry util
 * @version 3.0.1
 * @date 5/9/2025, 8:18:51 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*!
 * @antv/g-plugin-canvas-path-generator
 * @description A G plugin of path generator with Canvas2D API
 * @version 2.1.16
 * @date 1/23/2025, 8:32:57 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*!
 * @antv/g-plugin-canvas-path-generator
 * @description A G plugin of path generator with Canvas2D API
 * @version 2.1.19
 * @date 5/9/2025, 8:19:59 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*!
 * @antv/g-plugin-dragndrop
 * @description A G plugin for Drag n Drop implemented with PointerEvents
 * @version 2.0.32
 * @date 1/23/2025, 8:33:12 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*!
 * @antv/g-plugin-image-loader
 * @description A G plugin for loading image
 * @version 2.1.19
 * @date 2/27/2025, 8:27:22 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*!
 * @antv/g-web-animations-api
 * @description A simple implementation of Web Animations API.
 * @version 2.1.21
 * @date 1/23/2025, 8:33:55 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*!
 * @antv/g-web-animations-api
 * @description A simple implementation of Web Animations API.
 * @version 2.1.24
 * @date 5/9/2025, 8:20:56 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */

/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */

/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
