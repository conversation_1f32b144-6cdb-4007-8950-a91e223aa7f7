{"private": false, "name": "@ant-design/charts", "version": "2.3.0", "description": "AntV upper level visual component library", "bugs": {"url": "https://github.com/ant-design/ant-design-charts/issues"}, "repository": {"type": "git", "url": "git+https://github.com/ant-design/ant-design-charts.git"}, "main": "lib/index.js", "unpkg": "dist/charts.min.js", "module": "es/index.js", "types": "es/index.d.ts", "files": ["/lib", "/es", "/dist"], "dependencies": {"lodash": "^4.17.21", "@ant-design/graphs": "^2.1.0", "@ant-design/plots": "^2.4.0"}, "peerDependencies": {"react": ">=16.8.4", "react-dom": ">=16.8.4"}, "sideEffects": false, "license": "MIT", "devDependencies": {"open": "^10.1.0"}, "scripts": {"build": "run-s clean lib dist", "ci": "pnpm run build && pnpm run test:coverage", "clean": "rimraf lib es dist", "dist": "webpack --config webpack.config.js --mode production", "profile": "webpack --config webpack.config.js --mode production --profile --json > stats.json", "lib": "run-p lib:*", "lib:cjs": "tsc -p tsconfig.json --target ES5 --module commonjs --outDir lib", "lib:es": "tsc -p tsconfig.json --target ES5 --module ESNext --outDir es", "tag": "node ./scripts/tag.mjs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}}