import { Coordinate } from '@antv/coord';
import { Vector2 } from '../../../runtime';
import { LabelPosition } from './default';
/**
 * Spider label transform only suitable for the labels in polar coordinate,
 * labels should distinguish coordinate type.
 */
export declare function spider(position: LabelPosition, points: Vector2[], value: Record<string, any>, coordinate: Coordinate, options: Record<string, any>, labels: Vector2[][]): any;
