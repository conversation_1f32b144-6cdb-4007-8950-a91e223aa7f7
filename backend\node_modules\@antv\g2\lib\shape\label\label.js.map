{"version": 3, "file": "label.js", "sourceRoot": "", "sources": ["../../../src/shape/label/label.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,qDAA+C;AAE/C,6CAA+C;AAC/C,uDAAiE;AACjE,+CAA+C;AAC/C,6CAA0C;AAE1C,8DAAgD;AAUhD,SAAS,aAAa,CAAC,QAAuB,EAAE,UAAsB;IACpE,IAAI,QAAQ,KAAK,SAAS;QAAE,OAAO,QAAQ,CAAC;IAC5C,IAAI,IAAA,uBAAU,EAAC,UAAU,CAAC;QAAE,OAAO,QAAQ,CAAC;IAC5C,IAAI,IAAA,wBAAW,EAAC,UAAU,CAAC;QAAE,OAAO,OAAO,CAAC;IAC5C,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAiB,EACjB,KAA0B,EAC1B,UAAsB,EACtB,KAAc,EACd,OAAqB,EACrB,MAAmB;IAEnB,wDAAwD;IACxD,oCAAoC;IACpC,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAC3B,MAAM,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC9C,MAAM,SAAS,GAAG,MAAM;QACtB,CAAC,CAAC,WAAW;QACb,CAAC,CAAC,CAAC,KAAK,QAAQ;YAChB,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,OAAO,CAAC;IACZ,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;IAC3B,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACtC,MAAM,SAAS,GAAG,iBAAiB,CAAC,IAAA,kBAAS,EAAC,CAAC,CAAC,CAAC,CAAC;IAClD,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;KAC3C;IACD,uCACK,CAAC,GACD,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,EACvD;AACJ,CAAC;AAED;;;GAGG;AACI,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC1D,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IACtC,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAC3B,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;QACtC,MAAM,EACJ,IAAI,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EAAE,WAAW,GAAG,EAAE,EAC3B,eAAe,EACf,SAAS,GAAG,EAAE,KAEZ,KAAK,EADJ,aAAa,UACd,KAAK,EARH,+DAQL,CAAQ,CAAC;QACV,MAAM,KAIF,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,EAJhE,EACJ,MAAM,GAAG,CAAC,EACV,SAAS,GAAG,EAAE,OAEsD,EADjE,YAAY,cAHX,uBAIL,CAAqE,CAAC;QAEvE,OAAO,IAAA,kBAAM,EAAC,IAAI,iBAAO,EAAE,CAAC;aACzB,IAAI,CAAC,kBAAU,EAAE,YAAY,CAAC;aAC9B,KAAK,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;aACxB,KAAK,CAAC,WAAW,EAAE,GAAG,SAAS,WAAW,CAAC;aAC3C,KAAK,CACJ,WAAW,EACX,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5D;aACA,KAAK,CACJ,gBAAgB,EAChB,GAAG,SAAS,WAAW,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC,IAAI,EAAE,CACxD;aACA,KAAK,CAAC,sBAAsB,EAAE,eAAe,CAAC;aAC9C,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,SAAS,EAAE,CAAC;aAC5C,IAAI,CAAC,kBAAU,EAAE,aAAa,CAAC;aAC/B,IAAI,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AApCW,QAAA,KAAK,SAoChB;AAEF,aAAK,CAAC,KAAK,GAAG;IACZ,aAAa,EAAE,OAAO;CACvB,CAAC"}