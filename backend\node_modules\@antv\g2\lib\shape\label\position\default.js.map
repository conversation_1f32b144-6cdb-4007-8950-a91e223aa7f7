{"version": 3, "file": "default.js", "sourceRoot": "", "sources": ["../../../../src/shape/label/position/default.ts"], "names": [], "mappings": ";;;AAEA,gDAAoD;AACpD,0DAAiE;AACjE,kDAAwD;AACxD,kDAA4C;AAiB5C,SAAgB,qBAAqB,CACnC,QAAuB,EACvB,MAAiB,EACjB,KAA0B,EAC1B,UAAsB;IAEtB,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACzB,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;IACpC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAClB,MAAM,EAAE,GAAG,CAAC,OAAO,EAAE,EAAE;QACrB,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;QACjC,MAAM,EAAE,GAAG,IAAA,wBAAe,EAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,MAAM,EAAE,GAAG,IAAA,wBAAe,EAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,uCACK,OAAO,KACV,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,EAClB,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,IAClB;IACJ,CAAC,CAAC;IACF,eAAe;IACf,IAAI,QAAQ,KAAK,MAAM;QACrB,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC5E,IAAI,QAAQ,KAAK,OAAO;QACtB,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC1E,IAAI,QAAQ,KAAK,KAAK;QACpB,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;IAC1E,IAAI,QAAQ,KAAK,QAAQ;QACvB,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC7E,qBAAqB;IACrB,IAAI,QAAQ,KAAK,UAAU;QACzB,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;IACrE,IAAI,QAAQ,KAAK,WAAW;QAC1B,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;IACnE,IAAI,QAAQ,KAAK,aAAa;QAC5B,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC;IACxE,IAAI,QAAQ,KAAK,cAAc;QAC7B,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC;IACtE,0BAA0B;IAC1B,OAAO,EAAE,CAAC;QACR,CAAC,EAAE,CAAC,GAAG,CAAC;QACR,CAAC,EAAE,CAAC,GAAG,CAAC;QACR,SAAS,EAAE,QAAQ;QACnB,YAAY,EAAE,QAAQ;KACvB,CAAC,CAAC;AACL,CAAC;AA7CD,sDA6CC;AAED,SAAgB,gBAAgB,CAC9B,QAAuB,EACvB,MAAiB,EACjB,KAA0B,EAC1B,UAAsB;IAEtB,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,KAAK,CAAC;IACtD,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAE5D,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;IACrE,MAAM,KAAK,GAAG,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC7E,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;IAElE,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE;QAClB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC;QACxB,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC;QAC/D,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GACV,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAA,YAAG,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1E,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC,CAAC,EAAE,CAAC;IAEL,uCACK,KAAK,KACR,SAAS,EAAE,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EACrD,YAAY,EAAE,QAAQ,EACtB,MAAM,IACN;AACJ,CAAC;AA5BD,4CA4BC;AAED,SAAgB,UAAU,CAAC,MAAe,EAAE,KAAK,EAAE,MAAM;IACvD,OAAO;QACL,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM;QACpC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM;KACrC,CAAC;AACJ,CAAC;AALD,gCAKC;AAED,SAAgB,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,gBAAgB;IAC/D,IAAI,CAAC,UAAU;QAAE,OAAO,CAAC,CAAC;IAE1B,MAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrE,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC;AAC1C,CAAC;AALD,sCAKC;AAED,SAAS,uBAAuB,CAC9B,QAAuB,EACvB,MAAiB,EACjB,KAA0B,EAC1B,UAAsB;IAEtB,MAAM,EACJ,CAAC,EACD,EAAE,EACF,UAAU,EACV,gBAAgB,EAChB,MAAM,EAAE,WAAW,GAAG,GAAG,EACzB,MAAM,GAAG,CAAC,GACX,GAAG,KAAK,CAAC;IACV,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC5D,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;IAC3C,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IAEtC,MAAM,KAAK,GAAG,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;IAElE,MAAM,SAAS,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;IAC1E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC;IAC/C,MAAM,EAAE,GAAG,WAAW,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC;IACnE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC;IACvB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;IAE/C,uBACE,CAAC,EAAE,EAAE,EACL,CAAC,EAAE,EAAE,IACF,SAAS,EACZ;AACJ,CAAC;AAED,2DAA2D;AAC3D,6BAA6B;AAC7B,SAAS,cAAc,CAAC,CAAC;IACvB,OAAO,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC;AAED,SAAgB,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACzB,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;IACnB,OAAO;QACL,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxB,CAAC;AACJ,CAAC;AAPD,gDAOC;AAED,SAAgB,eAAe,CAC7B,QAAuB,EACvB,MAAiB,EACjB,KAA0B,EAC1B,UAAsB;IAEtB,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACzB,yBAAyB;IACzB,0CAA0C;IAC1C,sDAAsD;IACtD,wCAAwC;IACxC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;KAChE;IAED,MAAM,iBAAiB,GAAG,IAAA,qBAAQ,EAAC,UAAU,CAAC;QAC5C,CAAC,CAAC,gBAAgB;QAClB,CAAC,CAAC,IAAA,uBAAU,EAAC,UAAU,CAAC;YACxB,CAAC,CAAC,uBAAuB;YACzB,CAAC,CAAC,qBAAqB,CAAC;IAE1B,OAAO,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAChE,CAAC;AAtBD,0CAsBC"}