{"version": 3, "file": "outside.js", "sourceRoot": "", "sources": ["../../../../src/shape/label/position/outside.ts"], "names": [], "mappings": ";;;AAEA,gDAAoD;AACpD,0DAAiE;AAGjE,uCAMmB;AAEnB,SAAgB,UAAU,CAAC,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;IACzE,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAA,oBAAU,EAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACnD,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAA,oBAAU,EAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACpD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,OAAO;QACL,CAAC,EAAE,EAAE,EAAE,CAAC;QACR,CAAC,EAAE,EAAE,EAAE,CAAC;QACR,CAAC,EAAE,GAAG,IAAI,GAAG,OAAO,EAAE,EAAE,CAAC;KAC1B,CAAC;AACJ,CAAC;AATD,gCASC;AAED,SAAgB,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU;IAChD,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,UAAU,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACxE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC;IAC/C,OAAO,WAAW,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;AACnD,CAAC;AAJD,4BAIC;AAED,SAAgB,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU;IAC/C,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,UAAU,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACxE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;IAE3C,OAAO,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AACrC,CAAC;AALD,0BAKC;AAED,SAAgB,yBAAyB,CACvC,QAAuB,EACvB,MAAiB,EACjB,KAA0B,EAC1B,UAAsB;IAEtB,MAAM,EACJ,UAAU,EACV,gBAAgB,EAChB,MAAM,GAAG,CAAC,EACV,SAAS,GAAG,IAAI,EAChB,eAAe,GAAG,MAAM,EACxB,gBAAgB,GAAG,CAAC,EACpB,iBAAiB,GAAG,CAAC,GACtB,GAAG,KAAK,CAAC;IACV,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IAEtC,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IACjD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1C,MAAM,MAAM,GAAG,IAAA,uBAAa,EAAC,KAAK,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG;QAChB,SAAS,EAAE,IAAI,GAAG,CAAC,IAAI,IAAA,qBAAQ,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;QAC7D,YAAY,EAAE,QAAQ;QACtB,MAAM;KACP,CAAC;IAEF,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IACnD,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAEhE,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,UAAU,CAC/C,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CACjC,CAAC;IAEF,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,GAAG,EAAE,CAAC;IACb,MAAM,cAAc,GAAG;QACrB,SAAS;QACT,eAAe,EAAE;YACf,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YAChB,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;SACjB;KACF,CAAC;IAEF,qCACE,EAAE;QACF,EAAE,EACF,CAAC,EAAE,EAAE,GAAG,EAAE,EACV,CAAC,EAAE,EAAE,IACF,SAAS,GACT,cAAc,EACjB;AACJ,CAAC;AA1DD,8DA0DC;AAED,SAAgB,OAAO,CACrB,QAAuB,EACvB,MAAiB,EACjB,KAA0B,EAC1B,UAAsB;IAEtB,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACzB,yBAAyB;IACzB,0CAA0C;IAC1C,sDAAsD;IACtD,wCAAwC;IACxC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,IAAA,4BAAkB,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;KAChE;IAED,MAAM,iBAAiB,GAAG,IAAA,qBAAQ,EAAC,UAAU,CAAC;QAC5C,CAAC,CAAC,0BAAgB;QAClB,CAAC,CAAC,IAAA,uBAAU,EAAC,UAAU,CAAC;YACxB,CAAC,CAAC,yBAAyB;YAC3B,CAAC,CAAC,+BAAqB,CAAC;IAE1B,OAAO,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAChE,CAAC;AAtBD,0BAsBC"}