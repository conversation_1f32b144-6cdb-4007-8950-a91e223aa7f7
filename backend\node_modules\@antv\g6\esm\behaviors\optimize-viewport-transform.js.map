{"version": 3, "file": "optimize-viewport-transform.js", "sourceRoot": "", "sources": ["../../src/behaviors/optimize-viewport-transform.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAI1C,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAEpD,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAgC/C;;;;GAIG;AACH,MAAM,OAAO,yBAA0B,SAAQ,YAA8C;IAW3F,YAAY,OAAuB,EAAE,OAAyC;QAC5E,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,yBAAyB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QAL/E,iBAAY,GAAoB,EAAE,CAAC;QAEnC,cAAS,GAAY,IAAI,CAAC;QAO1B,0BAAqB,GAAG,CAC9B,QAAyB,EACzB,UAAwC,EACxC,MAA0C,EAC1C,EAAE;YACF,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC3C,IAAI,UAAU,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;oBACpD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAClC,CAAC;qBAAM,IAAI,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3E,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClE,CAAC;qBAAM,CAAC;oBACN,aAAa,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEM,iBAAY,GAAG,CAAC,IAAiB,EAAE,MAAkD,EAAE,EAAE;YAC/F,IAAI,UAAU,CAAC,MAAM,CAAC;gBAAE,OAAO,CAAC,KAAoB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,kBAAkB,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,IAAI,CAAC,CAAC;YAC1C,OAAO,CAAC,KAAoB,EAAE,EAAE;gBAC9B,IAAI,CAAC,KAAK,CAAC,SAAS;oBAAE,OAAO,IAAI,CAAC;gBAClC,OAAO,CAAC,CAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,CAAC;YACxD,CAAC,CAAC;QACJ,CAAC,CAAC;QAEM,eAAU,GAAG,CAAC,KAAqB,EAAE,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,OAAO;YAErD,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YACjC,MAAM,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YACrC,IAAI,CAAC,qBAAqB,CAAC,OAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;YAC7F,IAAI,CAAC,qBAAqB,CAAC,OAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;YAC7F,IAAI,CAAC,qBAAqB,CAAC,OAAQ,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YAC/F,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC,CAAC;QAEM,eAAU,GAAG,QAAQ,CAAC,CAAC,KAAqB,EAAE,EAAE;YACtD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS;gBAAE,OAAO;YAEpD,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YACjC,IAAI,CAAC,qBAAqB,CAAC,OAAQ,CAAC,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAC;YAC3D,IAAI,CAAC,qBAAqB,CAAC,OAAQ,CAAC,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAC;YAC3D,IAAI,CAAC,qBAAqB,CAAC,OAAQ,CAAC,SAAS,EAAE,EAAE,SAAS,CAAC,CAAC;YAC5D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QA/CxB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAgDO,UAAU;QAChB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAE/B,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACvD,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACxD,CAAC;IAEO,YAAY;QAClB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAE/B,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACxD,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAEO,QAAQ,CAAC,KAAqB;QACpC,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,UAAU,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7C,OAAO,CAAC,CAAC,MAAM,CAAC;IAClB,CAAC;IAEM,MAAM,CAAC,OAAkD;QAC9D,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC;;AA5FM,wCAAc,GAA8C;IACjE,MAAM,EAAE,IAAI;IACZ,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,CAAC,IAAiB,EAAE,EAAE,CAAC,IAAI,KAAK,MAAM;CAC/C,AAJoB,CAInB"}