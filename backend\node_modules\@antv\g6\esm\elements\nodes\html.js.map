{"version": 3, "file": "html.js", "sourceRoot": "", "sources": ["../../../src/elements/nodes/html.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAGL,qBAAqB,EACrB,IAAI,IAAI,KAAK,EAKb,IAAI,GACL,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAE9C,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AA8BvC;;;;;GAKG;AACH,MAAM,OAAO,IAAK,SAAQ,QAAwB;IAShD,YAAY,OAA4C;QACtD,KAAK,iCAAM,OAAO,KAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,KAAK,CAAC,IAAG,CAAC;QAGjF,qBAAgB,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;QA2EnD,kBAAa,GAAG,CAAC,WAAyB,EAAE,EAAE;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACnC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAK,CAAC,aAAc,CAAC,WAAY,CAAC;YAElF,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAE5E,gBAAgB,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,EAAE;gBAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;gBAChG,yCAAyC;gBACzC,iFAAiF;gBACjF,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,+BAA+B,EAAE,EAAE,CAAC,CAAC;gBACtE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IA1FF,CAAC;IAID,IAAY,YAAY;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;IAClD,CAAC;IAED,IAAY,MAAM;QAChB,OAAO;YACL,WAAW,CAAC,KAAK;YACjB,WAAW,CAAC,YAAY;YACxB,WAAW,CAAC,YAAY;YACxB,WAAW,CAAC,UAAU;YACtB,WAAW,CAAC,YAAY;YACxB,WAAW,CAAC,aAAa;SAC1B,CAAC;IACJ,CAAC;IAES,aAAa;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,aAAuC,IAAI,CAAC,gBAAgB,EAAE,YAAmB,IAAI;QACjG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAEzC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAES,WAAW,CAAC,UAAoC;QACxD,MAAM,KAIF,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,CAAC,CAA8B,EAJjG,EACJ,EAAE,GAAG,CAAC,EACN,EAAE,GAAG,CAAC,OAE+F,EADlG,KAAK,cAHJ,YAIL,CAAsG,CAAC;QACxG,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACjD,qCAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAK,KAAK,KAAE,KAAK,EAAE,MAAM,IAAG;IACnD,CAAC;IAES,YAAY,CAAC,UAAoC,EAAE,SAAgB;QAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC3C,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,SAAS,CAAE,CAAC;QACnG,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAEM,iBAAiB;QACtB,iCAAiC;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACzD,MAAM,gBAAgB,GAAG,QAAQ,YAAY,QAAQ,CAAC;QACtD,IAAI,CAAC,gBAAgB;YAAE,OAAO;QAE9B,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAChC,gDAAgD;YAChD,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,wBAAwB,CAAC,IAAS,EAAE,QAAa,EAAE,QAAa;QACrE,IAAI,IAAI,KAAK,QAAQ,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC/C,IAAI,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/C,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAChC,gDAAgD;YAChD,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC;IAiBO,uBAAuB,CAAC,KAAmB,EAAE,MAAe;QAClE,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrD,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAQ,CAAC;gBAE7C,sEAAsE;gBACtE,4CAA4C;gBAC5C,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC;oBAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBAChD,IAAI,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC;oBAAE,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;gBAClD,IAAI,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;oBACjC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC;gBAC9E,CAAC;gBACD,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC;oBAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;gBAC/D,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC;oBAAE,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;gBACjE,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC;oBAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;gBAC9C,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC;oBAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;gBAC9C,IAAI,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC;oBAAE,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;gBAChE,yEAAyE;gBACzE,IAAI,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC;oBAAE,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;gBAC1E,IAAI,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC;oBAAE,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC;gBACrE,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC;oBAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;gBAC9C,IAAI,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC;oBAAE,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC;gBACxE,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;gBAC1B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;gBAExB,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,KAAY,CAAC;YAC/B,IAAI,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC;gBAAE,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;YACjE,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;gBAAE,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YACtD,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC;gBAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YACxD,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;gBAAE,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YACtD,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;gBAAE,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YACtD,IAAI,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC;gBAAE,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC;YACxE,IAAI,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC;gBAAE,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC;YAC9D,IAAI,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAAE,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC;YAC9D,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;gBAAE,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YACtD,IAAI,WAAW,CAAC,SAAS,CAAC,kBAAkB,CAAC;gBAAE,SAAS,CAAC,kBAAkB,GAAG,CAAC,CAAC;YAChF,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;YAE9B,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,gBAAkC,CAAC;IAC5C,CAAC;IAEO,iBAAiB,CAAC,KAA0B,EAAE,WAAuB;QAC3E,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;QACxC,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC,UAA0B,CAAC;QAC1D,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACpC,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAE9B,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAClC,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QACpC,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QACpC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAClC,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QACpC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;QACrC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;QACrC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC;QACzC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC;QACzC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;QACjC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;QACjC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;QACrC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;QACrC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;IAC7B,CAAC;IAEO,cAAc,CACpB,KAA4B,EAC5B,eAA6B,EAC7B,IAAa,EACb,WAAmD;QAEnD,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,aAAa;QACb,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;QAC3B,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;QAEhC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;QAC5C,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;QACpC,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;QACtC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;QAC5C,KAAK,CAAC,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;QAChD,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;QAC1C,KAAK,CAAC,kBAAkB,GAAG,eAAe,CAAC,kBAAkB,CAAC;QAC9D,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;QACpC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;QACpC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;QACpC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QAE/C,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QACrD,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACrB,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3E,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;QACzB,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;QACzB,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEpC,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;QACxC,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAClC,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC;QAC5B,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,WAAsC;QAC1D,IAAI,CAAS,CAAC;QACd,IAAI,CAAS,CAAC;QACd,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,CAAC,GAAG,OAAO,CAAC;YACZ,CAAC,GAAG,OAAO,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;YAC5E,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;YACZ,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACd,CAAC;QACD,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IAES,OAAO;QACf,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,eAAe;QACf,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,OAAO,EAAE,CAAC;IACpD,CAAC;;AA1OM,sBAAiB,GAA4B;IAClD,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC;IACf,IAAI,EAAE,KAAK;IACX,IAAI,EAAE,KAAK;IACX,KAAK,EAAE,KAAK;IACZ,aAAa,EAAE,MAAM;CACtB,AANuB,CAMtB"}