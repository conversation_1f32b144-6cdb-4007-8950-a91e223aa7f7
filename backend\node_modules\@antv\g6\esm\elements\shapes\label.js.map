{"version": 3, "file": "label.js", "sourceRoot": "", "sources": ["../../../src/elements/shapes/label.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAA8B,IAAI,EAAkB,IAAI,EAAkB,MAAM,SAAS,CAAC;AAGjG,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAC/E,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAuBzC;;;;;;;;GAQG;AACH,MAAM,OAAO,KAAM,SAAQ,SAA0B;IAenD,YAAY,OAA6C;QACvD,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,iBAAiB,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAES,WAAW,CAAC,GAAW;QAC/B,OAAO,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAClC,CAAC;IAES,iBAAiB,CAAC,GAAW;QACrC,OAAO,UAAU,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;IACvC,CAAC;IAES,YAAY,CAAC,UAAqC;QAC1D,MAAM,KAAwB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAxD,EAAE,OAAO,OAA+C,EAA1C,KAAK,cAAnB,WAAqB,CAAmC,CAAC;QAC/D,OAAO,cAAc,CAAiB,KAAK,EAAE,YAAY,CAAC,CAAC;IAC7D,CAAC;IAES,kBAAkB,CAAC,UAAqC;QAChE,IAAI,UAAU,CAAC,UAAU,KAAK,KAAK;YAAE,OAAO,KAAK,CAAC;QAElD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAC/C,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;QACnD,MAAM,eAAe,GAAG,aAAa,CAAiB,KAAK,EAAE,YAAY,CAAC,CAAC;QAE3E,MAAM,EACJ,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EACjB,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAC1B,WAAW,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,GACrC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE3C,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,UAAU,GAAG,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;QAEhD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,eAAe,CAAC;QAC1C,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;YACpB,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACtG,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC7B,CAAC,EAAE,IAAI,GAAG,IAAI;gBACd,CAAC,EAAE,IAAI,GAAG,GAAG;gBACb,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU;gBACjF,MAAM,EAAE,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM;aACtC,CAAC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,eAAe,CAAC;QACnC,kDAAkD;QAClD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACvD,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;YACzD,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC;QAClG,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEM,MAAM,CAAC,aAAwC,IAAI,CAAC,gBAAgB,EAAE,YAAmB,IAAI;QAClG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC;IAClF,CAAC;IAEM,iBAAiB;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACnE,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;;AA9EM,uBAAiB,GAA6B;IACnD,OAAO,EAAE,CAAC;IACV,QAAQ,EAAE,EAAE;IACZ,UAAU,EAAE,uBAAuB;IACnC,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,CAAC;IACX,aAAa,EAAE,GAAG;IAClB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,QAAQ;IACtB,iBAAiB,EAAE,IAAI;IACvB,gBAAgB,EAAE,CAAC,CAAC;IACpB,mBAAmB,EAAE,CAAC;CACvB,CAAC"}