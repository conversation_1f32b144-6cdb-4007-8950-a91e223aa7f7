{"version": 3, "file": "viewport.js", "sourceRoot": "", "sources": ["../../src/runtime/viewport.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,IAAI,EAAW,MAAM,SAAS,CAAC;AACxC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAGzD,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC3G,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACnE,OAAO,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AACtC,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAGxD,MAAM,OAAO,kBAAkB;IAG7B,IAAY,OAAO;QACjB,OAAO,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC;IAED,IAAY,aAAa;QACvB,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QAChD,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAChF,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,YAAY,OAAuB;QA6B3B,oBAAe,GAAG,CAAC,CAAC;QA5B1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;QACpC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;QAC3D,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;IAChG,CAAC;IAED,IAAY,MAAM;QAChB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;YACnC,GAAG,EAAE,CAAC,MAAM,EAAE,IAAmB,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/F,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;gBAE7D,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC3B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;oBAChC,OAAO,CAAC,GAAG,IAAW,EAAE,EAAE;wBACxB,MAAM,MAAM,GAAI,KAAiC,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;wBACtE,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;4BACxB,MAAM,CAAC,IAAI,CAA6B,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;wBAChE,CAAC,CAAC,CAAC;wBAEH,OAAO,MAAM,CAAC;oBAChB,CAAC,CAAC;gBACJ,CAAC;YACH,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAIO,cAAc,CAAC,OAAyD;QAC9E,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,IAAI,CAAC,eAAe,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;IACnF,CAAC;IAEO,YAAY,CAAC,SAAyC;QAC5D,MAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC5E,IAAI,CAAC,cAAc;YAAE,OAAO,KAAK,CAAC;QAClC,OAAO,IAAI,mBAAM,cAAc,GAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAoD,CAAC;IAChH,CAAC;IAEM,aAAa;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QACrD,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACzB,CAAC;IAED;;;;;;;;;OASG;IACI,eAAe;QACpB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QACrD,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;;OASG;IACI,iBAAiB;QACtB,oCAAoC;QACpC,uCAAuC;QACvC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QACzC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;IACxE,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAEO,mBAAmB,CAAC,OAAyB;QACnD,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QACxB,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAEnC,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAC1C,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAExC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;QAExC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAEhD,OAAO,IAAI,KAAK,UAAU;YACxB,CAAC,CAAC;gBACE,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;gBAC9B,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC;aACnC;YACH,CAAC,CAAC;gBACE,QAAQ,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;gBAC3C,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;aAChD,CAAC;IACR,CAAC;IAEO,gBAAgB,CAAC,OAAyB;QAChD,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QACrC,MAAM,IAAI,GAAG,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QAC3E,OAAO,EAAE,IAAI,EAAE,CAAC;IAClB,CAAC;IAEO,cAAc,CAAC,OAAyB;QAC9C,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QACpC,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,SAAU,CAAC,CAAC;IACjF,CAAC;IAIY,SAAS,CAAC,OAAyB,EAAE,SAAyC;;YACzF,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YAC/B,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;YACrD,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAEhD,IAAI,CAAC,KAAK,EAAE,IAAI,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAC;YAErE,uDAAuD;YACvD,gIAAgI;YAChI,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,MAAiB,CAAC,CAAC;gBACpF,IAAI,CAAC,KAAK,EAAE,IAAI,aAAa,CAAC,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;gBACpE,OAAO;YACT,CAAC;YAED,MAAM,eAAe,GAAqD,EAAE,CAAC;YAC7E,IAAI,SAAS;gBAAE,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;YACjF,IAAI,QAAQ,CAAC,MAAM,CAAC;gBAAE,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;YACrF,IAAI,QAAQ,CAAC,KAAK,CAAC;gBAAE,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE5F,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,EAAE,IAAI,YAAY,CAAC,UAAU,CAAC,cAAc,EAAE,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;gBAEjG,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;oBACnC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;oBACjC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,kCACxD,UAAU,KACb,QAAQ,EAAE,GAAG,EAAE;4BACb,IAAI,CAAC,KAAK,EAAE,IAAI,YAAY,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;4BAChG,IAAI,CAAC,KAAK,EAAE,IAAI,aAAa,CAAC,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;4BACpE,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;4BACnC,OAAO,EAAE,CAAC;wBACZ,CAAC,IACD,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE;oBAC7D,QAAQ,EAAE,CAAC;iBACZ,CAAC,CAAC;gBAEH,IAAI,CAAC,KAAK,EAAE,IAAI,aAAa,CAAC,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;KAAA;IAEY,OAAO,CAAC,OAAwB,EAAE,SAAyC;;YACtF,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;YAChD,MAAM,EAAE,IAAI,GAAG,QAAQ,EAAE,SAAS,GAAG,MAAM,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;YAE9D,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACtD,MAAM,UAAU,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;YACxC,MAAM,WAAW,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;YAE1C,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACrD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC5D,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC;YAElE,MAAM,UAAU,GACd,CAAC,SAAS,KAAK,GAAG,IAAI,YAAY,IAAI,UAAU,CAAC;gBACjD,CAAC,SAAS,KAAK,GAAG,IAAI,aAAa,IAAI,WAAW,CAAC;gBACnD,CAAC,SAAS,KAAK,MAAM,IAAI,YAAY,IAAI,UAAU,IAAI,aAAa,IAAI,WAAW,CAAC,CAAC;YAEvF,IAAI,IAAI,KAAK,UAAU,IAAI,CAAC,UAAU;gBAAE,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;YAEnF,MAAM,MAAM,GAAG,UAAU,GAAG,YAAY,CAAC;YACzC,MAAM,MAAM,GAAG,WAAW,GAAG,aAAa,CAAC;YAC3C,MAAM,KAAK,GAAG,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAEjG,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,IAAI,CAAC,SAAS,CAClB;gBACE,IAAI,EAAE,UAAU;gBAChB,KAAK;gBACL,SAAS,EAAE,GAAG,CACZ,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAC7E,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAClC;aACF,EACD,UAAU,CACX,CAAC;QACJ,CAAC;KAAA;IAEY,SAAS,CAAC,OAAqB;;YAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACrD,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;KAAA;IAEY,aAAa;6DAAC,GAAS,EAAE,UAAwB,EAAE;YAC9D,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YACjC,IAAI,CAAC,OAAO;gBAAE,OAAO;YAErB,MAAM,WAAW,GAAG,CAAC,EAAW,EAAE,EAAE,CAClC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC;YAExF,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC;YAC9F,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;KAAA;IAEa,KAAK,CAAC,IAAU,EAAE,OAAqB;;YACnD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5D,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC3G,CAAC;KAAA;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,IAAU;QACjC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC1B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/B,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAEhD,MAAM,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QAClC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACnD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;;;;;;OAQG;IACI,YAAY,CAAC,MAAoB,EAAE,QAAQ,GAAG,KAAK,EAAE,SAAS,GAAG,CAAC;QACvE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAElC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAEjD,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,GAAG,eAAe,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,OAAO,CAAC,MAAM,CAAC;YACpB,CAAC,CAAC,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC;YACrC,CAAC,CAAC,CAAC,QAAQ;gBACT,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC;gBACjC,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC3C,CAAC;IAEM,eAAe;;QACpB,wCAAwC;QACxC,IAAI,MAAA,IAAI,CAAC,MAAM,CAAC,SAAS,0CAAE,MAAM,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACxC,CAAC;QACD,MAAA,IAAI,CAAC,iBAAiB,oDAAI,CAAC;IAC7B,CAAC;CACF"}