{"version": 3, "file": "update-related-edge.js", "sourceRoot": "", "sources": ["../../src/transforms/update-related-edge.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AACnC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAGjD;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,aAAa;IAC3C,UAAU,CAAC,KAAe,EAAE,OAAoB;QACrD,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;QAC1B,IAAI,KAAK,KAAK,YAAY;YAAE,OAAO,KAAK,CAAC;QAEzC,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/B,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GACjC,GAAG,KAAK,CAAC;QAEV,MAAM,eAAe,GAAG,CAAC,CAAe,EAAE,EAAM,EAAE,EAAE;YAClD,MAAM,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACvD,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAC5F,CAAC,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC/B,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAEhC,OAAO,KAAK,CAAC;IACf,CAAC;CACF"}