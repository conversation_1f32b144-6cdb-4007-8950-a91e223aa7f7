{"version": 3, "file": "layout.js", "sourceRoot": "", "sources": ["../../src/utils/layout.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAO,EAAE,KAAK,IAAI,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EAAE,UAAU,EAAE,MAAM,SAAS,CAAC;AASrC;;;;;;GAMG;AACH,MAAM,UAAU,aAAa,CAAC,OAAyB;IACrD,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IACzB,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC;IAChE,IAAI,IAAI,KAAK,YAAY,IAAI,OAAO,CAAC,WAAW;QAAE,OAAO,IAAI,CAAC;IAC9D,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,YAAY,CAAC,OAAyB;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IACzB,OAAO,CAAC,aAAa,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC7E,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,mBAAmB,CAAC,IAA6B;IAC/D,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,WAAW,CAAC,OAAuB;IACjD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAA,CAAC;AACvD,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,uBAAuB,CAAC,aAA4B;IAClE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,aAAa,CAAC;IACvC,MAAM,IAAI,GAAc,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IAE7D,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;QACzB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QACjE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC;QACtC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAC;YACX,EAAE,EAAE,QAAQ,CAAC,EAAQ;YACrB,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;SACnB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QACrB,MAAM,EACJ,EAAE,EACF,MAAM,EACN,MAAM,EACN,IAAI,EAAE,EAAE,MAAM,GAAG,EAAE,EAAE,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,GAC1E,GAAG,IAAI,CAAC;QAET,IAAI,CAAC,KAAM,CAAC,IAAI,CAAC;YACf,EAAE,EAAE,EAAQ;YACZ,MAAM,EAAE,MAAY;YACpB,MAAM,EAAE,MAAY;YACpB,KAAK,oBAKA,CAAC,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,EAAC,CAAC,CAAC,EAAE,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CACnF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,aAAa,CAC3B,IAA0D,EAC1D,OAAuB;IAEvB,MAAM,WAAY,SAAQ,UAAU;QAKlC,YAAY,OAAuB,EAAE,OAAiC;YACpE,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAE3B,IAAI,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAmB,EAAE,EAAE;oBAClC,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC7C,OAAO,uBAAuB,CAAC,UAAU,CAAC,CAAC;gBAC7C,CAAC,CAAC;YACJ,CAAC;QACH,CAAC;QAEY,OAAO,CAAC,KAAgB,EAAE,OAA0B;;gBAC/D,OAAO,uBAAuB,CAC5B,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CACzB,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,EACjC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAC1D,CACF,CAAC;YACJ,CAAC;SAAA;QAEO,gBAAgB,CAAC,OAAyB;YAChD,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;YAE3B,IAAI,CAAC,MAAM;gBAAE,OAAO,OAAO,CAAC;YAC5B,OAAO,CAAC,MAAM,GAAG,CAAC,IAAmB,EAAE,EAAE,CAAC,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;YAChF,OAAO,OAAO,CAAC;QACjB,CAAC;QAEM,qBAAqB,CAAC,IAAe;YAC1C,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;YACrD,MAAM,aAAa,GAAqB,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC1D,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,KAAc,KAAK,EAAd,IAAI,UAAK,KAAK,EAAvC,0BAA+B,CAAQ,CAAC;gBAE9C,MAAM,MAAM,GAAG;oBACb,EAAE;oBACF,IAAI,4EAGC,IAAI,KACP,IAAI,KAGD,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KACrC,KAAK,KACF,IAAI,CACR;iBACF,CAAC;gBACF,sBAAsB;gBACtB,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,CAAC;oBAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gBACzD,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,CAAC;oBAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gBACzD,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,CAAC;oBAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gBAEzD,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,aAAa,GAAG,KAAK;iBACxB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;gBACf,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;gBAChC,OAAO,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC1D,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACZ,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBAC7C,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,oBAAO,IAAI,CAAE,EAAE,KAAK,oBAAO,KAAK,CAAE,EAAE,CAAC;YACpF,CAAC,CAAC,CAAC;YAEL,MAAM,cAAc,GAAqB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5D,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,kBAAI,QAAQ,EAAE,IAAI,IAAK,KAAK,CAAC,IAAI,CAAE,EAAE,KAAK,oBAAO,KAAK,CAAC,KAAK,CAAE,EAAE,CAAC;YACjG,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,IAAI,QAAQ,CAAC;gBAC/B,KAAK,EAAE,CAAC,GAAG,aAAa,EAAE,GAAG,cAAc,CAAC;gBAC5C,KAAK,EAAE,aAAa;aACrB,CAAC,CAAC;YAEH,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;gBACpD,WAAW,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;gBAC3C,kDAAkD;gBAClD,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;oBACjE,IAAI,MAAM,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC7C,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;oBACvD,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,OAAO,WAAW,CAAC;QACrB,CAAC;KACF;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,kBAAkB,CAAC,MAAkB,EAAE,MAAc,EAAE,GAAG,IAAe;IACvF,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;QACrB,OAAQ,MAAc,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC1C,CAAC;IACD,4BAA4B;IAC5B,IAAI,UAAU,IAAI,MAAM,EAAE,CAAC;QACzB,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,CAAC;QAC1C,IAAI,MAAM,IAAI,QAAQ;YAAE,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAAkB,EAAE,IAAY;IAChE,IAAI,IAAI,IAAI,MAAM;QAAE,OAAQ,MAAc,CAAC,IAAI,CAAC,CAAC;IACjD,IAAI,UAAU,IAAI,MAAM,EAAE,CAAC;QACzB,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,CAAC;QAC1C,IAAI,IAAI,IAAI,QAAQ;YAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}