import type { CategoricalPalette } from '../palettes/types';
import type { PaletteOptions, STDPaletteOptions } from '../spec/element/palette';
import type { ElementData } from '../types/data';
/**
 * <zh/> 解析色板配置
 *
 * <en/> Parse palette options
 * @param palette - <zh/> 色板配置 | <en/> PaletteOptions options
 * @returns <zh/> 标准色板配置 | <en/> Standard palette options
 */
export declare function parsePalette(palette?: PaletteOptions): STDPaletteOptions | undefined;
/**
 * <zh/> 根据色板分配颜色
 *
 * <en/> Assign colors according to the palette
 * @param data - <zh/> 元素数据 | <en/> Element data
 * @param palette - <zh/> 色板配置 | <en/> PaletteOptions options
 * @returns <zh/> 元素颜色 | <en/> Element color
 * @remarks
 * <zh/> 返回值结果是一个以元素 id 为 key，颜色值为 value 的对象
 *
 * <en/> The return value is an object with element id as key and color value as value
 */
export declare function assignColorByPalette(data: ElementData, palette?: STDPaletteOptions): Record<string, string> | undefined;
/**
 * <zh/> 获取离散色板配色
 *
 * <en/> Get discrete palette colors
 * @param colorPalette - <zh/> 色板名或着颜色数组 | <en/> Palette name or color array
 * @returns <zh/> 色板上具体颜色 | <en/> Specific color on the palette
 */
export declare function getPaletteColors(colorPalette?: string | CategoricalPalette): CategoricalPalette | undefined;
