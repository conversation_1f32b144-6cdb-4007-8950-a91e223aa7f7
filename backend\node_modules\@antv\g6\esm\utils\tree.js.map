{"version": 3, "file": "tree.js", "sourceRoot": "", "sources": ["../../src/utils/tree.ts"], "names": [], "mappings": ";;;;;;;;;;;AAEA,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAQjC;;;;;;;GAOG;AACH,MAAM,UAAU,eAAe,CAAC,QAAkB,EAAE,MAAuB;IACzE,MAAM,EACJ,WAAW,GAAG,CAAC,KAAe,EAAE,KAAa,EAAE,EAAE;QAC/C,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,CAAC,QAAQ;YAAE,OAAO,KAAiB,CAAC;QAC9C,MAAM,EAAE,QAAQ,KAAmB,KAAK,EAAnB,SAAS,UAAK,KAAK,EAAlC,YAA0B,CAAQ,CAAC;QACzC,OAAO,gCAAK,SAAS,KAAE,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAc,CAAC;IACnF,CAAC,EACD,WAAW,GAAG,CAAC,MAAgB,EAAE,MAAgB,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,EAChG,WAAW,GAAG,CAAC,KAAe,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,GACxD,GAAG,MAAM,IAAI,EAAE,CAAC;IAEjB,MAAM,KAAK,GAAe,EAAE,CAAC;IAC7B,MAAM,KAAK,GAAe,EAAE,CAAC;IAE7B,GAAG,CACD,QAAQ,EACR,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QACd,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACrC,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QACnC,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC7B,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,EACD,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAC3B,IAAI,CACL,CAAC;IAEF,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAC1B,CAAC"}