{"version": 3, "file": "scroll-canvas.js", "sourceRoot": "", "sources": ["../../src/behaviors/scroll-canvas.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAAkD;AAClD,4CAA2C;AAG3C,wCAA6E;AAC7E,8CAAgD;AAChD,gDAA0D;AAC1D,4CAAqD;AAErD,mDAA+C;AAmE/C;;;;GAIG;AACH,MAAa,YAAa,SAAQ,4BAAiC;IAUjE,YAAY,OAAuB,EAAE,OAA4B;QAC/D,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QA2ClE,YAAO,GAAG,CAAO,KAAiB,EAAE,EAAE;YAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc;gBAAE,KAAK,CAAC,cAAc,EAAE,CAAC;YACxD,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;YAC3B,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;YAE3B,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAA,CAAC;QA/CA,IAAI,CAAC,QAAQ,GAAG,IAAI,mBAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,OAAqC;QACjD,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,UAAU;;QAChB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;QAC1B,IAAI,IAAA,eAAQ,EAAC,OAAO,CAAC,EAAE,CAAC;YACtB,MAAA,IAAI,CAAC,QAAQ,0CAAE,mBAAmB,CAAC,uBAAW,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;YAE9D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YAChE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACpE,CAAC;aAAM,CAAC;YACN;;;eAGG;YACH,MAAA,IAAI,CAAC,QAAQ,0CAAE,gBAAgB,CAAC,uBAAW,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,iBAAiB,EAAE,CAAC,aAAa,EAAE,CAAC;IAC5E,CAAC;IAUO,kBAAkB,CAAC,CAAQ;QACjC,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAErC,CAAC,GAAG,IAAA,iBAAQ,EAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAC7B,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAEzB,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,gBAAgB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAQ;QACtC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QACnC,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;YACtB,EAAE,GAAG,CAAC,CAAC;QACT,CAAC;aAAM,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;YAC7B,EAAE,GAAG,CAAC,CAAC;QACT,CAAC;QACD,OAAO,CAAC,EAAE,EAAE,EAAE,CAAU,CAAC;IAC3B,CAAC;IAEO,YAAY,CAAC,CAAC,EAAE,EAAE,EAAE,CAAQ;QAClC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAE1C,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QACrD,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,IAAA,sBAAY,EAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACpE,MAAM,KAAK,GAAG,CAAC,YAAY,GAAG,GAAG,EAAE,WAAW,GAAG,KAAK,EAAE,YAAY,GAAG,MAAM,EAAE,WAAW,GAAG,IAAI,CAAC,CAAC;QACnG,MAAM,cAAc,GAAG,IAAA,sBAAe,EAAC,IAAA,mBAAY,EAAC,QAAS,CAAC,eAAe,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QAEzF,MAAM,kBAAkB,GAAG,IAAA,iBAAQ,EAAC,QAAS,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,IAAA,oBAAa,EAAC,kBAAkB,EAAE,cAAc,CAAC,EAAE,CAAC;YACvD,MAAM,EACJ,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EACjB,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAClB,GAAG,cAAc,CAAC;YAEnB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;gBACzF,EAAE,GAAG,CAAC,CAAC;YACT,CAAC;YACD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;gBACzF,EAAE,GAAG,CAAC,CAAC;YACT,CAAC;QACH,CAAC;QACD,OAAO,CAAC,EAAE,EAAE,EAAE,CAAU,CAAC;IAC3B,CAAC;IAEa,MAAM,CAAC,KAAY,EAAE,KAAkC;;YACnE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,OAAO;YAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YACjC,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC,WAAW,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YAC/C,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,EAAI,CAAC;QACf,CAAC;KAAA;IAEO,QAAQ,CAAC,KAAkC;QACjD,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,IAAA,iBAAU,EAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7C,OAAO,CAAC,CAAC,MAAM,CAAC;IAClB,CAAC;IACD;;;;OAIG;IACI,OAAO;;QACZ,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACxB,MAAA,IAAI,CAAC,QAAQ,0CAAE,mBAAmB,CAAC,uBAAW,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACpE,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC;;AApIH,oCAqIC;AApIQ,2BAAc,GAAiC;IACpD,MAAM,EAAE,IAAI;IACZ,WAAW,EAAE,CAAC;IACd,cAAc,EAAE,IAAI;IACpB,KAAK,EAAE,QAAQ;CAChB,AALoB,CAKnB"}