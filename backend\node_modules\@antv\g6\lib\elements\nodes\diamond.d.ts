import type { DisplayObjectConfig } from '@antv/g';
import type { Point } from '../../types';
import type { PolygonStyleProps } from '../shapes';
import { Polygon } from '../shapes/polygon';
/**
 * <zh/> 菱形节点样式配置项
 *
 * <en/> Diamond node style props
 */
export interface DiamondStyleProps extends PolygonStyleProps {
}
/**
 * <zh/> 菱形节点
 *
 * <en/> Diamond node
 */
export declare class Diamond extends Polygon {
    constructor(options: DisplayObjectConfig<DiamondStyleProps>);
    protected getPoints(attributes: Required<DiamondStyleProps>): Point[];
}
