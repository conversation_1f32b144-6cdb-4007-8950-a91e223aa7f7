{"version": 3, "file": "snake.js", "sourceRoot": "", "sources": ["../../src/layouts/snake.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAEA,8CAAgD;AAChD,wCAA0C;AAC1C,+CAA2C;AAuD3C;;;;;;;;;;;;GAYG;AACH,MAAa,WAAY,SAAQ,wBAAU;IAA3C;;QACS,OAAE,GAAG,OAAO,CAAC;IAoHtB,CAAC;IA5GS,UAAU,CAAC,KAAiB,EAAE,IAAwC;QAC5E,MAAM,MAAM,GAAG,OAAO,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAA8B,CAAC;QAC9F,OAAO,KAAK,CAAC,MAAM,CACjB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACZ,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAA,gBAAS,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,CACP,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,QAAQ,CAAC,IAAe;QAC9B,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;QACxC,MAAM,QAAQ,GAA0B,EAAE,CAAC;QAC3C,MAAM,SAAS,GAA0B,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACtB,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACxB,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,kCAAkC;QAClC,MAAM,OAAO,GAAY,IAAI,GAAG,EAAE,CAAC;QACnC,MAAM,GAAG,GAAG,CAAC,MAAU,EAAE,EAAE;YACzB,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;gBAAE,OAAO;YAChC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC/B,CAAC,CAAC;QACF,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAEhD,sBAAsB;QACtB,8DAA8D;QAC9D,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACpE,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACnE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAErE,sBAAsB;QACtB,wEAAwE;QACxE,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QAChG,IAAI,WAAW,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,KAAK,CAAC;QAE1D,OAAO,IAAI,CAAC;IACd,CAAC;IAEK,OAAO,CAAC,KAAgB,EAAE,OAA4B;;;YAC1D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAC;YAExC,MAAM,EACJ,QAAQ,EAAE,YAAY,EACtB,OAAO,EAAE,WAAW,EACpB,MAAM,EACN,IAAI,EACJ,MAAM,EAAE,UAAU,EAClB,MAAM,EAAE,UAAU,EAClB,SAAS,EACT,KAAK,EACL,MAAM,GACP,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAiC,CAAC;YAEzG,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,IAAA,sBAAY,EAAC,WAAW,CAAC,CAAC;YAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE,YAAY,CAAC,CAAC;YAElE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;YAC1D,IAAI,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAChG,IAAI,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACjG,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,GAAG,CAAC;gBAAE,MAAM,GAAG,CAAC,CAAC;YAClD,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,GAAG,CAAC;gBAAE,MAAM,GAAG,CAAC,CAAC;YAElD,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,MAAA,KAAK,CAAC,KAAK,0CAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAEhF,MAAM,KAAK,GAAG,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;gBAC1C,MAAM,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC;gBAE9B,MAAM,cAAc,GAAG,SAAS;oBAC9B,CAAC,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC;wBAClB,CAAC,CAAC,QAAQ;wBACV,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ;oBACvB,CAAC,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC;wBAClB,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ;wBACrB,CAAC,CAAC,QAAQ,CAAC;gBAEf,MAAM,CAAC,GAAG,IAAI,GAAG,cAAc,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC3E,MAAM,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAEpE,OAAO;oBACL,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;iBAChB,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO,EAAE,KAAK,EAAE,CAAC;QACnB,CAAC;KAAA;;AApHH,kCAqHC;AAlHQ,0BAAc,GAAgC;IACnD,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC;IACP,SAAS,EAAE,IAAI;CAChB,AAJoB,CAInB;AAgHJ;;;;GAIG;AACH,SAAS,eAAe,CAAC,IAAe;IACtC,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;IACxC,MAAM,QAAQ,GAA0B,EAAE,CAAC;IAC3C,MAAM,OAAO,GAAwB,EAAE,CAAC;IAExC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QACrB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QACtB,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QACrB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,MAAM,KAAK,GAAS,EAAE,CAAC;IACvB,MAAM,WAAW,GAAe,EAAE,CAAC;IAEnC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QACrB,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,EAAG,CAAC;QAC9B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAE,CAAC;QACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEvB,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrB,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC"}