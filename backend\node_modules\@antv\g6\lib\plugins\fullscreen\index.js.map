{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plugins/fullscreen/index.ts"], "names": [], "mappings": ";;;AACA,6CAA0C;AAE1C,mDAAgD;AAEhD,gDAA4C;AA0C5C;;;;GAIG;AACH,MAAa,UAAW,SAAQ,wBAA6B;IAc3D,YAAY,OAAuB,EAAE,OAA0B;QAC7D,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QALhE,QAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,EAAG,CAAC;QAE1C,cAAS,GAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAqDrC,uBAAkB,GAAG,GAAG,EAAE;;YAChC,MAAM,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO;gBAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACpD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,OAAO,kDAAI,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,MAAM,kDAAI,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC;QAxDA,IAAI,CAAC,QAAQ,GAAG,IAAI,mBAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;;;;KAItB,CAAC;IACJ,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;QAE1B,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACzD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAEpC,MAAM,MAAM,GAAG,CAAC,wBAAwB,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;QAC3G,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAC3B,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;QAC1B,MAAM,MAAM,GAAG,CAAC,wBAAwB,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;QAC3G,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAC3B,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,UAAU,GAAG,IAAI;;QACpC,IAAI,KAAK,EAAE,MAAM,CAAC;QAClB,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,GAAG,CAAA,MAAA,UAAU,CAAC,MAAM,0CAAE,KAAK,KAAI,CAAC,CAAC;YACtC,MAAM,GAAG,CAAA,MAAA,UAAU,CAAC,MAAM,0CAAE,MAAM,KAAI,CAAC,CAAC;YACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;IAC9B,CAAC;IAYD;;;;OAIG;IACI,OAAO;QACZ,IAAI,QAAQ,CAAC,iBAAiB,IAAI,CAAC,mBAAmB,EAAE;YAAE,OAAO;QACjE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC,GAAU,EAAE,EAAE;YAChD,aAAK,CAAC,IAAI,CAAC,2CAA2C,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,IAAI;QACT,IAAI,CAAC,QAAQ,CAAC,iBAAiB;YAAE,OAAO;QACxC,QAAQ,CAAC,cAAc,EAAE,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,OAAmC;QAC/C,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QACpB,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC;;AAlHH,gCAmHC;AAlHQ,yBAAc,GAA+B;IAClD,OAAO,EAAE,EAAE;IACX,OAAO,EAAE,IAAI;CACd,AAHoB,CAGnB;AAiHJ;;;;;GAKG;AACH,SAAS,mBAAmB;IAC1B,OAAO,CACL,QAAQ,CAAC,iBAAiB;QAC1B,uEAAuE;QACvE,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,yBAAyB,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,sBAAsB,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAC7C,CAAC;AACJ,CAAC"}