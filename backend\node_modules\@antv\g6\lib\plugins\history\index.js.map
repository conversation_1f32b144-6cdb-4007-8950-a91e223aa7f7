{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plugins/history/index.ts"], "names": [], "mappings": ";;;;;;AAAA,wEAA+C;AAC/C,+CAA6C;AAC7C,4DAA8D;AAK9D,uCAAuC;AAEvC,gDAA4C;AAC5C,iCAAsC;AAmCtC;;;;;;;;GAQG;AACH,MAAa,OAAQ,SAAQ,wBAA0B;IAWrD,YAAY,OAAuB,EAAE,OAAuB;QAC1D,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QAP7D,iBAAY,GAA0B,IAAI,CAAC;QAC3C,mBAAc,GAAG,KAAK,CAAC;QACxB,cAAS,GAAc,EAAE,CAAC;QAC1B,cAAS,GAAc,EAAE,CAAC;QACzB,YAAO,GAAG,KAAK,CAAC;QAsFhB,mBAAc,GAAG,CAAC,GAAY,EAAE,MAAM,GAAG,IAAI,EAAE,EAAE;;YACvD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,cAAc,mDAAG,GAAG,CAAC,CAAC;YAEnC,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;YACnD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,IAAA,UAAK,EAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAC3D,MAAA,IAAI,CAAC,OAAO,CAAC,OAAO,0CAAE,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;YAExE,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC,CAAC;QAEM,eAAU,GAAG,CAAC,KAA0B,EAAE,EAAE;;YAClD,IAAI,IAAI,CAAC,OAAO;gBAAE,OAAO;YAEzB,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAU,CAAC,UAAU,EAAE,CAAC;gBACzC,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,SAAS,GAAG,IAAI,EAAE,GAAI,KAA6B,CAAC,IAAI,CAAC;gBAEnF,IAAI,MAAA,IAAI,CAAC,OAAO,CAAC,KAAK,0CAAE,UAAU,EAAE,CAAC;oBACnC,IAAI,CAAC,IAAI,CAAC,YAAY;wBAAE,OAAO;oBAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACpC,IAAI,CAAC,cAAc,KAAnB,IAAI,CAAC,cAAc,GAAK,SAAS,EAAC;oBAClC,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,YAAY,GAAG,CAAC,WAAW,CAAC,CAAC;gBAClC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAClC,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,IAAA,mBAAY,EAAC,IAAI,CAAC,YAAa,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC/F,IAAI,CAAC,MAAM,CAAC,sBAAY,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC;QAEM,qBAAgB,GAAG,CAAC,KAA0B,EAAE,EAAE;YACxD,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;YAChC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;gBACjC,IAAI,CAAC,GAAG;oBAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACrC,CAAC;QACH,CAAC,CAAC;QA5HA,IAAI,CAAC,OAAO,GAAG,IAAI,uBAAY,EAAE,CAAC;QAElC,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/B,KAAK,CAAC,EAAE,CAAC,sBAAU,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,KAAK,CAAC,EAAE,CAAC,sBAAU,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxD,KAAK,CAAC,EAAE,CAAC,sBAAU,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAClD,CAAC;IAED;;;;;OAKG;IACI,OAAO;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACI,OAAO;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACI,IAAI;;QACT,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,GAAG,EAAE,CAAC;YACR,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAEzB,MAAM,MAAM,GAAG,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,gBAAgB,mDAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,MAAM,KAAK,KAAK;gBAAE,OAAO;YAE7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,eAAe,mDAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,sBAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,GAAG,EAAE,CAAC;YACR,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,sBAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,aAAa;QAClB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,GAAG,EAAE,CAAC;YACR,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAChC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,sBAAY,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IA+CO,aAAa,CAAC,GAAY;;QAChC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAEnC,IAAI,SAAS,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;YAC1D,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;QAED,MAAM,MAAM,GAAG,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,gBAAgB,mDAAG,GAAG,EAAE,IAAI,CAAC,CAAC;QAC1D,IAAI,MAAM,KAAK,KAAK;YAAE,OAAO;QAE7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzB,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,eAAe,mDAAG,GAAG,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACI,KAAK;QACV,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,sBAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAEO,MAAM,CAAC,KAA2B,EAAE,GAAmB;QAC7D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAY,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC;IAED;;;;;;OAMG;IACI,EAAE,CAAC,KAA2B,EAAE,OAA8C;QACnF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACI,OAAO;QACZ,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/B,KAAK,CAAC,GAAG,CAAC,sBAAU,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAClD,KAAK,CAAC,GAAG,CAAC,sBAAU,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzD,KAAK,CAAC,GAAG,CAAC,sBAAU,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEjD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QAEnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACtB,CAAC;;AAxMH,0BAyMC;AAxMQ,sBAAc,GAA4B;IAC/C,SAAS,EAAE,CAAC;CACb,AAFoB,CAEnB"}