{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/plugins/history/util.ts"], "names": [], "mappings": ";;AAeA,kCASC;AAWD,oCAsDC;AAzFD,qCAAsC;AAItC,qDAA0D;AAC1D,+CAA0E;AAC1E,uCAAsC;AAEtC;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,SAA8B,EAAE,YAAiC;IAC3F,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;QAC5B,IAAI,IAAA,eAAQ,EAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;YAC1F,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;gBAAE,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YAC/C,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YAC3C,YAAY,CAAC,GAAG,CAAC,GAAG,IAAA,6BAAiB,EAAC,GAAG,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,YAAY,CAAC,OAAqB,EAAE,SAAS,GAAG,KAAK,EAAE,OAAwB;IAC7F,MAAM,GAAG,GAAG;QACV,SAAS;QACT,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;QAC5C,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;KACnC,CAAC;IAEb,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,0BAAiB,EAAC,IAAA,0BAAiB,EAAC,OAAO,CAAC,CAAC,CAAC;IAE7E,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAW,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;QAC3D,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrB,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAoD,EAAE,EAAE;;gBAChF,MAAM,QAAQ,qBAAQ,IAAI,CAAC,KAAK,CAAE,CAAC;gBACnC,IAAI,WAAW,qBAAQ,IAAI,CAAC,QAAQ,CAAE,CAAC;gBACvC,IAAI,OAAO,EAAE,CAAC;oBACZ,oBAAoB;oBACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,IAAA,SAAI,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACnE,MAAM,QAAQ,GAAG,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;oBACzD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAQ,CAAC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAChF,WAAW,GAAG,gCACT,IAAI,CAAC,QAAQ,KAChB,KAAK,kBAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,IAC7C,CAAC;gBACpB,CAAC;gBACD,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBACnC,MAAA,GAAG,CAAC,OAAO,CAAC,MAAM,EAAC,QAAQ,SAAR,QAAQ,IAAM,EAAE,EAAC;gBACnC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAChE,MAAA,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAC,QAAQ,SAAR,QAAQ,IAAM,EAAE,EAAC;gBACpC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClB,GAAG,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAiD,EAAE,EAAE;;gBAC1E,MAAM,QAAQ,qBAAQ,IAAI,CAAC,KAAK,CAAE,CAAC;gBACnC,MAAA,GAAG,CAAC,OAAO,CAAC,GAAG,EAAC,QAAQ,SAAR,QAAQ,IAAM,EAAE,EAAC;gBAChC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7D,MAAA,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAC,QAAQ,SAAR,QAAQ,IAAM,EAAE,EAAC;gBACpC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrB,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAoD,EAAE,EAAE;;gBAChF,MAAM,QAAQ,qBAAQ,IAAI,CAAC,KAAK,CAAE,CAAC;gBACnC,MAAA,GAAG,CAAC,OAAO,CAAC,MAAM,EAAC,QAAQ,SAAR,QAAQ,IAAM,EAAE,EAAC;gBACnC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAChE,MAAA,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAC,QAAQ,SAAR,QAAQ,IAAM,EAAE,EAAC;gBACjC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACb,CAAC"}