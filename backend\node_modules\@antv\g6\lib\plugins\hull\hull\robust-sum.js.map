{"version": 3, "file": "robust-sum.js", "sourceRoot": "", "sources": ["../../../../src/plugins/hull/hull/robust-sum.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;AAgBb,gDA8JC;AA5KD,SAAS,YAAY,CAAC,CAAS,EAAE,CAAS;IACxC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChB,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAElB,IAAI,CAAC,EAAE,CAAC;QACN,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,OAAO,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AAED,SAAgB,kBAAkB,CAAC,CAAW,EAAE,CAAW;IACzD,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACxB,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IAExB,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IAED,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;IACrB,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAClB,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IACjB,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAClB,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IACjB,IAAI,CAAC,EAAE,CAAC,CAAC;IAET,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;QACZ,CAAC,GAAG,EAAE,CAAC;QACP,KAAK,IAAI,CAAC,CAAC;QACX,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YACd,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QACf,CAAC;IACH,CAAC;SAAM,CAAC;QACN,CAAC,GAAG,EAAE,CAAC;QACP,KAAK,IAAI,CAAC,CAAC;QACX,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YACd,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QACf,CAAC;IACH,CAAC;IAED,IAAI,CAAC,KAAK,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;QAC3C,CAAC,GAAG,EAAE,CAAC;QACP,KAAK,IAAI,CAAC,CAAC;QACX,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YACd,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QACf,CAAC;IACH,CAAC;SAAM,CAAC;QACN,CAAC,GAAG,EAAE,CAAC;QACP,KAAK,IAAI,CAAC,CAAC;QACX,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YACd,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QACf,CAAC;IACH,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACd,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACf,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IAEX,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAE3B,OAAO,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;QAChC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;YACZ,CAAC,GAAG,EAAE,CAAC;YACP,KAAK,IAAI,CAAC,CAAC;YACX,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBACf,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;gBACd,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,CAAC,GAAG,EAAE,CAAC;YACP,KAAK,IAAI,CAAC,CAAC;YACX,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBACf,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;gBACd,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,CAAC;QACH,CAAC;QAED,CAAC,GAAG,EAAE,CAAC;QACP,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACV,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QACX,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAEX,IAAI,CAAC,EAAE,CAAC;YACN,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC;QAED,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACZ,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;QACd,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;QACf,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACd,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;QACf,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;QACf,EAAE,GAAG,EAAE,CAAC;IACV,CAAC;IAED,OAAO,KAAK,GAAG,EAAE,EAAE,CAAC;QAClB,CAAC,GAAG,EAAE,CAAC;QACP,CAAC,GAAG,EAAE,CAAC;QACP,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACV,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QACX,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAEX,IAAI,CAAC,EAAE,CAAC;YACN,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC;QAED,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACZ,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;QACd,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;QACf,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACd,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;QACf,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;QACf,EAAE,GAAG,EAAE,CAAC;QAER,KAAK,IAAI,CAAC,CAAC;QACX,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;IACH,CAAC;IAED,OAAO,KAAK,GAAG,EAAE,EAAE,CAAC;QAClB,CAAC,GAAG,EAAE,CAAC;QACP,CAAC,GAAG,EAAE,CAAC;QACP,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACV,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QACX,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAEX,IAAI,CAAC,EAAE,CAAC;YACN,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC;QAED,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACZ,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;QACd,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;QACf,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACd,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;QACf,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;QACf,EAAE,GAAG,EAAE,CAAC;QAER,KAAK,IAAI,CAAC,CAAC;QACX,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;IACH,CAAC;IAED,IAAI,EAAE,EAAE,CAAC;QACP,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,CAAC;IACD,IAAI,EAAE,EAAE,CAAC;QACP,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB,CAAC;IACD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC;IACnB,CAAC;IACD,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACjB,OAAO,CAAC,CAAC;AACX,CAAC"}