import { type PathArray } from '@antv/util';
import type { Point } from '../../types';
/**
 * <zh/> 计算 Hull 路径
 *
 * <en/> Compute Hull Path
 * @param points - <zh/> 顶点列表 | <en/> Vertices of Hull
 * @param padding - <zh/> 内边距 | <en/> padding
 * @param corner - <zh/> 拐角类型，目前支持 'sharp'、'rounded' 和 'smooth' | <en/> Corner type, currently supports 'sharp', 'rounded' and 'smooth'
 * @returns <zh/> Hull 路径 | <en/> Hull Path
 */
export declare function computeHullPath(points: Point[], padding: number, corner: 'rounded' | 'smooth' | 'sharp'): PathArray;
