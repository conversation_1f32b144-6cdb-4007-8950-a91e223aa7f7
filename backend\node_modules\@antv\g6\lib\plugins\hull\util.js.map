{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/plugins/hull/util.ts"], "names": [], "mappings": ";;AAiBA,0CAiBC;AAhCD,2CAAwD;AACxD,2CAAmD;AACnD,6CAAiE;AACjE,+CAAiH;AAGjH;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAAC,MAAe,EAAE,OAAe,EAAE,MAAsC;IACtG,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACnF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC9E,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,IAAA,uBAAe,EAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,IAAA,mBAAW,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAAE,OAAO,oBAAoB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACtF,CAAC;IAED,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,QAAQ;YACX,OAAO,wBAAwB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACnD,KAAK,OAAO;YACV,OAAO,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAClD,KAAK,SAAS,CAAC;QACf;YACE,OAAO,yBAAyB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,sBAAsB,GAAG,CAAC,KAAY,EAAE,OAAe,EAAE,MAA6B,EAAa,EAAE;IACzG,IAAI,MAAM,KAAK,OAAO;QACpB,OAAO;YACL,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;YAC7C,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;YAC7C,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;YAC7C,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;YAC7C,CAAC,GAAG,CAAC;SACN,CAAC;IACJ,MAAM,OAAO,GAA6C,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtF,OAAO;QACL,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QACnC,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QAC/C,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;KAChD,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,oBAAoB,GAAG,CAAC,MAAe,EAAE,OAAe,EAAE,MAA6B,EAAa,EAAE;IAC1G,MAAM,OAAO,GAA6C,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtF,MAAM,MAAM,GACV,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,IAAA,YAAG,EAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAA,cAAK,EAAC,IAAA,kBAAS,EAAC,IAAA,iBAAQ,EAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7G,MAAM,MAAM,GACV,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,IAAA,YAAG,EAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAA,cAAK,EAAC,IAAA,kBAAS,EAAC,IAAA,iBAAQ,EAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAE7G,MAAM,YAAY,GAAG,IAAA,cAAK,EAAC,IAAA,kBAAS,EAAC,IAAA,sBAAa,EAAC,IAAA,iBAAQ,EAAC,MAAM,EAAE,MAAM,CAAY,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC1G,MAAM,eAAe,GAAG,IAAA,cAAK,EAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;IAEhD,MAAM,IAAI,GAAG,IAAA,YAAG,EAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACvC,MAAM,OAAO,GAAG,IAAA,YAAG,EAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC1C,MAAM,EAAE,GAAG,IAAA,YAAG,EAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IACxC,MAAM,EAAE,GAAG,IAAA,YAAG,EAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IAExC,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;QACvB,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACnH,CAAC;IAED,OAAO;QACL,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;KACpC,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,yBAAyB,GAAG,CAAC,MAAe,EAAE,OAAe,EAAa,EAAE;IAChF,MAAM,QAAQ,GAAG,IAAA,uBAAe,EAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;QAC1D,MAAM,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3D,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1D,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAC/B,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAE/B,MAAM,EAAE,GAAG,IAAA,iBAAQ,EAAC,KAAK,EAAE,IAAI,CAAY,CAAC;QAC5C,MAAM,EAAE,GAAG,IAAA,iBAAQ,EAAC,IAAI,EAAE,OAAO,CAAY,CAAC;QAC9C,MAAM,EAAE,GAAG,IAAA,iBAAQ,EAAC,OAAO,EAAE,IAAI,CAAY,CAAC;QAE9C,+CAA+C;QAC/C,MAAM,SAAS,GAAG,CAAC,EAAW,EAAE,EAAW,EAAW,EAAE;YACtD,OAAO,IAAA,cAAK,EAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACvC,CAAC,CAAC;QACF,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAEtC,MAAM,YAAY,GAAG,CAAC,CAAU,EAAE,EAAE,CAAC,IAAA,cAAK,EAAC,IAAA,kBAAS,EAAC,IAAA,sBAAa,EAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACxF,MAAM,MAAM,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC;QAChC,OAAO;YACL;gBACE,CAAC,EAAE,IAAA,kBAAS,EAAC,WAAW,CAAC,CAAC,CAAC,IAAA,YAAG,EAAC,IAAI,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,YAAG,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC3E,OAAO,EAAE,WAAW,IAAI,IAAI;aAC7B;YACD;gBACE,CAAC,EAAE,IAAA,kBAAS,EAAC,WAAW,CAAC,CAAC,CAAC,IAAA,YAAG,EAAC,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,YAAG,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACjF,OAAO,EAAE,WAAW,IAAI,OAAO;aAChC;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,MAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CACnC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CACb,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QACjE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QACjE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO;QACnB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO;QACnB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CACtB,CAAC;IACF,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;IACxF,IAAI,aAAa,GAAY,EAAE,CAAC;IAChC,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;QAC3C,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,MAAM,WAAW,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,CAAC;YAAE,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YACxB,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YACxB,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,YAAY,CAAC,GAAG,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAClD,aAAa,GAAG,EAAE,CAAC;QACrB,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC,CAAc,CAAC;AAClB,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,wBAAwB,GAAG,CAAC,MAAe,EAAE,OAAe,EAAa,EAAE;IAC/E,MAAM,UAAU,GAAG,IAAA,uBAAe,EAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACtD,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,IAAA,kBAAS,EAAC,IAAA,iBAAQ,EAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,kFAAkF;IAClF,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;QAC3B,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;QAClD,MAAM,YAAY,GAAG,IAAA,kBAAS,EAAC,IAAA,YAAG,EAAC,KAAK,EAAE,IAAA,cAAK,EAAC,EAAE,CAAC,CAAC,EAAE,IAAA,cAAK,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,EAAE,CAAC,CAAC,GAAG,IAAA,YAAG,EAAC,EAAE,CAAC,CAAC,EAAE,IAAA,cAAK,EAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,OAAO,IAAA,sBAAe,EAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,uBAAuB,GAAG,CAAC,MAAe,EAAE,OAAe,EAAa,EAAE;IAC9E,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;QACzC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,IAAA,kBAAS,EAAC,IAAA,cAAK,EAAC,IAAA,kBAAS,EAAC,IAAA,sBAAa,EAAC,IAAA,iBAAQ,EAAC,IAAI,EAAE,OAAO,CAAY,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;QAC9G,OAAO,CAAC,IAAA,YAAG,EAAC,IAAI,EAAE,MAAM,CAAC,EAAE,IAAA,YAAG,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;IAE5B,MAAM,QAAQ,GAAG,GAAG;SACjB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACZ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAC7B,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAmB,CAAC;QAC9E,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAmB,CAAC;QACpF,OAAO,IAAA,2BAAoB,EAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC,CAAC;SACD,MAAM,CAAC,OAAO,CAAY,CAAC;IAC9B,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAc,CAAC;AAC5G,CAAC,CAAC"}