{"version": 3, "file": "legend.js", "sourceRoot": "", "sources": ["../../src/plugins/legend.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,+CAAsD;AAGtD,qCAA6C;AAC7C,4CAA0C;AAK1C,+CAA2C;AAC3C,2CAAoD;AAwEpD;;;;;;;;GAQG;AACH,MAAa,MAAO,SAAQ,wBAAyB;IA0BnD,YAAY,OAAuB,EAAE,OAAsB;QACzD,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QAb5D,eAAU,GAAG,UAAU,CAAC;QACxB,SAAI,GAAG,KAAK,CAAC;QACb,aAAQ,GAAG;YACjB,IAAI,EAAE,IAAI,GAAG,EAAgB;YAC7B,IAAI,EAAE,IAAI,GAAG,EAAgB;YAC7B,KAAK,EAAE,IAAI,GAAG,EAAgB;SAC/B,CAAC;QACM,kBAAa,GAAa,EAAE,CAAC;QAgC7B,eAAU,GAAG,GAAG,EAAE;YACxB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YAC/B,KAAK,CAAC,EAAE,CAAC,sBAAU,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACtD,CAAC,CAAC;QAEM,gBAAW,GAAG,CAAC,EAAa,EAAE,KAAsB,EAAE,EAAE;YAC9D,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;YAC5B,MAAM,SAAS,GAAG,IAAA,UAAG,EAAC,EAAE,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,IAAA,UAAG,EAAC,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAkC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAElF,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC;QAEF;;;;;WAKG;QACI,UAAK,GAAG,CAAC,KAAgB,EAAE,EAAE;YAClC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO;gBAAE,OAAO;YAC7C,MAAM,SAAS,GAAG,IAAA,UAAG,EAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACnC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;gBAC7E,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC;QAEF;;;;;WAKG;QACI,eAAU,GAAG,CAAC,KAAgB,EAAE,EAAE;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO;gBAAE,OAAO;YAC7C,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC;QAEF;;;;;WAKG;QACI,eAAU,GAAG,CAAC,KAAgB,EAAE,EAAE;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO;gBAAE,OAAO;YAC7C,MAAM,SAAS,GAAG,IAAA,UAAG,EAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACnC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC,CAAC;QAsBM,gBAAW,GAAG,CAAC,KAAa,EAAE,EAAM,EAAE,IAAiB,EAAE,EAAE;YACjE,IAAI,CAAC,KAAK;gBAAE,OAAO;YACnB,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,GAAG;gBAAE,OAAO;YACjB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpB,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC3B,IAAI,GAAG,EAAE,CAAC;oBACR,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACb,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEM,cAAS,GAAG,GAAG,EAAE;YACvB,OAAO;gBACL,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC;QACJ,CAAC,CAAC;QAEM,kBAAa,GAAG,CAAC,KAAgD,EAAE,WAAwB,EAAE,EAAE;YACrG,IAAI,CAAC,KAAK;gBAAE,OAAO,EAAE,CAAC;YACtB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YACxC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;YACjD,MAAM,KAAK,GAA6B,EAAE,CAAC;YAE3C,MAAM,QAAQ,GAAG,CAAC,IAAkB,EAAE,EAAE;gBACtC,IAAI,IAAA,iBAAU,EAAC,KAAK,CAAC;oBAAE,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC1C,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,MAAM;aACd,CAAC;YAEF,sCAAsC;YACtC,8DAA8D;YAC9D,MAAM,aAAa,GAA8B;gBAC/C,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,QAAQ,EAAE,0BAA0B;gBAC7C,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,UAAU;gBACpB,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,KAAK;gBACZ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,IAAI;gBACf,kBAAkB,EAAE,QAAQ;gBAC5B,gBAAgB,EAAE,MAAM;aACzB,CAAC;YAEF,MAAM,eAAe,GAAG,CAAC,IAAiB,EAAE,KAAmB,EAAE,EAAE;gBACjE,MAAM,KAAK,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;YAEF,MAAM,eAAe,GAAG,CAAC,IAAoB,EAAE,IAAiB,EAAE,EAAE;gBAClE,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACpB,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;oBACpB,MAAM,KAAK,GAAG,IAAA,UAAG,EAAC,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAClD,MAAM,MAAM,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,KAAI,QAAQ,CAAC;oBAC/D,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC1C,MAAM,KAAK,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAC,CAAC,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,CAAC,IAAI,SAAS,CAAC;oBAE3E,IAAI,EAAE,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;wBAC7C,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;wBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;4BAClB,KAAK,CAAC,KAAK,CAAC,GAAG;gCACb,EAAE,EAAE,GAAG,IAAI,KAAK,EAAE,EAAE;gCACpB,KAAK,EAAE,KAAK;gCACZ,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC;gCAClD,WAAW,EAAE,IAAI;gCACjB,SAAS,EAAE,CAAC;gCACZ,MAAM,EAAE,KAAK;gCACb,IAAI,EAAE,KAAK;6BACZ,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,QAAQ,WAAW,EAAE,CAAC;gBACpB,KAAK,MAAM;oBACT,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;oBAC/B,MAAM;gBACR,KAAK,MAAM;oBACT,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;oBAC/B,MAAM;gBACR,KAAK,OAAO;oBACV,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBACjC,MAAM;gBACR;oBACE,OAAO,EAAE,CAAC;YACd,CAAC;YAED,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC;QA0BM,kBAAa,GAAG,GAAG,EAAE;YAC3B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YACD,MAAM,KAYF,IAAI,CAAC,OAAO,EAZV,EACJ,KAAK,EACL,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,OAAO,EACP,QAAQ,EACR,SAAS,EACT,cAAc,EACd,SAAS,OAEK,EADX,IAAI,cAXH,8HAYL,CAAe,CAAC;YACjB,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACxD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAC3D,MAAM,KAAK,GAAG,CAAC,GAAG,SAAS,EAAE,GAAG,UAAU,EAAE,GAAG,SAAS,CAAC,CAAC;YAE1D,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CACjC;gBACE,KAAK;gBACL,MAAM;gBACN,IAAI,EAAE,KAAK;gBACX,mBAAmB,EAAE,CAAC,EAAE,SAAS,EAAS,EAAE,EAAE,CAAC,SAAS;gBACxD,UAAU,EAAE,CAAC,EAAE,MAAM,EAAS,EAAE,EAAE,CAAC,MAAM;gBACzC,gBAAgB,EAAE,CAAC,EAAE,MAAM,EAAS,EAAE,EAAE,CAAC,MAAM;gBAC/C,cAAc,EAAE,CAAC,EAAE,IAAI,EAAS,EAAE,EAAE,CAAC,IAAI;gBACzC,OAAO,EAAE,SAAS,CAAC,MAAM;aAC1B,EACD,IAAI,EACJ,IAAI,CAAC,SAAS,EAAE,CACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAI,oBAAQ,CAAC;gBAC5B,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,aAAa;aACrB,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACnC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAE7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC,CAAC;QA7RA,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,OAA+B;QAC3C,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEO,KAAK;;QACX,MAAA,IAAI,CAAC,MAAM,0CAAE,OAAO,EAAE,CAAC;QACvB,MAAA,IAAI,CAAC,SAAS,0CAAE,MAAM,EAAE,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IACpB,CAAC;IAiED;;;;OAIG;IACI,aAAa;QAClB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACnB,iBAAiB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAAE,OAAO,CAAC,CAAC;gBAC5E,OAAO,GAAG,CAAC;YACb,CAAC;YACD,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAAE,OAAO,CAAC,CAAC;gBAC5E,OAAO,GAAG,CAAC;YACb,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IA2GO,YAAY;QAClB,IAAI,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC;QAEpC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACxC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QAE1D,MAAM,EAAE,KAAK,GAAG,WAAW,EAAE,MAAM,GAAG,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QACpH,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,GAAG,IAAA,2BAAkB,EAAC;YAC9C,KAAK;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,cAAc;YACd,SAAS,EAAE,QAAQ;YACnB,SAAS,EAAE,QAAQ;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;QAC5B,IAAI,SAAS;YAAE,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAoDD;;;;;OAKG;IACI,OAAO;QACZ,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,sBAAU,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAClE,KAAK,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC;;AArUH,wBAsUC;AArUQ,qBAAc,GAA2B;IAC9C,QAAQ,EAAE,QAAQ;IAClB,OAAO,EAAE,OAAO;IAChB,WAAW,EAAE,YAAY;IACzB,MAAM,EAAE,MAAM;IACd,WAAW,EAAE,CAAC;IACd,UAAU,EAAE,EAAE;IACd,UAAU,EAAE,EAAE;IACd,cAAc,EAAE,EAAE;IAClB,iBAAiB,EAAE,EAAE;IACrB,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,GAAG;CACZ,AAZoB,CAYnB"}