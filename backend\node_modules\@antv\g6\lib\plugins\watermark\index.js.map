{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plugins/watermark/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA,gDAA4C;AAC5C,sCAAqD;AACrD,iCAA6D;AAmK7D;;;;;;;;GAQG;AACH,MAAa,SAAU,SAAQ,wBAA4B;IAgBzD,YAAY,OAAuB,EAAE,OAAyB;QAC5D,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QAH/D,aAAQ,GAAgB,IAAA,2BAAqB,EAAC,WAAW,CAAC,CAAC;QAKjE,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;QACtD,UAAW,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACU,MAAM,CAAC,OAAkC;;;;;YACpD,OAAM,MAAM,YAAC,OAAO,EAAE;YAEtB,MAAM,KAA6C,IAAI,CAAC,OAAO,EAAzD,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,OAA0B,EAArB,IAAI,cAAxC,uCAA0C,CAAe,CAAC;YAEhE,4BAA4B;YAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAChC,IAAI,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;oBACjC,0BAA0B;oBAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC1C,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,2BAA2B;YAC3B,MAAM,MAAM,GAAG,QAAQ;gBACrB,CAAC,CAAC,MAAM,IAAA,wBAAiB,EAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC;gBACxD,CAAC,CAAC,MAAM,IAAA,uBAAgB,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,MAAM,GAAG,CAAC;QACzD,CAAC;KAAA;IAED;;;;;OAKG;IACI,OAAO;QACZ,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,6BAA6B;QAC7B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;;AA9DH,8BA+DC;AA9DQ,wBAAc,GAA8B;IACjD,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,GAAG;IACZ,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;IACpB,IAAI,EAAE,EAAE;IACR,QAAQ,EAAE,MAAM;IAChB,YAAY,EAAE,EAAE;IAChB,SAAS,EAAE,QAAQ;IACnB,YAAY,EAAE,QAAQ;IACtB,gBAAgB,EAAE,QAAQ;CAC3B,AAXoB,CAWnB"}