/**
 * 从文本获取水印的 base64
 * @param width - width
 * @param height - height
 * @param text - 样式
 * @param style - 样式
 * @returns 水印的 base64
 */
export declare function getTextWatermark(width: number, height: number, text: string, style: any): Promise<string>;
/**
 * Get the image base64 of the watermark.
 * @param width - width
 * @param height - height
 * @param imageURL - image URL
 * @param style - 样式
 * @returns 水印的 base64
 */
export declare function getImageWatermark(width: number, height: number, imageURL: string, style: any): Promise<unknown>;
