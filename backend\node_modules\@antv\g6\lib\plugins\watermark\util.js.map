{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/plugins/watermark/util.ts"], "names": [], "mappings": ";;;;;;;;;;;AA4BA,4CAkCC;AAUD,8CAoBC;AA5FD,yBAAyB;AACzB,IAAI,MAAyB,CAAC;AAE9B;;;;;GAKG;AACH,SAAS,YAAY,CAAC,KAAa,EAAE,MAAc;IACjD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IACD,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACpC,GAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACpC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,SAAsB,gBAAgB,CAAC,KAAa,EAAE,MAAc,EAAE,IAAY,EAAE,KAAU;;QAC5F,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC3C,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAE,CAAC;QAErC,MAAM,EACJ,MAAM,EACN,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,eAAe,EACf,cAAc,EACd,SAAS,EACT,YAAY,GACb,GAAG,KAAK,CAAC;QAEV,iBAAiB;QACjB,sCAAsC;QACtC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;QAC1B,GAAG,CAAC,YAAY,GAAG,YAAY,CAAC;QAChC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QAErC,GAAG,CAAC,IAAI,GAAG,GAAG,YAAY,MAAM,cAAc,IAAI,eAAe,IAAI,cAAc,EAAE,CAAC;QAEtF,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC;QACvC,IAAI,QAAQ,EAAE,CAAC;YACb,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC;YACzB,iBAAiB;YACjB,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,qBAAqB;QACrB,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC5B,CAAC;CAAA;AAED;;;;;;;GAOG;AACH,SAAsB,iBAAiB,CAAC,KAAa,EAAE,MAAc,EAAE,QAAgB,EAAE,KAAU;;QACjG,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC3C,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAE,CAAC;QACrC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;QAElC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC;QAEvC,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;QACxB,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;QAC9B,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC;QAEnB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,GAAG,CAAC,MAAM,GAAG;gBACX,MAAM,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7D,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjE,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;gBACjG,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAC9B,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CAAA"}