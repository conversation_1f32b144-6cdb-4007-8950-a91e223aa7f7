{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/registry/extension/index.ts"], "names": [], "mappings": ";;;AAIA,2CAA6C;AAC7C,qDAAwD;AACxD,6CAA0C;AAC1C,gCAAsC;AAGtC,MAAsB,mBAAmB;IASvC,YAAY,OAAuB;QANzB,eAAU,GAAyB,EAAE,CAAC;QAEtC,iBAAY,GAAsB,EAAE,CAAC;QAK7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAEM,aAAa,CAClB,UAIG;QAEH,MAAM,aAAa,GAAG,IAAA,2BAAe,EAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAyB,CAAC;QAC7G,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAE9G,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,CAAC,UAAU,GAAG,aAAa,CAAC;IAClC,CAAC;IAES,eAAe,CAAC,SAA6B;QACrD,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAE1B,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;QAChC,MAAM,IAAI,GAAG,IAAA,kBAAY,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI;YAAE,OAAO,aAAK,CAAC,IAAI,CAAC,iBAAiB,IAAI,OAAO,QAAQ,qBAAqB,CAAC,CAAC;QAExF,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,QAAa,CAAC;IACzC,CAAC;IAES,gBAAgB,CAAC,UAAgC;QACzD,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;IACrE,CAAC;IAES,eAAe,CAAC,SAA6B;QACrD,MAAM,EAAE,GAAG,EAAE,GAAG,SAAS,CAAC;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAES,gBAAgB,CAAC,UAAgC;QACzD,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;IACrE,CAAC;IAES,gBAAgB,CAAC,GAAW;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,OAAO,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAES,iBAAiB,CAAC,UAAgC;QAC1D,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9D,CAAC;IAEM,OAAO;QACZ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7E,gCAAgC;QAChC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IACzB,CAAC;CACF;AA5ED,kDA4EC;AAED;;;;GAIG;AACH,MAAa,aAAa;IASxB,YAAY,OAAuB,EAAE,OAAmB;QAJ9C,WAAM,GAAoE,EAAE,CAAC;QAEhF,cAAS,GAAG,KAAK,CAAC;QAGvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAsB,CAAC;IACxC,CAAC;IAEM,MAAM,CAAC,OAAmB;QAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAEM,OAAO;QACZ,gCAAgC;QAChC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,gCAAgC;QAChC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAElB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;CACF;AA1BD,sCA0BC"}