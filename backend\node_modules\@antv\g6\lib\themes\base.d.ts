import type { PaletteOptions } from '../spec/element/palette';
import type { Theme } from './types';
type ThemeTokens = {
    bgColor: string;
    textColor: string;
    nodeColor: string;
    nodeColorDisabled: string;
    nodeStroke: string;
    nodeBadgePalette?: string[];
    nodePaletteOptions?: PaletteOptions;
    nodeHaloStrokeOpacityActive?: number;
    nodeHaloStrokeOpacitySelected?: number;
    nodeOpacityDisabled?: number;
    nodeOpacityInactive?: number;
    nodeIconOpacityInactive?: number;
    donutPaletteOptions?: PaletteOptions;
    edgeColor: string;
    edgeColorDisabled: string;
    edgeColorInactive: string;
    edgePaletteOptions?: PaletteOptions;
    comboColor: string;
    comboColorDisabled: string;
    comboStroke: string;
    comboStrokeDisabled: string;
};
/**
 * <zh/> 创建主题
 *
 * <en/> Create a theme based on the given tokens
 * @param tokens - <zh/> 主题配置项 <en/> Theme tokens
 * @returns <zh/> 主题 <en/> Theme
 */
export declare function create(tokens: ThemeTokens): Theme;
export {};
