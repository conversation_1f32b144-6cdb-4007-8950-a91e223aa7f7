import type { DisplayObject } from '@antv/g';
/**
 * <zh/> 缓存图形样式
 *
 * <en/> Cache shape style
 * @param element - <zh/> 图形元素 | <en/> shape element
 * @param name - <zh/> 样式名 | <en/> style name
 */
export declare function cacheStyle(element: DisplayObject, name: string | string[]): void;
/**
 * <zh/> 获取缓存的样式
 *
 * <en/> Get cached style
 * @param element - <zh/> 图形元素 | <en/> shape element
 * @param name - <zh/> 样式名 | <en/> style name
 * @returns <zh/> 样式值 | <en/> style value
 */
export declare function getCachedStyle(element: DisplayObject, name: string): any;
/**
 * <zh/> 是否有缓存的样式
 *
 * <en/> Whether there is a cached style
 * @param element - <zh/> 图形元素 | <en/> shape element
 * @param name - <zh/> 样式名 | <en/> style name
 * @returns <zh/> 是否有缓存的样式 | <en/> Whether there is a cached style
 */
export declare function hasCachedStyle(element: DisplayObject, name: string): boolean;
/**
 * <zh/> 设置缓存的样式
 *
 * <en/> Set cached style
 * @param element - <zh/> 图形元素 | <en/> shape element
 * @param name - <zh/> 样式名 | <en/> style name
 * @param value - <zh/> 样式值 | <en/> style value
 */
export declare function setCacheStyle(element: DisplayObject, name: string, value: any): void;
