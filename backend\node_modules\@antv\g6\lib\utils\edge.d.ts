import type { AABB, DisplayObject } from '@antv/g';
import type { PathArray } from '@antv/util';
import type { EdgeData } from '../spec';
import type { EdgeBadgeStyleProps, EdgeKey, EdgeLabelStyleProps, ID, LoopPlacement, Node, NodeLikeData, Point, Port, Size } from '../types';
/**
 * <zh/> 获取标签的位置样式
 *
 * <en/> Get the style of the label's position
 * @param key - <zh/> 边对象 | <en/> The edge object
 * @param placement - <zh/> 标签位置 | <en/> Position of the label
 * @param autoRotate - <zh/> 是否自动旋转 | <en/> Whether to auto-rotate
 * @param offsetX - <zh/> 标签相对于边的水平偏移量 | <en/> Horizontal offset of the label relative to the edge
 * @param offsetY - <zh/> 标签相对于边的垂直偏移量 | <en/> Vertical offset of the label relative to the edge
 * @returns <zh/> 标签的位置样式 | <en/> Returns the style of the label's position
 */
export declare function getLabelPositionStyle(key: EdgeKey, placement: EdgeLabelStyleProps['placement'], autoRotate: boolean, offsetX: number, offsetY: number): Partial<EdgeLabelStyleProps>;
/**
 * <zh/> 获取边上徽标的位置样式
 *
 * <en/> Get the position style of the badge on the edge
 * @param shapeMap - <zh/> 边上的图形映射 | <en/> Shape map on the edge
 * @param placement - <zh/> 徽标位置 | <en/> Badge position
 * @param labelPlacement - <zh/> 标签位置 | <en/> Label position
 * @param offsetX - <zh/> 水平偏移量 | <en/> Horizontal offset
 * @param offsetY - <zh/> 垂直偏移量 | <en/> Vertical offset
 * @returns <zh/> 徽标的位置样式 | <en/> Position style of the badge
 */
export declare function getBadgePositionStyle(shapeMap: Record<string, DisplayObject<any, any>>, placement: EdgeBadgeStyleProps['placement'], labelPlacement: EdgeLabelStyleProps['placement'], offsetX: number, offsetY: number): Partial<EdgeLabelStyleProps>;
/** ==================== Curve Edge =========================== */
/**
 * <zh/> 计算曲线的控制点
 *
 * <en/> Calculate the control point of the curve
 * @param sourcePoint - <zh/> 起点 | <en/> Source point
 * @param targetPoint - <zh/> 终点 | <en/> Target point
 * @param curvePosition - <zh/> 控制点在连线上的相对位置（取值范围为 0-1） | <en/> The relative position of the control point on the line (value range from 0 to 1)
 * @param curveOffset - <zh/> 控制点距离两端点连线的距离 | <en/> The distance between the control point and the line
 * @returns <zh/> 控制点 | <en/> Control points
 */
export declare function getCurveControlPoint(sourcePoint: Point, targetPoint: Point, curvePosition: number, curveOffset: number): Point;
/**
 * <zh/> 解析控制点距离两端点连线的距离 `curveOffset`
 *
 * <en/> parse the distance of the control point from the line `curveOffset`
 * @param curveOffset - <zh/> curveOffset | <en/> curveOffset
 * @returns <zh/> 标准 curveOffset | <en/> standard curveOffset
 */
export declare function parseCurveOffset(curveOffset: number | [number, number]): [number, number];
/**
 * <zh/> 解析控制点在两端点连线上的相对位置 `curvePosition`，范围为`0-1`
 *
 * <en/> parse the relative position of the control point on the line `curvePosition`
 * @param curvePosition - <zh/> curvePosition | <en/> curvePosition
 * @returns <zh/> 标准 curvePosition | <en/> standard curvePosition
 */
export declare function parseCurvePosition(curvePosition: number | [number, number]): [number, number];
/**
 * <zh/> 获取二次贝塞尔曲线绘制路径
 *
 * <en/> Calculate the path for drawing a quadratic Bessel curve
 * @param sourcePoint - <zh/> 边的起点 | <en/> Source point
 * @param targetPoint - <zh/> 边的终点 | <en/> Target point
 * @param controlPoint - <zh/> 控制点 | <en/> Control point
 * @returns <zh/> 返回绘制曲线的路径 | <en/> Returns curve path
 */
export declare function getQuadraticPath(sourcePoint: Point, targetPoint: Point, controlPoint: Point): PathArray;
/**
 * <zh/> 获取三次贝塞尔曲线绘制路径
 *
 * <en/> Calculate the path for drawing a cubic Bessel curve
 * @param sourcePoint - <zh/> 边的起点 | <en/> Source point
 * @param targetPoint - <zh/> 边的终点 | <en/> Target point
 * @param controlPoints - <zh/> 控制点 | <en/> Control point
 * @returns <zh/> 返回绘制曲线的路径 | <en/> Returns curve path
 */
export declare function getCubicPath(sourcePoint: Point, targetPoint: Point, controlPoints: [Point, Point]): PathArray;
/** ==================== Polyline Edge =========================== */
/**
 * <zh/> 获取折线的绘制路径
 *
 * <en/> Calculates the path for drawing a polyline
 * @param points - <zh/> 折线的顶点 | <en/> The vertices of the polyline
 * @param radius - <zh/> 圆角半径 | <en/> Radius of the rounded corner
 * @param z - <zh/> 路径是否闭合 | <en/> Whether the path is closed
 * @returns <zh/> 返回绘制折线的路径 | <en/> Returns the path for drawing a polyline
 */
export declare function getPolylinePath(points: Point[], radius?: number, z?: boolean): PathArray;
/**
 * <zh/> 根据给定的半径计算出不共线的三点生成贝塞尔曲线的控制点，以模拟接近圆弧
 *
 * <en/> Calculates the control points of the Bezier curve generated by three non-collinear points according to the given radius to simulate an arc
 * @param prevPoint - <zh/> 前一个点 | <en/> Previous point
 * @param midPoint - <zh/> 中间点 | <en/> Middle point
 * @param nextPoint - <zh/> 后一个点 | <en/> Next point
 * @param radius - <zh/> 圆角半径 | <en/> Radius of the rounded corner
 * @returns <zh/> 返回控制点 | <en/> Returns control points
 */
export declare function getBorderRadiusPoints(prevPoint: Point, midPoint: Point, nextPoint: Point, radius: number): [Point, Point];
/** ==================== Loop Edge =========================== */
export declare const getRadians: (bbox: AABB) => Record<LoopPlacement, [number, number]>;
/**
 * <zh/> 获取环形边的起点和终点
 *
 * <en/> Get the start and end points of the loop edge
 * @param node - <zh/> 节点实例 | <en/> Node instance
 * @param placement - <zh/> 环形边相对于节点位置 | <en/> Loop position relative to the node
 * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to draw the loop clockwise
 * @param sourcePort - <zh/> 起点连接桩 | <en/> Source port
 * @param targetPort - <zh/> 终点连接桩 | <en/> Target port
 * @returns <zh/> 起点和终点 | <en/> Start and end points
 */
export declare function getLoopEndpoints(node: Node, placement: LoopPlacement, clockwise: boolean, sourcePort?: Port, targetPort?: Port): [Point, Point];
/**
 * <zh/> 获取环形边的绘制路径
 *
 * <en/> Get the path of the loop edge
 * @param node - <zh/> 节点实例 | <en/> Node instance
 * @param placement - <zh/> 环形边相对于节点位置 | <en/> Loop position relative to the node
 * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to draw the loop clockwise
 * @param dist - <zh/> 从节点 keyShape 边缘到自环顶部的距离 | <en/> The distance from the edge of the node keyShape to the top of the self-loop
 * @param sourcePortKey - <zh/> 起点连接桩 key | <en/> Source port key
 * @param targetPortKey - <zh/> 终点连接桩 key | <en/> Target port key
 * @returns <zh/> 返回绘制环形边的路径 | <en/> Returns the path of the loop edge
 */
export declare function getCubicLoopPath(node: Node, placement: LoopPlacement, clockwise: boolean, dist: number, sourcePortKey?: string, targetPortKey?: string): PathArray;
/**
 * <zh/> 获取环形边的控制点
 *
 * <en/> Get the control points of the loop edge
 * @param node - <zh/> 节点实例 | <en/> Node instance
 * @param sourcePoint - <zh/> 起点 | <en/> Source point
 * @param targetPoint - <zh/> 终点 | <en/> Target point
 * @param dist - <zh/> 从节点 keyShape 边缘到自环顶部的距离 | <en/> The distance from the edge of the node keyShape to the top of the self-loop
 * @returns <zh/> 控制点 | <en/> Control points
 */
export declare function getCubicLoopControlPoints(node: Node, sourcePoint: Point, targetPoint: Point, dist: number): [Point, Point];
/**
 * <zh/> 获取环形折线边的绘制路径
 *
 * <en/> Get the path of the loop polyline edge
 * @param node - <zh/> 节点实例 | <en/> Node instance
 * @param radius - <zh/> 圆角半径 | <en/> Radius of the rounded corner
 * @param placement - <zh/> 环形边相对于节点位置 | <en/> Loop position relative to the node
 * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to draw the loop clockwise
 * @param dist - <zh/> 从节点 keyShape 边缘到自环顶部的距离 | <en/> The distance from the edge of the node keyShape to the top of the self-loop
 * @param sourcePortKey - <zh/> 起点连接桩 key | <en/> Source port key
 * @param targetPortKey - <zh/> 终点连接桩 key | <en/> Target port key
 * @returns <zh/> 返回绘制环形折线边的路径 | <en/> Returns the path of the loop polyline edge
 */
export declare function getPolylineLoopPath(node: Node, radius: number, placement: LoopPlacement, clockwise: boolean, dist: number, sourcePortKey?: string, targetPortKey?: string): PathArray;
/**
 * <zh/> 获取环形折线边的控制点
 *
 * <en/> Get the control points of the loop polyline edge
 * @param node - <zh/> 节点实例 | <en/> Node instance
 * @param sourcePoint - <zh/> 起点 | <en/> Source point
 * @param targetPoint - <zh/> 终点 | <en/> Target point
 * @param dist - <zh/> 从节点 keyShape 边缘到自环顶部的距离 | <en/> The distance from the edge of the node keyShape to the top of the self-loop
 * @returns <zh/> 控制点 | <en/> Control points
 */
export declare function getPolylineLoopControlPoints(node: Node, sourcePoint: Point, targetPoint: Point, dist: number): Point[];
/**
 * <zh/> 获取子图内的所有边，并按照内部边和外部边分组
 *
 * <en/> Get all the edges in the subgraph and group them into internal and external edges
 * @param ids - <zh/> 节点 ID 数组 | <en/> Node ID array
 * @param getRelatedEdges - <zh/> 获取节点邻边 | <en/> Get node edges
 * @returns <zh/> 子图边 | <en/> Subgraph edges
 */
export declare function getSubgraphRelatedEdges(ids: ID[], getRelatedEdges: (id: ID) => EdgeData[]): {
    edges: EdgeData[];
    internal: EdgeData[];
    external: EdgeData[];
};
/**
 * <zh/> 获取边的实际连接节点
 *
 * <en/> Get the actual connected object of the edge
 * @param node - <zh/> 逻辑连接节点数据 | <en/> Logical connection node data
 * @param getParentData - <zh/> 获取父节点数据 | <en/> Get parent node data
 * @returns <zh/> 实际连接节点数据 | <en/> Actual connected node data
 */
export declare function findActualConnectNodeData(node: NodeLikeData, getParentData: (id: ID) => NodeLikeData | undefined): NodeLikeData;
/**
 * <zh/> 获取箭头大小，若用户未指定，则根据线宽自动计算
 *
 * <en/> Get the size of the arrow
 * @param lineWidth - <zh/> 箭头所在边的线宽 | <en/> The line width of the edge where the arrow is located
 * @param size - <zh/> 自定义箭头大小 | <en/> Custom arrow size
 * @returns <zh/> 箭头大小 | <en/> Arrow size
 */
export declare function getArrowSize(lineWidth: number, size?: Size): Size;
