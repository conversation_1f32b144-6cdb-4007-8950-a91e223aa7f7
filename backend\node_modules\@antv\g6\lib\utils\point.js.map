{"version": 3, "file": "point.js", "sourceRoot": "", "sources": ["../../src/utils/point.ts"], "names": [], "mappings": ";;AAeA,gCAEC;AASD,sCAEC;AAQD,0BAEC;AASD,kCAQC;AAUD,sBAEC;AAYD,wBAMC;AAUD,oCAEC;AAUD,gCAEC;AAUD,oCAEC;AAWD,kCAEC;AAUD,8CAEC;AAaD,4DA6BC;AAYD,4CAgBC;AAWD,sDASC;AAYD,4DAaC;AAQD,8CAaC;AAUD,0CAcC;AAUD,8CAGC;AAUD,wDAyBC;AASD,4BAGC;AAUD,0CAOC;AAQD,8CAEC;AAxYD,qCAAqC;AAErC,iCAAqD;AAErD,iCAA+D;AAC/D,yCAA8C;AAC9C,qCAAiF;AAEjF;;;;;GAKG;AACH,SAAgB,UAAU,CAAC,KAAkB;;IAC3C,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,MAAA,KAAK,CAAC,CAAC,mCAAI,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,aAAa,CAAC,KAAY;;IACxC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAA,KAAK,CAAC,CAAC,CAAC,mCAAI,CAAC,EAAE,CAAC;AACxD,CAAC;AAED;;;;;GAKG;AACH,SAAgB,OAAO,CAAC,MAAe;IACrC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,MAAe;IACzC,MAAM,GAAG,GAAG,IAAI,GAAG,EAAU,CAAC;IAC9B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;QACzB,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;YAAE,OAAO,KAAK,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,KAAK,CAAC,KAAY,EAAE,MAAM,GAAG,CAAC;IAC5C,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAU,CAAC;AAClE,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,MAAM,CAAC,CAAQ,EAAE,GAAU,EAAE,QAAgB,EAAE,OAAO,GAAG,KAAK;IAC5E,IAAI,IAAA,cAAO,EAAC,CAAC,EAAE,GAAG,CAAC;QAAE,OAAO,CAAC,CAAC;IAC9B,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,IAAA,iBAAQ,EAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAChE,MAAM,mBAAmB,GAAG,IAAA,kBAAS,EAAC,SAAS,CAAC,CAAC;IACjD,MAAM,UAAU,GAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,mBAAmB,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;IACjG,OAAO,IAAA,YAAG,EAAC,IAAA,kBAAS,EAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AACvC,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,YAAY,CAAC,EAAS,EAAE,EAAS;IAC/C,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,UAAU,CAAC,EAAS,EAAE,EAAS;IAC7C,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,YAAY,CAAC,EAAS,EAAE,EAAS;IAC/C,OAAO,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpD,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,WAAW,CAAC,EAAS,EAAE,EAAS,EAAE,EAAS;IACzD,OAAO,IAAA,sBAAe,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7C,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,iBAAiB,CAAC,CAAQ,EAAE,MAAa;IACvD,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,wBAAwB,CACtC,CAAQ,EACR,MAAa,EACb,MAAe,EACf,aAAa,GAAG,IAAI,EACpB,eAAe,GAAG,KAAK;IAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAE1C,IAAI,aAAa,EAAE,CAAC;YAClB,KAAK,GAAG,IAAA,YAAG,EAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAC3B,GAAG,GAAG,IAAA,YAAG,EAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACzB,CAAC;QAED,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,SAAS,GAAG,IAAA,2BAAoB,EAAC,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;QACrE,IAAI,SAAS,EAAE,CAAC;YACd,OAAO;gBACL,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;aACnB,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO;QACL,KAAK,EAAE,MAAM;QACb,IAAI,EAAE,SAAS;KAChB,CAAC;AACJ,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,gBAAgB,CAAC,KAAY,EAAE,MAAe,EAAE,KAAc,EAAE,GAAY;IAC1F,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACnB,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACnB,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,IAAI,KAAK,KAAK,SAAS;QAAE,KAAK,GAAG,CAAC,CAAC;IACnC,IAAI,GAAG,KAAK,SAAS;QAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3C,MAAM,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC;IACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;QAC9C,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,SAAS,GAAG,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;QACnF,IAAI,SAAS;YAAE,MAAM,GAAG,CAAC,MAAM,CAAC;IAClC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,qBAAqB,CAAC,CAAQ,EAAE,IAAU,EAAE,eAAe,GAAG,KAAK;IACjF,MAAM,MAAM,GAAG,IAAA,2BAAgB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAChD,MAAM,OAAO,GAAG;QACd,IAAA,2BAAgB,EAAC,IAAI,EAAE,UAAU,CAAC;QAClC,IAAA,2BAAgB,EAAC,IAAI,EAAE,WAAW,CAAC;QACnC,IAAA,2BAAgB,EAAC,IAAI,EAAE,cAAc,CAAC;QACtC,IAAA,2BAAgB,EAAC,IAAI,EAAE,aAAa,CAAC;KACtC,CAAC;IACF,OAAO,wBAAwB,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC,KAAK,CAAC;AACpF,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,wBAAwB,CAAC,CAAQ,EAAE,IAAU,EAAE,eAAe,GAAG,KAAK;IACpF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,MAAM,GAAG,GAAG,IAAA,iBAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,IAAI,KAAK,CAAC,KAAK,CAAC;QAAE,OAAO,MAAM,CAAC;IAEhC,MAAM,EAAE,GAAG,IAAA,mBAAY,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClC,MAAM,EAAE,GAAG,IAAA,oBAAa,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnC,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAEpD,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAClC,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,MAAe,EAAE,MAAe;IAChE,IAAI,WAAW,GAAG,QAAQ,CAAC;IAC3B,IAAI,aAAa,GAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;QACpB,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YACpB,MAAM,IAAI,GAAG,IAAA,iBAAQ,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9B,IAAI,IAAI,GAAG,WAAW,EAAE,CAAC;gBACvB,WAAW,GAAG,IAAI,CAAC;gBACnB,aAAa,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,aAAa,CAAC;AACvB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,eAAe,CAAC,KAAY,EAAE,KAAoB;IAChE,IAAI,WAAW,GAAG,QAAQ,CAAC;IAC3B,IAAI,WAAW,GAAmB;QAChC,CAAC,CAAC,EAAE,CAAC,CAAC;QACN,CAAC,CAAC,EAAE,CAAC,CAAC;KACP,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QACrB,MAAM,QAAQ,GAAG,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAChD,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;YAC3B,WAAW,GAAG,QAAQ,CAAC;YACvB,WAAW,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,iBAAiB,CAAC,KAAY,EAAE,IAAiB;IAC/D,MAAM,YAAY,GAAG,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACzD,OAAO,IAAA,iBAAQ,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACvC,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,sBAAsB,CAAC,KAAY,EAAE,IAAiB;IACpE,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;IAEvB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IAEnB,uDAAuD;IACvD,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAClB,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEhE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACV,CAAC,GAAG,CAAC,CAAC;IACR,CAAC;SAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACjB,CAAC,GAAG,CAAC,CAAC;IACR,CAAC;IAED,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IACtB,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;IAEtB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,MAAe;IACtC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,IAAA,YAAG,EAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrE,OAAO,IAAA,eAAM,EAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,eAAe,CAAC,MAAe,EAAE,SAAS,GAAG,IAAI;IAC/D,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IAChC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,OAAO,SAAS,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;IACvD,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,KAAY,EAAE,GAAU;IACxD,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC"}