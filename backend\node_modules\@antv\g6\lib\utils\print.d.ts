/**
 * <zh/> 格式化打印
 *
 * <en/> Format print
 * @param message - <zh/> 消息 | <en/> Message
 * @returns <zh/> 格式化后的消息 | <en/> Formatted message
 */
export declare function format(message: string): string;
export declare const print: {
    mute: boolean;
    debug: (message: string) => void;
    info: (message: string) => void;
    warn: (message: string) => void;
    error: (message: string) => void;
};
