import type { DisplayObject } from '@antv/g';
/**
 * <zh/> 获取图形的所有子元素
 *
 * <en/> Get all the child elements of the shape
 * @param shape - <zh/> 图形元素 | <en/> shape
 * @returns <zh/> 子元素数组 | <en/> child elements array
 */
export declare function getDescendantShapes<T extends DisplayObject>(shape: T): DisplayObject<any, any>[];
/**
 * <zh/> 获取图形的所有祖先元素
 *
 * <en/> Get all the ancestor elements of the shape
 * @param shape - <zh/> 图形元素 | <en/> shape
 * @returns <zh/> 祖先元素数组 | <en/> ancestor elements array
 */
export declare function getAncestorShapes<T extends DisplayObject>(shape: T): DisplayObject<any, any>[];
