{"version": 3, "file": "transform.js", "sourceRoot": "", "sources": ["../../src/utils/transform.ts"], "names": [], "mappings": ";;AAaA,kEAwCC;AApDD,qCAAsC;AAEtC;;;;;;;;;GASG;AACH,SAAgB,2BAA2B,CACzC,CAAS,EACT,CAAS,EACT,CAAU,EACV,YAAqC,EAAE;IAEvC,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC;IAE7D,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,IAAI,cAAc,GAAG,CAAC,CAAC,CAAC;QACxB,MAAM,YAAY,GAAmB,EAAE,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE,CAAC;gBACzB,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBAAE,OAAO,IAAI,CAAC;gBAC1C,cAAc,GAAG,CAAC,CAAC;gBACnB,YAAY,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC;iBAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,aAAa,EAAE,CAAC;gBAClC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBAAE,OAAO,IAAI,CAAC;gBACxD,cAAc,GAAG,CAAC,CAAC;gBACnB,YAAY,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,aAAD,CAAC,cAAD,CAAC,GAAI,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QAED,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE,CAAC;YAC1B,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAA,eAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,aAAD,CAAC,cAAD,CAAC,GAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/F,CAAC;QACD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAC3C,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,MAAM,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5F,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACZ,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,gBAAgB,EAAE,CAAC;IACpD,CAAC;SAAM,CAAC;QACN,OAAO,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,gBAAgB,EAAE,CAAC;IAC5D,CAAC;AACH,CAAC"}