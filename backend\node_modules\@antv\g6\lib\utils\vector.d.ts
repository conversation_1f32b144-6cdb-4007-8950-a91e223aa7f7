import type { Vector2, Vector3 } from '../types';
/**
 * <zh/> 两个向量求和
 *
 * <en/> Adds two vectors
 * @param a - <zh/> 第一个向量 | <en/> The first vector
 * @param b - <zh/> 第二个向量 | <en/> The second vector
 * @returns <zh/> 两个向量的和 | <en/> The sum of the two vectors
 */
export declare function add(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector2 | Vector3;
/**
 * <zh/> 两个向量求差
 *
 * <en/> Subtracts two vectors
 * @param a - <zh/> 第一个向量 | <en/> The first vector
 * @param b - <zh/> 第二个向量 | <en/> The second vector
 * @returns <zh/> 两个向量的差 | <en/> The difference of the two vectors
 */
export declare function subtract(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector2 | Vector3;
/**
 * <zh/> 两个向量求积或者向量和标量求积
 *
 * <en/> Multiplies two vectors or a vector and a scalar
 * @param a - <zh/> 向量 | <en/> The vector
 * @param b - <zh/> 向量或者标量 | <en/> The vector or scalar
 * @returns <zh/> 两个向量的积或者向量和标量的积 | <en/> The product of the two vectors or the product of the vector and scalar
 */
export declare function multiply(a: Vector2 | Vector3, b: number | Vector2 | Vector3): Vector2 | Vector3;
/**
 * <zh/> 两个向量求商或者向量和标量求商
 *
 * <en/> Divides two vectors or a vector and a scalar
 * @param a - <zh/> 向量 | <en/> The vector
 * @param b - <zh/> 向量或者标量 | <en/> The vector or scalar
 * @returns <zh/> 两个向量的商或者向量和标量的商 | <en/> The quotient of the two vectors or the quotient of the vector and scalar
 */
export declare function divide(a: Vector2 | Vector3, b: number | Vector2 | Vector3): Vector2 | Vector3;
/**
 * <zh/> 两个向量求点积
 *
 * <en/> Calculates the dot product of two vectors
 * @param a - <zh/> 第一个向量 | <en/> The first vector
 * @param b - <zh/> 第二个向量 | <en/> The second vector
 * @returns <zh/> 两个向量的点积 | <en/> The dot product of the two vectors
 */
export declare function dot(a: Vector2 | Vector3, b: Vector2 | Vector3): number;
/**
 * <zh/> 两个二维向量求叉积
 *
 * <en/> Calculates the cross product of two vectors in three-dimensional Euclidean space
 * @param a - <zh/> 第一个向量 | <en/> The first vector
 * @param b - <zh/> 第二个向量 | <en/> The second vector
 * @returns <zh/> 两个向量的叉积 | <en/> The cross product of the two vectors
 */
export declare function cross(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector3;
/**
 * <zh/> 向量缩放
 *
 * <en/> Scales a vector by a scalar number
 * @param a  - <zh/> 向量 | <en/> The vector to scale
 * @param s - <zh/> 缩放系数 | <en/> Scale factor
 * @returns <zh/> 缩放后的向量 | <en/> The scaled vector
 */
export declare function scale(a: Vector2 | Vector3, s: number): Vector2 | Vector3;
/**
 * <zh/> 计算两个向量间的欧几里得距离
 *
 * <en/> Calculates the Euclidean distance between two vectors
 * @param a - <zh/> 第一个向量 | <en/> The first vector
 * @param b - <zh/> 第二个向量 | <en/> The second vector
 * @returns <zh/> 两个向量间的距离 | <en/> The distance between the two vectors
 */
export declare function distance(a: Vector2 | Vector3, b: Vector2 | Vector3): number;
/**
 * <zh/> 计算两个向量间的曼哈顿距离
 *
 * <en/> Calculates the Manhattan distance between two vectors
 * @param a - <zh/> 第一个向量 | <en/> The first vector
 * @param b - <zh/> 第二个向量 | <en/> The second vector
 * @returns <zh/> 两个向量间的距离 | <en/> The distance between the two vectors
 */
export declare function manhattanDistance(a: Vector2 | Vector3, b: Vector2 | Vector3): number;
/**
 * <zh/> 标准化向量（使长度为 1）
 *
 * <en/> Normalizes a vector (making its length 1)
 * @param a - <zh/> 要标准化的向量 | <en/> The vector to normalize
 * @returns <zh/> 标准化后的向量 | <en/> The normalized vector
 */
export declare function normalize(a: Vector2 | Vector3): Vector2 | Vector3;
/**
 * <zh/> 计算两个向量间的夹角，输出为锐角余弦值
 *
 * <en/> Get the angle between two vectors
 * @param a - <zh/> 第一个向量 | <en/> The first vector
 * @param b - <zh/> 第二个向量 | <en/> The second vector
 * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to calculate the angle in a clockwise direction
 * @returns  <zh/> 弧度值 | <en/> The angle in radians
 */
export declare function angle(a: Vector2 | Vector3, b: Vector2 | Vector3, clockwise?: boolean): number;
/**
 * <zh/> 判断两个向量是否完全相等（使用 === 比较）
 *
 * <en/> Returns whether or not the vectors exactly have the same elements in the same position (when compared with ===)
 * @param a - <zh/> 第一个向量 | <en/> The first vector
 * @param b - <zh/> 第二个向量 | <en/> The second vector
 * @returns  - <zh/> 是否相等 | <en/> Whether or not the vectors are equal
 */
export declare function exactEquals(a: Vector2 | Vector3, b: Vector2 | Vector3): boolean;
/**
 * <zh/> 计算向量的垂直向量
 *
 * <en/> Calculates the perpendicular vector to a given vector
 * @param a - <zh/> 原始向量 | <en/> The original vector
 * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to calculate the perpendicular vector in a clockwise direction
 * @returns <zh/> 原始向量的垂直向量 | <en/> The perpendicular vector to the original vector
 */
export declare function perpendicular(a: Vector2, clockwise?: boolean): Vector2;
/**
 * <zh/> 计算向量的模
 *
 * <en/> Calculates the modulus of a vector
 * @param a - <zh/> 原始向量 | <en/> The original vector
 * @param b - <zh/> 模 | <en/> The modulus
 * @returns - <zh/> 向量的模 | <en/> The modulus of the vector
 */
export declare function mod(a: Vector2 | Vector3, b: number): Vector2 | Vector3;
/**
 * <zh/> 向量强制转换为二维向量
 *
 * <en/> Force vector to be two-dimensional
 * @param a - <zh/> 原始向量 | <en/> The original vector
 * @returns <zh/> 二维向量 | <en/> Two-dimensional vector
 */
export declare function toVector2(a: Vector2 | Vector3): Vector2;
/**
 * <zh/> 向量强制转换为三维向量
 *
 * <en/> Force vector to be three-dimensional
 * @param a - <zh/> 原始向量 | <en/> The original vector
 * @returns  - <zh/> 三维向量 | <en/> Three-dimensional vector
 */
export declare function toVector3(a: Vector2 | Vector3): Vector3;
/**
 * <zh/> 计算向量与 x 轴正方向的夹角（弧度制）
 *
 * <en/> The angle between the vector and the positive direction of the x-axis (radians)
 * @param a - <zh/> 向量 | <en/> The vector
 * @returns <zh/> 弧度值 | <en/> The angle in radians
 */
export declare function rad(a: Vector2 | Vector3): number;
