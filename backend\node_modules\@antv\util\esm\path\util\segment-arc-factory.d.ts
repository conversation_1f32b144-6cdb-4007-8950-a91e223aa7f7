import type { LengthFactory, PathLengthFactoryOptions } from '../types';
/**
 * Returns a {x,y} point at a given length, the total length and
 * the shape minimum and maximum {x,y} coordinates of an A (arc-to) segment.
 *
 * For better performance, it can skip calculate bbox or length in some scenario.
 */
export declare function segmentArcFactory(X1: number, Y1: number, RX: number, RY: number, angle: number, LAF: number, SF: number, X2: number, Y2: number, distance: number, options: Partial<PathLengthFactoryOptions>): LengthFactory;
