import type { LengthFactory, PathLengthFactoryOptions } from '../types';
/**
 * Returns a {x,y} point at a given length, the total length and
 * the minimum and maximum {x,y} coordinates of a Q (quadratic-bezier) segment.
 */
export declare function segmentQuadFactory(x1: number, y1: number, qx: number, qy: number, x2: number, y2: number, distance: number, options: Partial<PathLengthFactoryOptions>): LengthFactory;
