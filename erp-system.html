<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白墨打印统计系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #001529;
            color: white;
            padding: 0;
        }

        .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #002140;
        }

        .logo h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .menu {
            list-style: none;
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: #1890ff;
        }

        .menu-item.active {
            background: #1890ff;
            border-left-color: #40a9ff;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .page.active {
            display: block;
        }

        .page-header {
            margin-bottom: 24px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 16px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .page-description {
            color: #666;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 14px;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .stat-card .value {
            font-size: 28px;
            font-weight: 600;
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 16px;
            background: #fafafa;
            border-radius: 6px;
        }

        .search-box {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #40a9ff;
        }

        .btn-danger {
            background: #ff4d4f;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 600;
        }

        .table tr:hover {
            background: #f5f5f5;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }

        .form-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        .status-normal {
            color: #52c41a;
            background: #f6ffed;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .status-low {
            color: #faad14;
            background: #fffbe6;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="logo">
                <h1>白墨打印统计系统</h1>
            </div>
            <ul class="menu">
                <li class="menu-item active" onclick="showPage('dashboard')">📊 仪表板</li>
                <li class="menu-item" onclick="showPage('orders')">📋 订单管理</li>
                <li class="menu-item" onclick="showPage('customers')">🤝 客户管理</li>
                <li class="menu-item" onclick="showPage('patterns')">🎨 图案数据库</li>
                <li class="menu-item" onclick="showPage('finance')">💰 财务管理</li>
                <li class="menu-item" onclick="showPage('materials')">📦 耗材管理</li>
                <li class="menu-item" onclick="showPage('settings')">⚙️ 基础设置</li>
            </ul>
        </div>

        <div class="main-content">
            <!-- 仪表板页面 -->
            <div id="dashboard" class="page active">
                <div class="page-header">
                    <h2 class="page-title">仪表板</h2>
                    <p class="page-description">打印业务数据概览和快捷操作</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>今日订单</h3>
                        <div class="value" id="todayOrders">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>今日收入</h3>
                        <div class="value" id="todayIncome">¥0</div>
                    </div>
                    <div class="stat-card">
                        <h3>本月订单</h3>
                        <div class="value" id="monthOrders">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>本月收入</h3>
                        <div class="value" id="monthIncome">¥0</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3 style="margin-bottom: 16px;">快捷操作</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button class="btn" onclick="showPage('orders')">新建订单</button>
                            <button class="btn" onclick="showPage('customers')">客户管理</button>
                            <button class="btn" onclick="showPage('patterns')">图案库</button>
                            <button class="btn" onclick="showPage('finance')">财务分析</button>
                        </div>
                    </div>
                    <div>
                        <h3 style="margin-bottom: 16px;">最近订单</h3>
                        <div id="recentOrders" style="font-size: 14px; line-height: 1.6;">
                            <!-- 最近订单将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单管理页面 -->
            <div id="orders" class="page">
                <div class="page-header">
                    <h2 class="page-title">订单管理</h2>
                    <p class="page-description">管理打印订单、计算价格和跟踪订单状态</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索客户姓名..." id="orderSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="colorFilter">
                            <option value="">全部色号</option>
                            <option value="单色">单色</option>
                            <option value="双色">双色</option>
                            <option value="三色">三色</option>
                        </select>

                    </div>
                    <button class="btn" onclick="showOrderModal()">新建订单</button>
                </div>

                <table class="table" id="ordersTable">
                    <thead>
                        <tr>
                            <th>客户姓名</th>
                            <th>图案名称</th>
                            <th>色号</th>
                            <th>计费方式</th>
                            <th>数量</th>
                            <th>单价</th>
                            <th>总价格</th>
                            <th>时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 客户管理页面 -->
            <div id="customers" class="page">
                <div class="page-header">
                    <h2 class="page-title">客户管理</h2>
                    <p class="page-description">管理客户信息、单价设置和联系方式</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索客户姓名..." id="customerSearch">
                    </div>
                    <button class="btn" onclick="showCustomerModal()">添加客户</button>
                </div>

                <table class="table" id="customersTable">
                    <thead>
                        <tr>
                            <th>客户姓名</th>
                            <th>客户单价</th>
                            <th>微信号码</th>
                            <th>微信备注</th>
                            <th>联系时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 图案数据库页面 -->
            <div id="patterns" class="page">
                <div class="page-header">
                    <h2 class="page-title">图案数据库</h2>
                    <p class="page-description">管理打印图案的基础信息和参数</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索图案名称..." id="patternSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="patternColorFilter">
                            <option value="">全部色号</option>
                            <option value="单色">单色</option>
                            <option value="双色">双色</option>
                            <option value="三色">三色</option>
                        </select>
                    </div>
                    <button class="btn" onclick="showPatternModal()">添加图案</button>
                </div>

                <table class="table" id="patternsTable">
                    <thead>
                        <tr>
                            <th>图案名称</th>
                            <th>色号</th>
                            <th>行数量</th>
                            <th>实际高度</th>
                            <th>添加出血</th>
                            <th>每平方数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="patternsTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 财务管理页面 -->
            <div id="finance" class="page">
                <div class="page-header">
                    <h2 class="page-title">财务管理</h2>
                    <p class="page-description">财务分析和客户充值管理</p>
                </div>

                <div style="margin-bottom: 24px;">
                    <h3 style="margin-bottom: 16px;">📊 财务分析</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 20px;">
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>今日收入</h4>
                            <div style="font-size: 24px; color: #52c41a; margin-top: 8px;" id="financeToday">¥0</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>本月收入</h4>
                            <div style="font-size: 24px; color: #1890ff; margin-top: 8px;" id="financeMonth">¥0</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>本年收入</h4>
                            <div style="font-size: 24px; color: #722ed1; margin-top: 8px;" id="financeYear">¥0</div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 style="margin-bottom: 16px;">💰 客户充值</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <p style="color: #666; margin-bottom: 16px;">客户充值功能开发中...</p>
                        <button class="btn" onclick="alert('充值功能开发中')">客户充值</button>
                    </div>
                </div>
            </div>

            <!-- 耗材管理页面 -->
            <div id="materials" class="page">
                <div class="page-header">
                    <h2 class="page-title">耗材管理</h2>
                    <p class="page-description">管理打印耗材库存和采购</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索耗材名称..." id="materialSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="categoryFilter">
                            <option value="">全部分类</option>
                            <option value="打印纸张">打印纸张</option>
                            <option value="墨水耗材">墨水耗材</option>
                            <option value="设备配件">设备配件</option>
                            <option value="清洗用品">清洗用品</option>
                        </select>
                    </div>
                    <button class="btn" onclick="showMaterialModal()">添加耗材</button>
                </div>

                <table class="table" id="materialsTable">
                    <thead>
                        <tr>
                            <th>耗材名称</th>
                            <th>分类</th>
                            <th>单位</th>
                            <th>单价</th>
                            <th>库存</th>
                            <th>状态</th>
                            <th>供应商</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="materialsTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 基础设置页面 -->
            <div id="settings" class="page">
                <div class="page-header">
                    <h2 class="page-title">基础设置</h2>
                    <p class="page-description">系统设置、数据管理和打印参数配置</p>
                </div>

                <!-- 数据管理 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">📁 数据管理</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <p style="margin-bottom: 16px; color: #666;">
                            为了防止数据丢失，建议定期备份数据。您可以导出数据到本地文件，也可以从备份文件恢复数据。
                        </p>
                        <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                            <button class="btn" onclick="exportData()">📤 导出数据</button>
                            <button class="btn" onclick="document.getElementById('importFile').click()">📥 导入数据</button>
                            <button class="btn btn-danger" onclick="clearAllData()">🗑️ 清空数据</button>
                        </div>
                        <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(event)">
                        <div style="margin-top: 12px; font-size: 12px; color: #999;">
                            数据格式：JSON文件 | 包含：订单、客户、图案、耗材等所有数据
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">ℹ️ 系统信息</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div>
                                <strong>系统版本：</strong> v1.0.0<br>
                                <strong>数据存储：</strong> 浏览器本地存储 + 文件备份<br>
                                <strong>最后备份：</strong> <span id="lastBackupTime">未备份</span>
                            </div>
                            <div>
                                <strong>数据统计：</strong><br>
                                <span id="dataStats">正在统计...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 打印参数设置 -->
                <div>
                    <h3 style="margin-bottom: 16px;">⚙️ 打印参数设置</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>📏 基础参数</h4>
                            <p style="color: #666; font-size: 14px;">清洗出品 = 实际高度 + 20mm</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('参数设置功能开发中')">参数设置</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>🎨 色号管理</h4>
                            <p style="color: #666; font-size: 14px;">单色、双色、三色设置</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('色号管理功能开发中')">色号设置</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>📐 计算公式</h4>
                            <p style="color: #666; font-size: 14px;">客户方数量 = 1000 / 清洗出品 / 行数量</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('公式设置功能开发中')">公式设置</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>🖨️ 打印设置</h4>
                            <p style="color: #666; font-size: 14px;">价格计算和打印参数</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('打印设置功能开发中')">打印设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单编辑模态框 -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <h3 id="orderModalTitle">新建订单</h3>
            <form id="orderForm">
                <div class="form-group">
                    <label>客户姓名 *</label>
                    <select id="orderCustomerName" required>
                        <option value="">请选择客户</option>
                        <!-- 客户选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label>选择图案 *</label>
                    <select id="orderPatternName" required onchange="loadPatternData()">
                        <option value="">请选择图案</option>
                        <!-- 图案选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="form-group" style="display: none;">
                    <label>色号 (自动填充)</label>
                    <input type="text" id="orderColorType" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group" style="display: none;">
                    <label>行数量 (自动填充)</label>
                    <input type="number" id="orderRowCount" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group" style="display: none;">
                    <label>实际高度(mm) (自动填充)</label>
                    <input type="number" id="orderActualHeight" step="0.1" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group" style="display: none;">
                    <label>添加出血(mm) (自动填充)</label>
                    <input type="number" id="orderBleedHeight" step="0.1" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group" style="display: none;">
                    <label>每平方数量 (自动计算)</label>
                    <input type="number" id="orderCustomerSquare" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group">
                    <label>计费方式 *</label>
                    <div style="margin-top: 8px;">
                        <label style="display: inline-block; margin-right: 20px;">
                            <input type="radio" name="billingType" value="pieces" onchange="changeBillingType()" checked> 按个数计费
                        </label>
                        <label style="display: inline-block;">
                            <input type="radio" name="billingType" value="square" onchange="changeBillingType()"> 按平方计费
                        </label>
                    </div>
                </div>
                <div class="form-group" id="piecesGroup">
                    <label>数量(个) *</label>
                    <input type="number" id="orderPiecesCount" min="0" value="0" onchange="calculateOrderPrice()">
                    <div style="font-size: 12px; color: #666; margin-top: 4px;">
                        个数单价 = 客户单价 ÷ 每平方数量
                    </div>
                </div>
                <div class="form-group" id="squareGroup" style="display: none;">
                    <label>面积(平方) *</label>
                    <input type="number" id="orderSquareCount" step="0.01" min="0" value="0" onchange="calculateOrderPrice()">
                    <div style="font-size: 12px; color: #666; margin-top: 4px;">
                        平方单价 = 16元/平方
                    </div>
                </div>
                <div class="form-group">
                    <label>单价 (自动计算)</label>
                    <input type="number" id="orderUnitPrice" step="0.01" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group">
                    <label>总价格 (自动计算)</label>
                    <input type="number" id="orderTotalPrice" step="0.01" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeOrderModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 客户管理编辑模态框 -->
    <div id="customerModal" class="modal">
        <div class="modal-content">
            <h3 id="customerModalTitle">添加客户</h3>
            <form id="customerForm">
                <div class="form-group">
                    <label>客户姓名 *</label>
                    <input type="text" id="customerName" required>
                </div>
                <div class="form-group">
                    <label>客户单价 *</label>
                    <input type="number" id="customerUnitPrice" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label>微信号码</label>
                    <input type="text" id="customerWechat">
                </div>
                <div class="form-group">
                    <label>微信备注</label>
                    <input type="text" id="customerWechatNote">
                </div>
                <div class="form-group">
                    <label>联系时间</label>
                    <input type="datetime-local" id="customerContactTime">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeCustomerModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 图案数据库编辑模态框 -->
    <div id="patternModal" class="modal">
        <div class="modal-content">
            <h3 id="patternModalTitle">添加图案</h3>
            <form id="patternForm">
                <div class="form-group">
                    <label>图案名称 *</label>
                    <input type="text" id="patternName" required>
                </div>
                <div class="form-group">
                    <label>色号 *</label>
                    <select id="patternColorType" required>
                        <option value="">请选择色号</option>
                        <option value="单色">单色</option>
                        <option value="双色">双色</option>
                        <option value="三色">三色</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>行数量 *</label>
                    <input type="number" id="patternRowCount" min="1" required onchange="calculatePatternData()">
                </div>
                <div class="form-group">
                    <label>实际高度(mm) *</label>
                    <input type="number" id="patternActualHeight" step="0.1" min="0" required onchange="calculatePatternData()">
                </div>
                <div class="form-group">
                    <label>添加出血(mm) (自动计算)</label>
                    <input type="number" id="patternBleedHeight" step="0.1" readonly style="background: #f5f5f5;">
                    <div style="font-size: 12px; color: #666; margin-top: 4px;">
                        添加出血 = 实际高度 + 20mm
                    </div>
                </div>
                <div class="form-group">
                    <label>每平方数量 (自动计算)</label>
                    <input type="number" id="patternCustomerSquare" readonly style="background: #f5f5f5;">
                    <div style="font-size: 12px; color: #666; margin-top: 4px;">
                        每平方数量 = (1600mm ÷ 添加出血) × 行数量 (取整数)
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closePatternModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 耗材编辑模态框 -->
    <div id="materialModal" class="modal">
        <div class="modal-content">
            <h3 id="materialModalTitle">添加耗材</h3>
            <form id="materialForm">
                <div class="form-group">
                    <label>耗材名称 *</label>
                    <input type="text" id="materialName" required>
                </div>
                <div class="form-group">
                    <label>分类 *</label>
                    <select id="materialCategory" required>
                        <option value="">请选择分类</option>
                        <option value="打印纸张">打印纸张</option>
                        <option value="墨水耗材">墨水耗材</option>
                        <option value="设备配件">设备配件</option>
                        <option value="清洗用品">清洗用品</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>单位 *</label>
                    <input type="text" id="materialUnit" placeholder="如：张、瓶、个等" required>
                </div>
                <div class="form-group">
                    <label>单价 *</label>
                    <input type="number" id="materialPrice" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label>库存数量 *</label>
                    <input type="number" id="materialStock" min="0" required>
                </div>
                <div class="form-group">
                    <label>供应商</label>
                    <input type="text" id="materialSupplier">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeMaterialModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 打印订单数据
        let orders = [
            {
                id: 1,
                customerName: '张三',
                patternName: '标准花纹A',
                colorType: '单色',
                rowCount: 10,
                actualHeight: 50.0,
                bleedHeight: 70.0,
                customerSquare: 228,
                billingType: 'pieces',
                piecesCount: 100,
                squareCount: 0,
                unitPrice: 10.96,
                totalPrice: 1096.0,
                createTime: '2025-01-28 10:30'
            },
            {
                id: 2,
                customerName: '李四',
                patternName: '复杂图案B',
                colorType: '双色',
                rowCount: 8,
                actualHeight: 60.0,
                bleedHeight: 80.0,
                customerSquare: 160,
                billingType: 'square',
                piecesCount: 0,
                squareCount: 3.0,
                unitPrice: 16.0,
                totalPrice: 48.0,
                createTime: '2025-01-28 14:20'
            },
            {
                id: 3,
                customerName: '王五',
                patternName: '精细图案C',
                colorType: '三色',
                rowCount: 12,
                actualHeight: 45.0,
                bleedHeight: 65.0,
                customerSquare: 295,
                billingType: 'pieces',
                piecesCount: 80,
                squareCount: 0,
                unitPrice: 9.49,
                totalPrice: 759.2,
                createTime: '2025-01-28 16:45'
            }
        ];

        // 客户管理数据
        let customers = [
            {
                id: 1,
                name: '张三',
                unitPrice: 2.5,
                wechat: 'zhangsan123',
                wechatNote: '老客户',
                contactTime: '2025-01-28T10:00'
            },
            {
                id: 2,
                name: '李四',
                unitPrice: 3.0,
                wechat: 'lisi456',
                wechatNote: '新客户',
                contactTime: '2025-01-28T14:00'
            },
            {
                id: 3,
                name: '王五',
                unitPrice: 2.8,
                wechat: 'wangwu789',
                wechatNote: '大客户',
                contactTime: '2025-01-28T16:00'
            }
        ];

        // 图案数据库
        let patterns = [
            {
                id: 1,
                name: '标准花纹A',
                colorType: '单色',
                rowCount: 10,
                actualHeight: 50.0,
                bleedHeight: 70.0,
                customerSquare: 228
            },
            {
                id: 2,
                name: '复杂图案B',
                colorType: '双色',
                rowCount: 8,
                actualHeight: 60.0,
                bleedHeight: 80.0,
                customerSquare: 160
            },
            {
                id: 3,
                name: '精细图案C',
                colorType: '三色',
                rowCount: 12,
                actualHeight: 45.0,
                bleedHeight: 65.0,
                customerSquare: 295
            }
        ];

        // 打印耗材数据
        let materials = [
            {
                id: 1,
                name: '白墨打印纸',
                category: '打印纸张',
                unit: '张',
                price: 0.5,
                stock: 5000,
                supplier: '纸张供应商A',
                status: '正常'
            },
            {
                id: 2,
                name: '黑色墨水',
                category: '墨水耗材',
                unit: '瓶',
                price: 120.0,
                stock: 8,
                supplier: '墨水供应商B',
                status: '库存不足'
            },
            {
                id: 3,
                name: '清洗液',
                category: '清洗用品',
                unit: '瓶',
                price: 45.0,
                stock: 30,
                supplier: '清洗用品公司',
                status: '正常'
            }
        ];

        let editingOrderId = null;
        let editingCustomerId = null;
        let editingPatternId = null;
        let editingMaterialId = null;

        // 页面切换
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(pageId).classList.add('active');

            // 更新菜单状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 设置对应菜单项为active
            event.target.classList.add('active');

            // 根据页面类型刷新对应表格
            if (pageId === 'orders') {
                renderOrdersTable();
                loadCustomerOptions();
                loadPatternOptions();
            } else if (pageId === 'customers') {
                renderCustomersTable();
            } else if (pageId === 'patterns') {
                renderPatternsTable();
            } else if (pageId === 'materials') {
                renderMaterialsTable();
            } else if (pageId === 'dashboard') {
                updateDashboard();
            } else if (pageId === 'finance') {
                updateFinanceData();
            }
        }

        // 打印计算公式
        function calculatePrintData(rowCount, actualHeight) {
            const cleanHeight = actualHeight + 20; // 清洗出品 = 实际高度 + 20mm
            const customerSquare = 1000 / cleanHeight / rowCount; // 客户方数量 = 1000 / 清洗出品 / 行数量
            return {
                cleanHeight: cleanHeight,
                customerSquare: customerSquare
            };
        }

        // 切换计费方式
        function changeBillingType() {
            const billingType = document.querySelector('input[name="billingType"]:checked').value;
            const piecesGroup = document.getElementById('piecesGroup');
            const squareGroup = document.getElementById('squareGroup');

            if (billingType === 'pieces') {
                piecesGroup.style.display = 'block';
                squareGroup.style.display = 'none';
                document.getElementById('orderSquareCount').value = 0;
            } else {
                piecesGroup.style.display = 'none';
                squareGroup.style.display = 'block';
                document.getElementById('orderPiecesCount').value = 0;
            }

            calculateOrderPrice();
        }

        // 计算订单价格
        function calculateOrderPrice() {
            const customerName = document.getElementById('orderCustomerName').value;
            const patternName = document.getElementById('orderPatternName').value;
            const billingType = document.querySelector('input[name="billingType"]:checked')?.value;
            const piecesCount = parseInt(document.getElementById('orderPiecesCount').value) || 0;
            const squareCount = parseFloat(document.getElementById('orderSquareCount').value) || 0;

            if (!customerName || !patternName || !billingType) {
                document.getElementById('orderUnitPrice').value = '';
                document.getElementById('orderTotalPrice').value = '';
                return;
            }

            const customer = customers.find(c => c.name === customerName);
            const pattern = patterns.find(p => p.name === patternName);

            if (!customer || !pattern) {
                return;
            }

            let unitPrice = 0;
            let totalPrice = 0;

            if (billingType === 'pieces') {
                // 个数单价 = 客户单价 ÷ 每平方数量
                unitPrice = customer.unitPrice / pattern.customerSquare;
                totalPrice = unitPrice * piecesCount;
            } else if (billingType === 'square') {
                // 平方单价 = 16元/平方
                unitPrice = 16;
                totalPrice = unitPrice * squareCount;
            }

            document.getElementById('orderUnitPrice').value = unitPrice.toFixed(2);
            document.getElementById('orderTotalPrice').value = totalPrice.toFixed(2);
        }

        // 计算图案数据
        function calculatePatternData() {
            const rowCount = parseInt(document.getElementById('patternRowCount').value) || 0;
            const actualHeight = parseFloat(document.getElementById('patternActualHeight').value) || 0;

            if (actualHeight) {
                // 添加出血 = 实际高度 + 20mm
                const bleedHeight = actualHeight + 20;
                document.getElementById('patternBleedHeight').value = bleedHeight.toFixed(1);

                if (rowCount) {
                    // 每平方数量 = (1600mm ÷ 添加出血) × 行数量 (取整数，直接舍去小数)
                    const customerSquare = Math.floor((1600 / bleedHeight) * rowCount);
                    document.getElementById('patternCustomerSquare').value = customerSquare;
                } else {
                    document.getElementById('patternCustomerSquare').value = '';
                }
            } else {
                document.getElementById('patternBleedHeight').value = '';
                document.getElementById('patternCustomerSquare').value = '';
            }
        }

        // 加载客户选项
        function loadCustomerOptions() {
            const select = document.getElementById('orderCustomerName');
            select.innerHTML = '<option value="">请选择客户</option>';
            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.name;
                option.textContent = `${customer.name} (¥${customer.unitPrice}/单位)`;
                select.appendChild(option);
            });
        }

        // 加载图案选项
        function loadPatternOptions() {
            const select = document.getElementById('orderPatternName');
            select.innerHTML = '<option value="">请选择图案</option>';
            patterns.forEach(pattern => {
                const option = document.createElement('option');
                option.value = pattern.name;
                option.textContent = `${pattern.name} (${pattern.colorType})`;
                select.appendChild(option);
            });
        }

        // 加载图案数据
        function loadPatternData() {
            const patternName = document.getElementById('orderPatternName').value;
            if (patternName) {
                const pattern = patterns.find(p => p.name === patternName);
                if (pattern) {
                    document.getElementById('orderColorType').value = pattern.colorType;
                    document.getElementById('orderRowCount').value = pattern.rowCount;
                    document.getElementById('orderActualHeight').value = pattern.actualHeight;
                    document.getElementById('orderBleedHeight').value = pattern.bleedHeight;
                    document.getElementById('orderCustomerSquare').value = pattern.customerSquare;

                    // 重新计算价格
                    calculateOrderPrice();
                }
            } else {
                // 清空数据
                document.getElementById('orderColorType').value = '';
                document.getElementById('orderRowCount').value = '';
                document.getElementById('orderActualHeight').value = '';
                document.getElementById('orderBleedHeight').value = '';
                document.getElementById('orderCustomerSquare').value = '';
                document.getElementById('orderTotalPrice').value = '';
            }
        }

        // 更新仪表板数据
        function updateDashboard() {
            const today = new Date().toISOString().split('T')[0];
            const thisMonth = new Date().toISOString().slice(0, 7);

            // 计算今日订单和收入
            const todayOrders = orders.filter(order => order.createTime.startsWith(today));
            const todayIncome = todayOrders.reduce((sum, order) => sum + order.totalPrice, 0);

            // 计算本月订单和收入
            const monthOrders = orders.filter(order => order.createTime.startsWith(thisMonth));
            const monthIncome = monthOrders.reduce((sum, order) => sum + order.totalPrice, 0);

            // 更新仪表板显示
            document.getElementById('todayOrders').textContent = todayOrders.length;
            document.getElementById('todayIncome').textContent = `¥${todayIncome.toFixed(2)}`;
            document.getElementById('monthOrders').textContent = monthOrders.length;
            document.getElementById('monthIncome').textContent = `¥${monthIncome.toFixed(2)}`;

            // 更新最近订单
            const recentOrders = orders.slice(-4).reverse();
            const recentOrdersHtml = recentOrders.map(order => `
                <div style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                    <span style="color: #1890ff;">${order.customerName}</span> ${order.colorType}
                    <span style="color: #999;">${order.createTime.split(' ')[1]}</span>
                </div>
            `).join('');
            document.getElementById('recentOrders').innerHTML = recentOrdersHtml;
        }

        // 更新财务数据
        function updateFinanceData() {
            const today = new Date().toISOString().split('T')[0];
            const thisMonth = new Date().toISOString().slice(0, 7);
            const thisYear = new Date().getFullYear().toString();

            // 计算收入
            const todayIncome = orders.filter(order => order.createTime.startsWith(today))
                                   .reduce((sum, order) => sum + order.totalPrice, 0);
            const monthIncome = orders.filter(order => order.createTime.startsWith(thisMonth))
                                   .reduce((sum, order) => sum + order.totalPrice, 0);
            const yearIncome = orders.filter(order => order.createTime.startsWith(thisYear))
                                  .reduce((sum, order) => sum + order.totalPrice, 0);

            document.getElementById('financeToday').textContent = `¥${todayIncome.toFixed(2)}`;
            document.getElementById('financeMonth').textContent = `¥${monthIncome.toFixed(2)}`;
            document.getElementById('financeYear').textContent = `¥${yearIncome.toFixed(2)}`;
        }

        // 渲染订单表格
        function renderOrdersTable() {
            const tbody = document.getElementById('ordersTableBody');
            const searchTerm = document.getElementById('orderSearch').value.toLowerCase();
            const colorFilter = document.getElementById('colorFilter').value;

            let filteredOrders = orders.filter(order => {
                const matchesSearch = order.customerName.toLowerCase().includes(searchTerm);
                const matchesColor = !colorFilter || order.colorType === colorFilter;
                return matchesSearch && matchesColor;
            });

            tbody.innerHTML = filteredOrders.map(order => `
                <tr>
                    <td>${order.customerName}</td>
                    <td>${order.patternName}</td>
                    <td>${order.colorType}</td>
                    <td>${order.billingType === 'pieces' ? '按个数' : '按平方'}</td>
                    <td>${order.billingType === 'pieces' ? order.piecesCount + '个' : order.squareCount + '㎡'}</td>
                    <td>¥${order.unitPrice.toFixed(2)}</td>
                    <td>¥${order.totalPrice.toFixed(2)}</td>
                    <td>${order.createTime}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editOrder(${order.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteOrder(${order.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        // 订单管理功能
        function showOrderModal(orderId = null) {
            editingOrderId = orderId;
            const modal = document.getElementById('orderModal');
            const title = document.getElementById('orderModalTitle');
            const form = document.getElementById('orderForm');

            loadCustomerOptions();
            loadPatternOptions();

            if (orderId) {
                const order = orders.find(o => o.id === orderId);
                title.textContent = '编辑订单';
                document.getElementById('orderCustomerName').value = order.customerName;
                document.getElementById('orderPatternName').value = order.patternName;

                // 设置计费方式
                document.querySelector(`input[name="billingType"][value="${order.billingType}"]`).checked = true;
                changeBillingType();

                document.getElementById('orderColorType').value = order.colorType;
                document.getElementById('orderRowCount').value = order.rowCount;
                document.getElementById('orderActualHeight').value = order.actualHeight;
                document.getElementById('orderBleedHeight').value = order.bleedHeight;
                document.getElementById('orderCustomerSquare').value = order.customerSquare;
                document.getElementById('orderPiecesCount').value = order.piecesCount;
                document.getElementById('orderSquareCount').value = order.squareCount;
                document.getElementById('orderUnitPrice').value = order.unitPrice;
                document.getElementById('orderTotalPrice').value = order.totalPrice;
            } else {
                title.textContent = '新建订单';
                form.reset();
                // 默认选择按个数计费
                document.querySelector('input[name="billingType"][value="pieces"]').checked = true;
                changeBillingType();
            }

            modal.classList.add('active');
        }

        function closeOrderModal() {
            document.getElementById('orderModal').classList.remove('active');
            editingOrderId = null;
        }

        function editOrder(id) {
            showOrderModal(id);
        }

        function deleteOrder(id) {
            if (confirm('确定要删除这个订单吗？')) {
                orders = orders.filter(o => o.id !== id);
                renderOrdersTable();
                saveToStorage();
                updateDashboard();
            }
        }

        // 客户管理功能
        function renderCustomersTable() {
            const tbody = document.getElementById('customersTableBody');
            const searchTerm = document.getElementById('customerSearch').value.toLowerCase();

            let filteredCustomers = customers.filter(customer => {
                return customer.name.toLowerCase().includes(searchTerm);
            });

            tbody.innerHTML = filteredCustomers.map(customer => `
                <tr>
                    <td>${customer.name}</td>
                    <td>¥${customer.unitPrice.toFixed(2)}</td>
                    <td>${customer.wechat || '-'}</td>
                    <td>${customer.wechatNote || '-'}</td>
                    <td>${customer.contactTime ? new Date(customer.contactTime).toLocaleString() : '-'}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editCustomer(${customer.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteCustomer(${customer.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        function showCustomerModal(customerId = null) {
            editingCustomerId = customerId;
            const modal = document.getElementById('customerModal');
            const title = document.getElementById('customerModalTitle');
            const form = document.getElementById('customerForm');

            if (customerId) {
                const customer = customers.find(c => c.id === customerId);
                title.textContent = '编辑客户';
                document.getElementById('customerName').value = customer.name;
                document.getElementById('customerUnitPrice').value = customer.unitPrice;
                document.getElementById('customerWechat').value = customer.wechat;
                document.getElementById('customerWechatNote').value = customer.wechatNote;
                document.getElementById('customerContactTime').value = customer.contactTime;
            } else {
                title.textContent = '添加客户';
                form.reset();
            }

            modal.classList.add('active');
        }

        function closeCustomerModal() {
            document.getElementById('customerModal').classList.remove('active');
            editingCustomerId = null;
        }

        function editCustomer(id) {
            showCustomerModal(id);
        }

        function deleteCustomer(id) {
            if (confirm('确定要删除这个客户吗？')) {
                customers = customers.filter(c => c.id !== id);
                renderCustomersTable();
                saveToStorage();
            }
        }

        // 图案数据库功能
        function renderPatternsTable() {
            const tbody = document.getElementById('patternsTableBody');
            const searchTerm = document.getElementById('patternSearch').value.toLowerCase();
            const colorFilter = document.getElementById('patternColorFilter').value;

            let filteredPatterns = patterns.filter(pattern => {
                const matchesSearch = pattern.name.toLowerCase().includes(searchTerm);
                const matchesColor = !colorFilter || pattern.colorType === colorFilter;
                return matchesSearch && matchesColor;
            });

            tbody.innerHTML = filteredPatterns.map(pattern => `
                <tr>
                    <td>${pattern.name}</td>
                    <td>${pattern.colorType}</td>
                    <td>${pattern.rowCount}</td>
                    <td>${pattern.actualHeight}mm</td>
                    <td>${pattern.bleedHeight}mm</td>
                    <td>${pattern.customerSquare}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editPattern(${pattern.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deletePattern(${pattern.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        function showPatternModal(patternId = null) {
            editingPatternId = patternId;
            const modal = document.getElementById('patternModal');
            const title = document.getElementById('patternModalTitle');
            const form = document.getElementById('patternForm');

            if (patternId) {
                const pattern = patterns.find(p => p.id === patternId);
                title.textContent = '编辑图案';
                document.getElementById('patternName').value = pattern.name;
                document.getElementById('patternColorType').value = pattern.colorType;
                document.getElementById('patternRowCount').value = pattern.rowCount;
                document.getElementById('patternActualHeight').value = pattern.actualHeight;
                document.getElementById('patternBleedHeight').value = pattern.bleedHeight;
                document.getElementById('patternCustomerSquare').value = pattern.customerSquare;
            } else {
                title.textContent = '添加图案';
                form.reset();
            }

            modal.classList.add('active');
        }

        function closePatternModal() {
            document.getElementById('patternModal').classList.remove('active');
            editingPatternId = null;
        }

        function editPattern(id) {
            showPatternModal(id);
        }

        function deletePattern(id) {
            if (confirm('确定要删除这个图案吗？')) {
                patterns = patterns.filter(p => p.id !== id);
                renderPatternsTable();
                saveToStorage();
            }
        }

        // 耗材管理功能
        function renderMaterialsTable() {
            const tbody = document.getElementById('materialsTableBody');
            const searchTerm = document.getElementById('materialSearch').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;

            let filteredMaterials = materials.filter(material => {
                const matchesSearch = material.name.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || material.category === categoryFilter;
                return matchesSearch && matchesCategory;
            });

            tbody.innerHTML = filteredMaterials.map(material => `
                <tr>
                    <td>${material.name}</td>
                    <td>${material.category}</td>
                    <td>${material.unit}</td>
                    <td>¥${material.price.toFixed(2)}</td>
                    <td>${material.stock}</td>
                    <td><span class="status-${material.status === '正常' ? 'normal' : 'low'}">${material.status}</span></td>
                    <td>${material.supplier || '-'}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editMaterial(${material.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteMaterial(${material.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        function showMaterialModal(materialId = null) {
            editingMaterialId = materialId;
            const modal = document.getElementById('materialModal');
            const title = document.getElementById('materialModalTitle');
            const form = document.getElementById('materialForm');

            if (materialId) {
                const material = materials.find(m => m.id === materialId);
                title.textContent = '编辑耗材';
                document.getElementById('materialName').value = material.name;
                document.getElementById('materialCategory').value = material.category;
                document.getElementById('materialUnit').value = material.unit;
                document.getElementById('materialPrice').value = material.price;
                document.getElementById('materialStock').value = material.stock;
                document.getElementById('materialSupplier').value = material.supplier;
            } else {
                title.textContent = '添加耗材';
                form.reset();
            }

            modal.classList.add('active');
        }

        function closeMaterialModal() {
            document.getElementById('materialModal').classList.remove('active');
            editingMaterialId = null;
        }

        function editMaterial(id) {
            showMaterialModal(id);
        }

        function deleteMaterial(id) {
            if (confirm('确定要删除这个耗材吗？')) {
                materials = materials.filter(m => m.id !== id);
                renderMaterialsTable();
                saveToStorage();
            }
        }

        // 表单提交处理
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const orderData = {
                customerName: document.getElementById('orderCustomerName').value,
                patternName: document.getElementById('orderPatternName').value,
                colorType: document.getElementById('orderColorType').value,
                rowCount: parseInt(document.getElementById('orderRowCount').value),
                actualHeight: parseFloat(document.getElementById('orderActualHeight').value),
                bleedHeight: parseFloat(document.getElementById('orderBleedHeight').value),
                customerSquare: parseFloat(document.getElementById('orderCustomerSquare').value),
                billingType: document.querySelector('input[name="billingType"]:checked').value,
                piecesCount: parseInt(document.getElementById('orderPiecesCount').value) || 0,
                squareCount: parseFloat(document.getElementById('orderSquareCount').value) || 0,
                unitPrice: parseFloat(document.getElementById('orderUnitPrice').value),
                totalPrice: parseFloat(document.getElementById('orderTotalPrice').value),
                createTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
            };

            if (editingOrderId) {
                const index = orders.findIndex(o => o.id === editingOrderId);
                orders[index] = { ...orders[index], ...orderData };
            } else {
                orderData.id = Date.now();
                orders.push(orderData);
            }

            closeOrderModal();
            renderOrdersTable();
            saveToStorage();
            updateDashboard();
        });

        document.getElementById('customerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const customerData = {
                name: document.getElementById('customerName').value,
                unitPrice: parseFloat(document.getElementById('customerUnitPrice').value),
                wechat: document.getElementById('customerWechat').value,
                wechatNote: document.getElementById('customerWechatNote').value,
                contactTime: document.getElementById('customerContactTime').value
            };

            if (editingCustomerId) {
                const index = customers.findIndex(c => c.id === editingCustomerId);
                customers[index] = { ...customers[index], ...customerData };
            } else {
                customerData.id = Date.now();
                customers.push(customerData);
            }

            closeCustomerModal();
            renderCustomersTable();
            saveToStorage();
        });

        document.getElementById('patternForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const patternData = {
                name: document.getElementById('patternName').value,
                colorType: document.getElementById('patternColorType').value,
                rowCount: parseInt(document.getElementById('patternRowCount').value),
                actualHeight: parseFloat(document.getElementById('patternActualHeight').value),
                bleedHeight: parseFloat(document.getElementById('patternBleedHeight').value),
                customerSquare: parseFloat(document.getElementById('patternCustomerSquare').value)
            };

            if (editingPatternId) {
                const index = patterns.findIndex(p => p.id === editingPatternId);
                patterns[index] = { ...patterns[index], ...patternData };
            } else {
                patternData.id = Date.now();
                patterns.push(patternData);
            }

            closePatternModal();
            renderPatternsTable();
            saveToStorage();
        });

        document.getElementById('materialForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const materialData = {
                name: document.getElementById('materialName').value,
                category: document.getElementById('materialCategory').value,
                unit: document.getElementById('materialUnit').value,
                price: parseFloat(document.getElementById('materialPrice').value),
                stock: parseInt(document.getElementById('materialStock').value),
                supplier: document.getElementById('materialSupplier').value,
                status: parseInt(document.getElementById('materialStock').value) < 10 ? '库存不足' : '正常'
            };

            if (editingMaterialId) {
                const index = materials.findIndex(m => m.id === editingMaterialId);
                materials[index] = { ...materials[index], ...materialData };
            } else {
                materialData.id = Date.now();
                materials.push(materialData);
            }

            closeMaterialModal();
            renderMaterialsTable();
            saveToStorage();
        });

        // 数据存储功能
        function saveToStorage() {
            const data = {
                orders: orders,
                customers: customers,
                patterns: patterns,
                materials: materials,
                lastBackup: new Date().toISOString()
            };
            localStorage.setItem('printSystemData', JSON.stringify(data));
        }

        function loadFromStorage() {
            const data = localStorage.getItem('printSystemData');
            if (data) {
                const parsed = JSON.parse(data);
                orders = parsed.orders || orders;
                customers = parsed.customers || customers;
                patterns = parsed.patterns || patterns;
                materials = parsed.materials || materials;

                if (parsed.lastBackup) {
                    document.getElementById('lastBackupTime').textContent = new Date(parsed.lastBackup).toLocaleString();
                }
            }
        }

        // 数据管理功能
        function exportData() {
            const data = {
                orders: orders,
                customers: customers,
                patterns: patterns,
                materials: materials,
                exportTime: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `白墨打印统计系统_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);

            // 更新最后备份时间
            document.getElementById('lastBackupTime').textContent = new Date().toLocaleString();
            saveToStorage();
        }

        function importData(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = JSON.parse(e.target.result);
                        if (confirm('导入数据将覆盖现有数据，确定要继续吗？')) {
                            orders = data.orders || [];
                            customers = data.customers || [];
                            patterns = data.patterns || [];
                            materials = data.materials || [];

                            saveToStorage();
                            updateDashboard();
                            alert('数据导入成功！');
                        }
                    } catch (error) {
                        alert('文件格式错误，请选择正确的JSON文件！');
                    }
                };
                reader.readAsText(file);
            }
        }

        function clearAllData() {
            if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                if (confirm('请再次确认：这将删除所有订单、客户、图案和耗材数据！')) {
                    orders = [];
                    customers = [];
                    patterns = [];
                    materials = [];

                    localStorage.removeItem('printSystemData');
                    updateDashboard();
                    alert('所有数据已清空！');
                }
            }
        }

        // 搜索功能
        document.getElementById('orderSearch').addEventListener('input', renderOrdersTable);
        document.getElementById('colorFilter').addEventListener('change', renderOrdersTable);
        document.getElementById('customerSearch').addEventListener('input', renderCustomersTable);
        document.getElementById('patternSearch').addEventListener('input', renderPatternsTable);
        document.getElementById('patternColorFilter').addEventListener('change', renderPatternsTable);
        document.getElementById('materialSearch').addEventListener('input', renderMaterialsTable);
        document.getElementById('categoryFilter').addEventListener('change', renderMaterialsTable);

        // 更新数据统计
        function updateDataStats() {
            const stats = `
                订单数量：${orders.length} 个<br>
                客户数量：${customers.length} 个<br>
                图案数量：${patterns.length} 个<br>
                耗材数量：${materials.length} 个
            `;
            document.getElementById('dataStats').innerHTML = stats;
        }

        // 初始化系统
        function initSystem() {
            loadFromStorage();
            updateDashboard();
            updateFinanceData();
            updateDataStats();

            // 设置默认页面
            showPage('dashboard');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSystem();
        });
    </script>
</body>
</html>