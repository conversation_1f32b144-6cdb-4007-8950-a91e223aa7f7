<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业财务管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 240px;
            background: #fff;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
        }

        .logo {
            text-align: center;
            padding: 0 20px 20px;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 20px;
        }

        .logo h1 {
            color: #1890ff;
            font-size: 18px;
        }

        .menu {
            list-style: none;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }

        .menu-item:hover {
            background: #f0f0f0;
        }

        .menu-item.active {
            background: #e6f7ff;
            border-left-color: #1890ff;
            color: #1890ff;
        }

        .content {
            flex: 1;
            padding: 20px;
        }

        .page {
            display: none;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }

        .page.active {
            display: block;
        }

        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .page-description {
            color: #8c8c8c;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 14px;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .stat-card .value {
            font-size: 28px;
            font-weight: bold;
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #40a9ff;
        }

        .btn-danger {
            background: #ff4d4f;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .search-box {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 600;
        }

        .table tr:hover {
            background: #f5f5f5;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 500px;
            max-width: 90vw;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            margin-top: 24px;
        }

        .status-normal {
            color: #52c41a;
            font-weight: 500;
        }

        .status-low {
            color: #ff4d4f;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="logo">
                <h1>财务管理系统</h1>
            </div>
            <ul class="menu">
                <li class="menu-item active" onclick="showPage('dashboard')">📊 仪表板</li>
                <li class="menu-item" onclick="showPage('materials')">📦 耗材管理</li>
                <li class="menu-item" onclick="showPage('employees')">👥 员工支出</li>
                <li class="menu-item" onclick="showPage('customers')">🤝 客户结算</li>
                <li class="menu-item" onclick="showPage('area')">📐 平方结算</li>
                <li class="menu-item" onclick="showPage('payments')">💳 付款管理</li>
                <li class="menu-item" onclick="showPage('reports')">📊 财务统计</li>
                <li class="menu-item" onclick="showPage('daily')">📋 日报管理</li>
                <li class="menu-item" onclick="showPage('settings')">⚙️ 基础设置</li>
            </ul>
        </div>

        <div class="content">
            <!-- 仪表板页面 -->
            <div id="dashboard" class="page active">
                <div class="page-header">
                    <h2 class="page-title">仪表板</h2>
                    <p class="page-description">企业财务数据概览和快捷操作</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>总收入</h3>
                        <div class="value">¥125,600</div>
                    </div>
                    <div class="stat-card">
                        <h3>总支出</h3>
                        <div class="value">¥89,400</div>
                    </div>
                    <div class="stat-card">
                        <h3>净利润</h3>
                        <div class="value">¥36,200</div>
                    </div>
                    <div class="stat-card">
                        <h3>利润率</h3>
                        <div class="value">28.8%</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3 style="margin-bottom: 16px;">快捷操作</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button class="btn" onclick="showPage('materials')">添加耗材</button>
                            <button class="btn" onclick="showPage('employees')">员工报销</button>
                            <button class="btn" onclick="showPage('customers')">客户结算</button>
                            <button class="btn" onclick="showPage('payments')">付款申请</button>
                        </div>
                    </div>
                    <div>
                        <h3 style="margin-bottom: 16px;">最近活动</h3>
                        <div style="font-size: 14px; line-height: 1.6;">
                            <div style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #52c41a;">+¥5,600</span> 客户A项目结算 <span style="color: #999;">2小时前</span>
                            </div>
                            <div style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #ff4d4f;">-¥1,200</span> 办公用品采购 <span style="color: #999;">4小时前</span>
                            </div>
                            <div style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #52c41a;">+¥8,900</span> 客户B平方结算 <span style="color: #999;">6小时前</span>
                            </div>
                            <div style="padding: 8px 0;">
                                <span style="color: #ff4d4f;">-¥3,400</span> 员工差旅费报销 <span style="color: #999;">1天前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 耗材管理页面 -->
            <div id="materials" class="page">
                <div class="page-header">
                    <h2 class="page-title">耗材管理</h2>
                    <p class="page-description">管理企业耗材库存、采购和消耗记录</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索耗材名称..." id="materialSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="categoryFilter">
                            <option value="">全部分类</option>
                            <option value="办公用品">办公用品</option>
                            <option value="日用品">日用品</option>
                            <option value="设备配件">设备配件</option>
                        </select>
                    </div>
                    <button class="btn" onclick="showMaterialModal()">添加耗材</button>
                </div>

                <table class="table" id="materialsTable">
                    <thead>
                        <tr>
                            <th>耗材名称</th>
                            <th>分类</th>
                            <th>单位</th>
                            <th>单价</th>
                            <th>库存</th>
                            <th>供应商</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="materialsTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 其他页面 -->
            <div id="employees" class="page">
                <div class="page-header">
                    <h2 class="page-title">员工支出管理</h2>
                    <p class="page-description">管理员工费用申请、审批和报销流程</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索员工姓名..." id="employeeSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="待审批">待审批</option>
                            <option value="已审批">已审批</option>
                            <option value="已报销">已报销</option>
                            <option value="已拒绝">已拒绝</option>
                        </select>
                    </div>
                    <button class="btn" onclick="showEmployeeModal()">添加支出</button>
                </div>

                <table class="table" id="employeesTable">
                    <thead>
                        <tr>
                            <th>员工姓名</th>
                            <th>支出类型</th>
                            <th>金额</th>
                            <th>申请日期</th>
                            <th>状态</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="employeesTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <div id="customers" class="page">
                <div class="page-header">
                    <h2 class="page-title">客户结算管理</h2>
                    <p class="page-description">管理客户账单、收款记录和应收账款</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>客户结算管理功能正在开发中，敬请期待。</p>
                </div>
            </div>

            <div id="area" class="page">
                <div class="page-header">
                    <h2 class="page-title">平方结算管理</h2>
                    <p class="page-description">管理按面积计费的项目结算</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>平方结算管理功能正在开发中，敬请期待。</p>
                </div>
            </div>

            <div id="payments" class="page">
                <div class="page-header">
                    <h2 class="page-title">付款管理</h2>
                    <p class="page-description">管理付款申请、审批流程和付款记录</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>付款管理功能正在开发中，敬请期待。</p>
                </div>
            </div>

            <div id="reports" class="page">
                <div class="page-header">
                    <h2 class="page-title">财务统计</h2>
                    <p class="page-description">查看收支统计、利润分析和财务报表</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>财务统计功能正在开发中，敬请期待。</p>
                </div>
            </div>

            <div id="daily" class="page">
                <div class="page-header">
                    <h2 class="page-title">日报管理</h2>
                    <p class="page-description">每日业务数据录入和统计查看</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>日报管理功能正在开发中，敬请期待。</p>
                </div>
            </div>

            <div id="settings" class="page">
                <div class="page-header">
                    <h2 class="page-title">基础设置</h2>
                    <p class="page-description">管理客户、供应商、产品、员工等基础信息</p>
                </div>

                <!-- 数据管理 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">📁 数据管理</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <p style="margin-bottom: 16px; color: #666;">
                            为了防止数据丢失，建议定期备份数据。您可以导出数据到本地文件，也可以从备份文件恢复数据。
                        </p>
                        <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                            <button class="btn" onclick="exportData()">📤 导出数据</button>
                            <button class="btn" onclick="document.getElementById('importFile').click()">📥 导入数据</button>
                            <button class="btn btn-danger" onclick="clearAllData()">🗑️ 清空数据</button>
                        </div>
                        <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(event)">
                        <div style="margin-top: 12px; font-size: 12px; color: #999;">
                            数据格式：JSON文件 | 包含：耗材、员工支出、客户结算等所有数据
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">ℹ️ 系统信息</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div>
                                <strong>系统版本：</strong> v1.0.0<br>
                                <strong>数据存储：</strong> 浏览器本地存储 + 文件备份<br>
                                <strong>最后备份：</strong> <span id="lastBackupTime">未备份</span>
                            </div>
                            <div>
                                <strong>数据统计：</strong><br>
                                <span id="dataStats">正在统计...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 基础数据设置 -->
                <div>
                    <h3 style="margin-bottom: 16px;">⚙️ 基础数据设置</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>👥 员工管理</h4>
                            <p style="color: #666; font-size: 14px;">管理员工基础信息</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('功能开发中')">管理员工</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>🤝 客户管理</h4>
                            <p style="color: #666; font-size: 14px;">管理客户基础信息</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('功能开发中')">管理客户</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>🏢 供应商管理</h4>
                            <p style="color: #666; font-size: 14px;">管理供应商信息</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('功能开发中')">管理供应商</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>💰 价格设置</h4>
                            <p style="color: #666; font-size: 14px;">设置产品和服务价格</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('功能开发中')">价格设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 耗材编辑模态框 -->
    <div id="materialModal" class="modal">
        <div class="modal-content">
            <h3 id="modalTitle">添加耗材</h3>
            <form id="materialForm">
                <div class="form-group">
                    <label>耗材名称 *</label>
                    <input type="text" id="materialName" required>
                </div>
                <div class="form-group">
                    <label>分类 *</label>
                    <select id="materialCategory" required>
                        <option value="">请选择分类</option>
                        <option value="办公用品">办公用品</option>
                        <option value="日用品">日用品</option>
                        <option value="设备配件">设备配件</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>单位 *</label>
                    <input type="text" id="materialUnit" placeholder="如：个、包、套等" required>
                </div>
                <div class="form-group">
                    <label>单价 *</label>
                    <input type="number" id="materialPrice" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label>库存数量 *</label>
                    <input type="number" id="materialStock" min="0" required>
                </div>
                <div class="form-group">
                    <label>供应商</label>
                    <input type="text" id="materialSupplier">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeMaterialModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 员工支出编辑模态框 -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <h3 id="employeeModalTitle">添加员工支出</h3>
            <form id="employeeForm">
                <div class="form-group">
                    <label>员工姓名 *</label>
                    <input type="text" id="employeeName" required>
                </div>
                <div class="form-group">
                    <label>支出类型 *</label>
                    <select id="expenseType" required>
                        <option value="">请选择类型</option>
                        <option value="差旅费">差旅费</option>
                        <option value="餐费">餐费</option>
                        <option value="交通费">交通费</option>
                        <option value="办公用品">办公用品</option>
                        <option value="培训费">培训费</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>金额 *</label>
                    <input type="number" id="expenseAmount" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label>申请日期 *</label>
                    <input type="date" id="expenseDate" required>
                </div>
                <div class="form-group">
                    <label>状态 *</label>
                    <select id="expenseStatus" required>
                        <option value="待审批">待审批</option>
                        <option value="已审批">已审批</option>
                        <option value="已报销">已报销</option>
                        <option value="已拒绝">已拒绝</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>备注</label>
                    <input type="text" id="expenseNote" placeholder="支出详细说明">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeEmployeeModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 示例数据
        let materials = [
            {
                id: 1,
                name: '办公纸张',
                category: '办公用品',
                unit: '包',
                price: 25.00,
                stock: 50,
                supplier: '办公用品公司A',
                status: '正常'
            },
            {
                id: 2,
                name: '打印墨盒',
                category: '办公用品',
                unit: '个',
                price: 120.00,
                stock: 8,
                supplier: '办公用品公司B',
                status: '库存不足'
            },
            {
                id: 3,
                name: '清洁用品',
                category: '日用品',
                unit: '套',
                price: 45.00,
                stock: 30,
                supplier: '清洁用品公司',
                status: '正常'
            }
        ];

        // 员工支出数据
        let employees = [
            {
                id: 1,
                name: '张三',
                type: '差旅费',
                amount: 1200.00,
                date: '2025-05-25',
                status: '已审批',
                note: '北京出差住宿费'
            },
            {
                id: 2,
                name: '李四',
                type: '餐费',
                amount: 350.00,
                date: '2025-05-26',
                status: '待审批',
                note: '客户招待费'
            },
            {
                id: 3,
                name: '王五',
                type: '交通费',
                amount: 180.00,
                date: '2025-05-27',
                status: '已报销',
                note: '市内交通费'
            },
            {
                id: 4,
                name: '赵六',
                type: '办公用品',
                amount: 450.00,
                date: '2025-05-28',
                status: '已审批',
                note: '采购办公用品'
            }
        ];

        let editingMaterialId = null;
        let editingEmployeeId = null;

        // 页面切换
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(pageId).classList.add('active');

            // 设置对应菜单项为active
            event.target.classList.add('active');

            // 根据页面类型刷新对应表格
            if (pageId === 'materials') {
                renderMaterialsTable();
            } else if (pageId === 'employees') {
                renderEmployeesTable();
            }
        }

        // 渲染耗材表格
        function renderMaterialsTable() {
            const tbody = document.getElementById('materialsTableBody');
            const searchTerm = document.getElementById('materialSearch').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;

            let filteredMaterials = materials.filter(material => {
                const matchesSearch = material.name.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || material.category === categoryFilter;
                return matchesSearch && matchesCategory;
            });

            tbody.innerHTML = filteredMaterials.map(material => `
                <tr>
                    <td>${material.name}</td>
                    <td>${material.category}</td>
                    <td>${material.unit}</td>
                    <td>¥${material.price.toFixed(2)}</td>
                    <td>${material.stock}</td>
                    <td>${material.supplier}</td>
                    <td><span class="${material.status === '正常' ? 'status-normal' : 'status-low'}">${material.status}</span></td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editMaterial(${material.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteMaterial(${material.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        // 显示耗材模态框
        function showMaterialModal(materialId = null) {
            editingMaterialId = materialId;
            const modal = document.getElementById('materialModal');
            const title = document.getElementById('modalTitle');
            const form = document.getElementById('materialForm');

            if (materialId) {
                const material = materials.find(m => m.id === materialId);
                title.textContent = '编辑耗材';
                document.getElementById('materialName').value = material.name;
                document.getElementById('materialCategory').value = material.category;
                document.getElementById('materialUnit').value = material.unit;
                document.getElementById('materialPrice').value = material.price;
                document.getElementById('materialStock').value = material.stock;
                document.getElementById('materialSupplier').value = material.supplier;
            } else {
                title.textContent = '添加耗材';
                form.reset();
            }

            modal.classList.add('active');
        }

        // 关闭耗材模态框
        function closeMaterialModal() {
            document.getElementById('materialModal').classList.remove('active');
            editingMaterialId = null;
        }

        // 编辑耗材
        function editMaterial(id) {
            showMaterialModal(id);
        }

        // 删除耗材
        function deleteMaterial(id) {
            if (confirm('确定要删除这个耗材吗？')) {
                materials = materials.filter(m => m.id !== id);
                renderMaterialsTable();
            }
        }

        // 表单提交
        document.getElementById('materialForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('materialName').value,
                category: document.getElementById('materialCategory').value,
                unit: document.getElementById('materialUnit').value,
                price: parseFloat(document.getElementById('materialPrice').value),
                stock: parseInt(document.getElementById('materialStock').value),
                supplier: document.getElementById('materialSupplier').value
            };

            formData.status = formData.stock > 10 ? '正常' : '库存不足';

            if (editingMaterialId) {
                // 编辑
                const index = materials.findIndex(m => m.id === editingMaterialId);
                materials[index] = { ...materials[index], ...formData };
            } else {
                // 新增
                formData.id = Date.now();
                materials.push(formData);
            }

            renderMaterialsTable();
            closeMaterialModal();
        });

        // 搜索和筛选
        document.getElementById('materialSearch').addEventListener('input', renderMaterialsTable);
        document.getElementById('categoryFilter').addEventListener('change', renderMaterialsTable);

        // 点击模态框外部关闭
        document.getElementById('materialModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMaterialModal();
            }
        });

        // ========== 员工支出管理功能 ==========

        // 渲染员工支出表格
        function renderEmployeesTable() {
            const tbody = document.getElementById('employeesTableBody');
            const searchTerm = document.getElementById('employeeSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            let filteredEmployees = employees.filter(employee => {
                const matchesSearch = employee.name.toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || employee.status === statusFilter;
                return matchesSearch && matchesStatus;
            });

            tbody.innerHTML = filteredEmployees.map(employee => `
                <tr>
                    <td>${employee.name}</td>
                    <td>${employee.type}</td>
                    <td>¥${employee.amount.toFixed(2)}</td>
                    <td>${employee.date}</td>
                    <td><span class="status-${getStatusClass(employee.status)}">${employee.status}</span></td>
                    <td>${employee.note}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editEmployee(${employee.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteEmployee(${employee.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case '已审批':
                case '已报销':
                    return 'normal';
                case '待审批':
                    return 'low';
                case '已拒绝':
                    return 'low';
                default:
                    return 'normal';
            }
        }

        // 显示员工支出模态框
        function showEmployeeModal(employeeId = null) {
            editingEmployeeId = employeeId;
            const modal = document.getElementById('employeeModal');
            const title = document.getElementById('employeeModalTitle');
            const form = document.getElementById('employeeForm');

            if (employeeId) {
                const employee = employees.find(e => e.id === employeeId);
                title.textContent = '编辑员工支出';
                document.getElementById('employeeName').value = employee.name;
                document.getElementById('expenseType').value = employee.type;
                document.getElementById('expenseAmount').value = employee.amount;
                document.getElementById('expenseDate').value = employee.date;
                document.getElementById('expenseStatus').value = employee.status;
                document.getElementById('expenseNote').value = employee.note;
            } else {
                title.textContent = '添加员工支出';
                form.reset();
                // 设置默认日期为今天
                document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
            }

            modal.classList.add('active');
        }

        // 关闭员工支出模态框
        function closeEmployeeModal() {
            document.getElementById('employeeModal').classList.remove('active');
            editingEmployeeId = null;
        }

        // 编辑员工支出
        function editEmployee(id) {
            showEmployeeModal(id);
        }

        // 删除员工支出
        function deleteEmployee(id) {
            if (confirm('确定要删除这条员工支出记录吗？')) {
                employees = employees.filter(e => e.id !== id);
                renderEmployeesTable();
                saveEmployeesToStorage();
            }
        }

        // 员工支出表单提交
        document.getElementById('employeeForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('employeeName').value,
                type: document.getElementById('expenseType').value,
                amount: parseFloat(document.getElementById('expenseAmount').value),
                date: document.getElementById('expenseDate').value,
                status: document.getElementById('expenseStatus').value,
                note: document.getElementById('expenseNote').value
            };

            if (editingEmployeeId) {
                // 编辑
                const index = employees.findIndex(e => e.id === editingEmployeeId);
                employees[index] = { ...employees[index], ...formData };
            } else {
                // 新增
                formData.id = Date.now();
                employees.push(formData);
            }

            renderEmployeesTable();
            saveEmployeesToStorage();
            closeEmployeeModal();
        });

        // 员工支出搜索和筛选
        document.getElementById('employeeSearch').addEventListener('input', renderEmployeesTable);
        document.getElementById('statusFilter').addEventListener('change', renderEmployeesTable);

        // 点击员工支出模态框外部关闭
        document.getElementById('employeeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEmployeeModal();
            }
        });

        // 数据导出功能
        function exportData() {
            const allData = {
                materials: materials,
                employees: employees, // 包含员工支出数据
                customers: [], // 将来添加客户数据
                payments: [],  // 将来添加付款数据
                exportTime: new Date().toISOString(),
                version: '1.0.0'
            };

            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `财务系统数据备份_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            // 更新最后备份时间
            localStorage.setItem('lastBackupTime', new Date().toLocaleString());
            updateSystemInfo();

            alert('数据导出成功！文件已下载到您的下载文件夹。');
        }

        // 数据导入功能
        function importData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    // 验证数据格式
                    if (!importedData.materials || !Array.isArray(importedData.materials)) {
                        throw new Error('数据格式不正确');
                    }

                    // 确认导入
                    const employeeCount = importedData.employees ? importedData.employees.length : 0;
                    if (confirm('导入数据将覆盖现有数据，确定要继续吗？\n\n导入数据信息：\n' +
                               `- 耗材数据：${importedData.materials.length} 条\n` +
                               `- 员工支出：${employeeCount} 条\n` +
                               `- 导出时间：${importedData.exportTime ? new Date(importedData.exportTime).toLocaleString() : '未知'}\n` +
                               `- 版本：${importedData.version || '未知'}`)) {

                        // 导入数据
                        materials = importedData.materials || [];
                        employees = importedData.employees || [];

                        // 保存到localStorage
                        localStorage.setItem('materials', JSON.stringify(materials));
                        localStorage.setItem('employees', JSON.stringify(employees));

                        // 刷新界面
                        renderMaterialsTable();
                        renderEmployeesTable();
                        updateSystemInfo();

                        alert('数据导入成功！');
                    }
                } catch (error) {
                    alert('导入失败：' + error.message + '\n请确保选择的是正确的备份文件。');
                }
            };
            reader.readAsText(file);

            // 清空文件选择
            event.target.value = '';
        }

        // 清空所有数据
        function clearAllData() {
            if (confirm('警告：此操作将清空所有数据且无法恢复！\n\n建议在清空前先导出数据备份。\n\n确定要继续吗？')) {
                if (confirm('最后确认：真的要清空所有数据吗？')) {
                    materials = [];
                    employees = [];
                    localStorage.removeItem('materials');
                    localStorage.removeItem('employees');
                    localStorage.removeItem('lastBackupTime');

                    renderMaterialsTable();
                    renderEmployeesTable();
                    updateSystemInfo();

                    alert('所有数据已清空。');
                }
            }
        }

        // 更新系统信息
        function updateSystemInfo() {
            const lastBackup = localStorage.getItem('lastBackupTime') || '未备份';
            document.getElementById('lastBackupTime').textContent = lastBackup;

            const stats = `耗材：${materials.length} 条，员工支出：${employees.length} 条`;
            document.getElementById('dataStats').textContent = stats;
        }

        // 从localStorage加载数据
        function loadDataFromStorage() {
            const savedMaterials = localStorage.getItem('materials');
            if (savedMaterials) {
                try {
                    materials = JSON.parse(savedMaterials);
                } catch (error) {
                    console.error('加载耗材数据失败:', error);
                }
            }

            const savedEmployees = localStorage.getItem('employees');
            if (savedEmployees) {
                try {
                    employees = JSON.parse(savedEmployees);
                } catch (error) {
                    console.error('加载员工数据失败:', error);
                }
            }
        }

        // 保存数据到localStorage
        function saveDataToStorage() {
            localStorage.setItem('materials', JSON.stringify(materials));
        }

        function saveEmployeesToStorage() {
            localStorage.setItem('employees', JSON.stringify(employees));
        }

        // 修改原有的数据操作函数，添加自动保存
        const originalRenderMaterialsTable = renderMaterialsTable;
        renderMaterialsTable = function() {
            originalRenderMaterialsTable();
            saveDataToStorage();
        };

        const originalRenderEmployeesTable = renderEmployeesTable;
        renderEmployeesTable = function() {
            originalRenderEmployeesTable();
            saveEmployeesToStorage();
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDataFromStorage();
            renderMaterialsTable();
            renderEmployeesTable();
            updateSystemInfo();
        });
    </script>
</body>
</html>
