<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业财务管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 240px;
            background: #fff;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
        }
        
        .logo {
            text-align: center;
            padding: 0 20px 20px;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 20px;
        }
        
        .logo h1 {
            color: #1890ff;
            font-size: 18px;
        }
        
        .menu {
            list-style: none;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            background: #f0f0f0;
        }
        
        .menu-item.active {
            background: #e6f7ff;
            border-left-color: #1890ff;
            color: #1890ff;
        }
        
        .content {
            flex: 1;
            padding: 20px;
        }
        
        .page {
            display: none;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }
        
        .page.active {
            display: block;
        }
        
        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }
        
        .page-description {
            color: #8c8c8c;
            font-size: 14px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 14px;
            margin-bottom: 8px;
            opacity: 0.9;
        }
        
        .stat-card .value {
            font-size: 28px;
            font-weight: bold;
        }
        
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn-danger {
            background: #ff4d4f;
        }
        
        .btn-danger:hover {
            background: #ff7875;
        }
        
        .search-box {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .table th {
            background: #fafafa;
            font-weight: 600;
        }
        
        .table tr:hover {
            background: #f5f5f5;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 500px;
            max-width: 90vw;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            margin-top: 24px;
        }
        
        .status-normal {
            color: #52c41a;
            font-weight: 500;
        }
        
        .status-low {
            color: #ff4d4f;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="logo">
                <h1>财务管理系统</h1>
            </div>
            <ul class="menu">
                <li class="menu-item active" onclick="showPage('dashboard')">📊 仪表板</li>
                <li class="menu-item" onclick="showPage('materials')">📦 耗材管理</li>
                <li class="menu-item" onclick="showPage('employees')">👥 员工支出</li>
                <li class="menu-item" onclick="showPage('customers')">🤝 客户结算</li>
                <li class="menu-item" onclick="showPage('area')">📐 平方结算</li>
                <li class="menu-item" onclick="showPage('payments')">💳 付款管理</li>
                <li class="menu-item" onclick="showPage('reports')">📊 财务统计</li>
                <li class="menu-item" onclick="showPage('daily')">📋 日报管理</li>
                <li class="menu-item" onclick="showPage('settings')">⚙️ 基础设置</li>
            </ul>
        </div>
        
        <div class="content">
            <!-- 仪表板页面 -->
            <div id="dashboard" class="page active">
                <div class="page-header">
                    <h2 class="page-title">仪表板</h2>
                    <p class="page-description">企业财务数据概览和快捷操作</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>总收入</h3>
                        <div class="value">¥125,600</div>
                    </div>
                    <div class="stat-card">
                        <h3>总支出</h3>
                        <div class="value">¥89,400</div>
                    </div>
                    <div class="stat-card">
                        <h3>净利润</h3>
                        <div class="value">¥36,200</div>
                    </div>
                    <div class="stat-card">
                        <h3>利润率</h3>
                        <div class="value">28.8%</div>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3 style="margin-bottom: 16px;">快捷操作</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button class="btn" onclick="showPage('materials')">添加耗材</button>
                            <button class="btn" onclick="showPage('employees')">员工报销</button>
                            <button class="btn" onclick="showPage('customers')">客户结算</button>
                            <button class="btn" onclick="showPage('payments')">付款申请</button>
                        </div>
                    </div>
                    <div>
                        <h3 style="margin-bottom: 16px;">最近活动</h3>
                        <div style="font-size: 14px; line-height: 1.6;">
                            <div style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #52c41a;">+¥5,600</span> 客户A项目结算 <span style="color: #999;">2小时前</span>
                            </div>
                            <div style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #ff4d4f;">-¥1,200</span> 办公用品采购 <span style="color: #999;">4小时前</span>
                            </div>
                            <div style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #52c41a;">+¥8,900</span> 客户B平方结算 <span style="color: #999;">6小时前</span>
                            </div>
                            <div style="padding: 8px 0;">
                                <span style="color: #ff4d4f;">-¥3,400</span> 员工差旅费报销 <span style="color: #999;">1天前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 耗材管理页面 -->
            <div id="materials" class="page">
                <div class="page-header">
                    <h2 class="page-title">耗材管理</h2>
                    <p class="page-description">管理企业耗材库存、采购和消耗记录</p>
                </div>
                
                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索耗材名称..." id="materialSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="categoryFilter">
                            <option value="">全部分类</option>
                            <option value="办公用品">办公用品</option>
                            <option value="日用品">日用品</option>
                            <option value="设备配件">设备配件</option>
                        </select>
                    </div>
                    <button class="btn" onclick="showMaterialModal()">添加耗材</button>
                </div>
                
                <table class="table" id="materialsTable">
                    <thead>
                        <tr>
                            <th>耗材名称</th>
                            <th>分类</th>
                            <th>单位</th>
                            <th>单价</th>
                            <th>库存</th>
                            <th>供应商</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="materialsTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 其他页面 -->
            <div id="employees" class="page">
                <div class="page-header">
                    <h2 class="page-title">员工支出管理</h2>
                    <p class="page-description">管理员工费用申请、审批和报销流程</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>员工支出管理功能正在开发中，敬请期待。</p>
                </div>
            </div>
            
            <div id="customers" class="page">
                <div class="page-header">
                    <h2 class="page-title">客户结算管理</h2>
                    <p class="page-description">管理客户账单、收款记录和应收账款</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>客户结算管理功能正在开发中，敬请期待。</p>
                </div>
            </div>
            
            <div id="area" class="page">
                <div class="page-header">
                    <h2 class="page-title">平方结算管理</h2>
                    <p class="page-description">管理按面积计费的项目结算</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>平方结算管理功能正在开发中，敬请期待。</p>
                </div>
            </div>
            
            <div id="payments" class="page">
                <div class="page-header">
                    <h2 class="page-title">付款管理</h2>
                    <p class="page-description">管理付款申请、审批流程和付款记录</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>付款管理功能正在开发中，敬请期待。</p>
                </div>
            </div>
            
            <div id="reports" class="page">
                <div class="page-header">
                    <h2 class="page-title">财务统计</h2>
                    <p class="page-description">查看收支统计、利润分析和财务报表</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>财务统计功能正在开发中，敬请期待。</p>
                </div>
            </div>
            
            <div id="daily" class="page">
                <div class="page-header">
                    <h2 class="page-title">日报管理</h2>
                    <p class="page-description">每日业务数据录入和统计查看</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>日报管理功能正在开发中，敬请期待。</p>
                </div>
            </div>
            
            <div id="settings" class="page">
                <div class="page-header">
                    <h2 class="page-title">基础设置</h2>
                    <p class="page-description">管理客户、供应商、产品、员工等基础信息</p>
                </div>
                <div style="text-align: center; padding: 40px 0;">
                    <h3>功能开发中...</h3>
                    <p>基础设置功能正在开发中，敬请期待。</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 耗材编辑模态框 -->
    <div id="materialModal" class="modal">
        <div class="modal-content">
            <h3 id="modalTitle">添加耗材</h3>
            <form id="materialForm">
                <div class="form-group">
                    <label>耗材名称 *</label>
                    <input type="text" id="materialName" required>
                </div>
                <div class="form-group">
                    <label>分类 *</label>
                    <select id="materialCategory" required>
                        <option value="">请选择分类</option>
                        <option value="办公用品">办公用品</option>
                        <option value="日用品">日用品</option>
                        <option value="设备配件">设备配件</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>单位 *</label>
                    <input type="text" id="materialUnit" placeholder="如：个、包、套等" required>
                </div>
                <div class="form-group">
                    <label>单价 *</label>
                    <input type="number" id="materialPrice" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label>库存数量 *</label>
                    <input type="number" id="materialStock" min="0" required>
                </div>
                <div class="form-group">
                    <label>供应商</label>
                    <input type="text" id="materialSupplier">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeMaterialModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 示例数据
        let materials = [
            {
                id: 1,
                name: '办公纸张',
                category: '办公用品',
                unit: '包',
                price: 25.00,
                stock: 50,
                supplier: '办公用品公司A',
                status: '正常'
            },
            {
                id: 2,
                name: '打印墨盒',
                category: '办公用品',
                unit: '个',
                price: 120.00,
                stock: 8,
                supplier: '办公用品公司B',
                status: '库存不足'
            },
            {
                id: 3,
                name: '清洁用品',
                category: '日用品',
                unit: '套',
                price: 45.00,
                stock: 30,
                supplier: '清洁用品公司',
                status: '正常'
            }
        ];
        
        let editingMaterialId = null;
        
        // 页面切换
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            
            // 设置对应菜单项为active
            event.target.classList.add('active');
            
            // 如果是耗材页面，刷新表格
            if (pageId === 'materials') {
                renderMaterialsTable();
            }
        }
        
        // 渲染耗材表格
        function renderMaterialsTable() {
            const tbody = document.getElementById('materialsTableBody');
            const searchTerm = document.getElementById('materialSearch').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            
            let filteredMaterials = materials.filter(material => {
                const matchesSearch = material.name.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || material.category === categoryFilter;
                return matchesSearch && matchesCategory;
            });
            
            tbody.innerHTML = filteredMaterials.map(material => `
                <tr>
                    <td>${material.name}</td>
                    <td>${material.category}</td>
                    <td>${material.unit}</td>
                    <td>¥${material.price.toFixed(2)}</td>
                    <td>${material.stock}</td>
                    <td>${material.supplier}</td>
                    <td><span class="${material.status === '正常' ? 'status-normal' : 'status-low'}">${material.status}</span></td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editMaterial(${material.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteMaterial(${material.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }
        
        // 显示耗材模态框
        function showMaterialModal(materialId = null) {
            editingMaterialId = materialId;
            const modal = document.getElementById('materialModal');
            const title = document.getElementById('modalTitle');
            const form = document.getElementById('materialForm');
            
            if (materialId) {
                const material = materials.find(m => m.id === materialId);
                title.textContent = '编辑耗材';
                document.getElementById('materialName').value = material.name;
                document.getElementById('materialCategory').value = material.category;
                document.getElementById('materialUnit').value = material.unit;
                document.getElementById('materialPrice').value = material.price;
                document.getElementById('materialStock').value = material.stock;
                document.getElementById('materialSupplier').value = material.supplier;
            } else {
                title.textContent = '添加耗材';
                form.reset();
            }
            
            modal.classList.add('active');
        }
        
        // 关闭耗材模态框
        function closeMaterialModal() {
            document.getElementById('materialModal').classList.remove('active');
            editingMaterialId = null;
        }
        
        // 编辑耗材
        function editMaterial(id) {
            showMaterialModal(id);
        }
        
        // 删除耗材
        function deleteMaterial(id) {
            if (confirm('确定要删除这个耗材吗？')) {
                materials = materials.filter(m => m.id !== id);
                renderMaterialsTable();
            }
        }
        
        // 表单提交
        document.getElementById('materialForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('materialName').value,
                category: document.getElementById('materialCategory').value,
                unit: document.getElementById('materialUnit').value,
                price: parseFloat(document.getElementById('materialPrice').value),
                stock: parseInt(document.getElementById('materialStock').value),
                supplier: document.getElementById('materialSupplier').value
            };
            
            formData.status = formData.stock > 10 ? '正常' : '库存不足';
            
            if (editingMaterialId) {
                // 编辑
                const index = materials.findIndex(m => m.id === editingMaterialId);
                materials[index] = { ...materials[index], ...formData };
            } else {
                // 新增
                formData.id = Date.now();
                materials.push(formData);
            }
            
            renderMaterialsTable();
            closeMaterialModal();
        });
        
        // 搜索和筛选
        document.getElementById('materialSearch').addEventListener('input', renderMaterialsTable);
        document.getElementById('categoryFilter').addEventListener('change', renderMaterialsTable);
        
        // 点击模态框外部关闭
        document.getElementById('materialModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMaterialModal();
            }
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderMaterialsTable();
        });
    </script>
</body>
</html>
