import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Materials from './pages/Materials'
import EmployeeExpenses from './pages/EmployeeExpenses'
import CustomerSettlements from './pages/CustomerSettlements'
import AreaSettlements from './pages/AreaSettlements'
import Payments from './pages/Payments'
import FinancialStats from './pages/FinancialStats'
import DailyReports from './pages/DailyReports'
import Settings from './pages/Settings'
import { AppProvider } from './context/AppContext'
import './App.css'

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AppProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/materials" element={<Materials />} />
              <Route path="/employee-expenses" element={<EmployeeExpenses />} />
              <Route path="/customer-settlements" element={<CustomerSettlements />} />
              <Route path="/area-settlements" element={<AreaSettlements />} />
              <Route path="/payments" element={<Payments />} />
              <Route path="/financial-stats" element={<FinancialStats />} />
              <Route path="/daily-reports" element={<DailyReports />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Layout>
        </Router>
      </AppProvider>
    </ConfigProvider>
  )
}

export default App
