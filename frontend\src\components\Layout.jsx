import React, { useState } from 'react'
import { Layout as AntLayout, Menu, Avatar, Dropdown, Space, Typography } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  DashboardOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  TeamOutlined,
  CalculatorOutlined,
  CreditCardOutlined,
  Bar<PERSON>hartOutlined,
  FileTextOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons'

const { Header, Sider, Content, Footer } = AntLayout
const { Title } = Typography

const menuItems = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: '/materials',
    icon: <ShoppingCartOutlined />,
    label: '耗材管理',
  },
  {
    key: '/employee-expenses',
    icon: <TeamOutlined />,
    label: '员工支出',
  },
  {
    key: '/customer-settlements',
    icon: <UserOutlined />,
    label: '客户结算',
  },
  {
    key: '/area-settlements',
    icon: <CalculatorOutlined />,
    label: '平方结算',
  },
  {
    key: '/payments',
    icon: <CreditCardOutlined />,
    label: '付款管理',
  },
  {
    key: '/financial-stats',
    icon: <BarChartOutlined />,
    label: '财务统计',
  },
  {
    key: '/daily-reports',
    icon: <FileTextOutlined />,
    label: '日报管理',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '基础设置',
  },
]

const userMenuItems = [
  {
    key: 'profile',
    icon: <UserOutlined />,
    label: '个人资料',
  },
  {
    key: 'logout',
    icon: <LogoutOutlined />,
    label: '退出登录',
  },
]

function Layout({ children }) {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()

  const handleMenuClick = ({ key }) => {
    navigate(key)
  }

  const handleUserMenuClick = ({ key }) => {
    if (key === 'logout') {
      // 处理退出登录
      console.log('退出登录')
    } else if (key === 'profile') {
      // 处理个人资料
      console.log('个人资料')
    }
  }

  return (
    <AntLayout className="app-layout">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="app-sider"
        width={240}
      >
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <Title level={4} style={{ color: '#1890ff', margin: 0 }}>
            {collapsed ? 'ERP' : '财务管理系统'}
          </Title>
        </div>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      <AntLayout>
        <Header className="app-header">
          <Space>
            {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
              className: 'trigger',
              onClick: () => setCollapsed(!collapsed),
              style: { fontSize: '18px', cursor: 'pointer' }
            })}
          </Space>
          <Dropdown
            menu={{
              items: userMenuItems,
              onClick: handleUserMenuClick,
            }}
            placement="bottomRight"
          >
            <Space style={{ cursor: 'pointer' }}>
              <Avatar icon={<UserOutlined />} />
              <span>管理员</span>
            </Space>
          </Dropdown>
        </Header>
        <Content className="app-content">
          {children}
        </Content>
        <Footer className="app-footer">
          企业财务管理系统 ©2025 Created by ERP Team
        </Footer>
      </AntLayout>
    </AntLayout>
  )
}

export default Layout
