import React, { createContext, useContext, useReducer } from 'react'

// 初始状态
const initialState = {
  user: {
    id: 1,
    name: '管理员',
    role: 'admin',
  },
  materials: [],
  employees: [],
  customers: [],
  expenses: [],
  settlements: [],
  payments: [],
  loading: false,
  error: null,
}

// Action 类型
export const actionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_MATERIALS: 'SET_MATERIALS',
  ADD_MATERIAL: 'ADD_MATERIAL',
  UPDATE_MATERIAL: 'UPDATE_MATERIAL',
  DELETE_MATERIAL: 'DELETE_MATERIAL',
  SET_EMPLOYEES: 'SET_EMPLOYEES',
  ADD_EMPLOYEE: 'ADD_EMPLOYEE',
  UPDATE_EMPLOYEE: 'UPDATE_EMPLOYEE',
  DELETE_EMPLOYEE: 'DELETE_EMPLOYEE',
  SET_CUSTOMERS: 'SET_CUSTOMERS',
  ADD_CUSTOMER: 'ADD_CUSTOMER',
  UPDATE_CUSTOMER: 'UPDATE_CUSTOMER',
  DELETE_CUSTOMER: 'DELETE_CUSTOMER',
}

// Reducer
function appReducer(state, action) {
  switch (action.type) {
    case actionTypes.SET_LOADING:
      return { ...state, loading: action.payload }
    
    case actionTypes.SET_ERROR:
      return { ...state, error: action.payload }
    
    case actionTypes.SET_MATERIALS:
      return { ...state, materials: action.payload }
    
    case actionTypes.ADD_MATERIAL:
      return { ...state, materials: [...state.materials, action.payload] }
    
    case actionTypes.UPDATE_MATERIAL:
      return {
        ...state,
        materials: state.materials.map(item =>
          item.id === action.payload.id ? action.payload : item
        )
      }
    
    case actionTypes.DELETE_MATERIAL:
      return {
        ...state,
        materials: state.materials.filter(item => item.id !== action.payload)
      }
    
    case actionTypes.SET_EMPLOYEES:
      return { ...state, employees: action.payload }
    
    case actionTypes.ADD_EMPLOYEE:
      return { ...state, employees: [...state.employees, action.payload] }
    
    case actionTypes.UPDATE_EMPLOYEE:
      return {
        ...state,
        employees: state.employees.map(item =>
          item.id === action.payload.id ? action.payload : item
        )
      }
    
    case actionTypes.DELETE_EMPLOYEE:
      return {
        ...state,
        employees: state.employees.filter(item => item.id !== action.payload)
      }
    
    case actionTypes.SET_CUSTOMERS:
      return { ...state, customers: action.payload }
    
    case actionTypes.ADD_CUSTOMER:
      return { ...state, customers: [...state.customers, action.payload] }
    
    case actionTypes.UPDATE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.map(item =>
          item.id === action.payload.id ? action.payload : item
        )
      }
    
    case actionTypes.DELETE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.filter(item => item.id !== action.payload)
      }
    
    default:
      return state
  }
}

// Context
const AppContext = createContext()

// Provider 组件
export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState)

  const value = {
    state,
    dispatch,
    // 便捷方法
    setLoading: (loading) => dispatch({ type: actionTypes.SET_LOADING, payload: loading }),
    setError: (error) => dispatch({ type: actionTypes.SET_ERROR, payload: error }),
    
    // 耗材相关方法
    setMaterials: (materials) => dispatch({ type: actionTypes.SET_MATERIALS, payload: materials }),
    addMaterial: (material) => dispatch({ type: actionTypes.ADD_MATERIAL, payload: material }),
    updateMaterial: (material) => dispatch({ type: actionTypes.UPDATE_MATERIAL, payload: material }),
    deleteMaterial: (id) => dispatch({ type: actionTypes.DELETE_MATERIAL, payload: id }),
    
    // 员工相关方法
    setEmployees: (employees) => dispatch({ type: actionTypes.SET_EMPLOYEES, payload: employees }),
    addEmployee: (employee) => dispatch({ type: actionTypes.ADD_EMPLOYEE, payload: employee }),
    updateEmployee: (employee) => dispatch({ type: actionTypes.UPDATE_EMPLOYEE, payload: employee }),
    deleteEmployee: (id) => dispatch({ type: actionTypes.DELETE_EMPLOYEE, payload: id }),
    
    // 客户相关方法
    setCustomers: (customers) => dispatch({ type: actionTypes.SET_CUSTOMERS, payload: customers }),
    addCustomer: (customer) => dispatch({ type: actionTypes.ADD_CUSTOMER, payload: customer }),
    updateCustomer: (customer) => dispatch({ type: actionTypes.UPDATE_CUSTOMER, payload: customer }),
    deleteCustomer: (id) => dispatch({ type: actionTypes.DELETE_CUSTOMER, payload: id }),
  }

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  )
}

// Hook
export function useApp() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within an AppProvider')
  }
  return context
}

export default AppContext
