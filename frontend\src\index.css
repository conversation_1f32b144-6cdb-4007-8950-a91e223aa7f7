/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

#root {
  min-height: 100vh;
}

/* 全局样式 */
.page-container {
  padding: 24px;
  background: #fff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.page-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* 表格样式 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-toolbar-left {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-toolbar-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
}

.stats-card .ant-statistic-title {
  color: rgba(255, 255, 255, 0.85);
}

.stats-card .ant-statistic-content {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    margin: 8px;
    padding: 16px;
  }
  
  .table-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .table-toolbar-left,
  .table-toolbar-right {
    justify-content: center;
  }
}
