import React from 'react'
import { Row, Col, Card, Statistic, Typography, Space, Button } from 'antd'
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons'

const { Title, Paragraph } = Typography

function Dashboard() {
  // 模拟数据
  const stats = {
    totalRevenue: 125600,
    totalExpenses: 89400,
    profit: 36200,
    materials: 156,
    employees: 24,
    customers: 89,
  }

  const recentActivities = [
    { id: 1, type: '收入', amount: 5600, description: '客户A项目结算', time: '2小时前' },
    { id: 2, type: '支出', amount: 1200, description: '办公用品采购', time: '4小时前' },
    { id: 3, type: '收入', amount: 8900, description: '客户B平方结算', time: '6小时前' },
    { id: 4, type: '支出', amount: 3400, description: '员工差旅费报销', time: '1天前' },
  ]

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">仪表板</Title>
        <Paragraph className="page-description">
          企业财务数据概览和快捷操作
        </Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收入"
              value={stats.totalRevenue}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix={<ArrowUpOutlined />}
              suffix="元"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总支出"
              value={stats.totalExpenses}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              prefix={<ArrowDownOutlined />}
              suffix="元"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="净利润"
              value={stats.profit}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              prefix={<DollarOutlined />}
              suffix="元"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="利润率"
              value={(stats.profit / stats.totalRevenue * 100)}
              precision={1}
              valueStyle={{ color: '#722ed1' }}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 快捷操作 */}
        <Col xs={24} lg={12}>
          <Card title="快捷操作" extra={<Button type="link">更多</Button>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Button type="primary" block icon={<ShoppingCartOutlined />}>
                    添加耗材
                  </Button>
                </Col>
                <Col span={12}>
                  <Button block icon={<TeamOutlined />}>
                    员工报销
                  </Button>
                </Col>
              </Row>
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Button block icon={<UserOutlined />}>
                    客户结算
                  </Button>
                </Col>
                <Col span={12}>
                  <Button block icon={<DollarOutlined />}>
                    付款申请
                  </Button>
                </Col>
              </Row>
            </Space>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card title="最近活动" extra={<Button type="link">查看全部</Button>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {recentActivities.map(activity => (
                <div key={activity.id} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <div>
                    <div style={{ fontWeight: 500 }}>
                      <span style={{ 
                        color: activity.type === '收入' ? '#52c41a' : '#ff4d4f',
                        marginRight: 8 
                      }}>
                        {activity.type === '收入' ? '+' : '-'}¥{activity.amount}
                      </span>
                    </div>
                    <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                      {activity.description}
                    </div>
                  </div>
                  <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                    {activity.time}
                  </div>
                </div>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 业务概览 */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="耗材种类"
              value={stats.materials}
              prefix={<ShoppingCartOutlined />}
              suffix="种"
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="员工数量"
              value={stats.employees}
              prefix={<TeamOutlined />}
              suffix="人"
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="客户数量"
              value={stats.customers}
              prefix={<UserOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
