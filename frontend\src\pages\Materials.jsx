import React, { useState } from 'react'
import { Table, Button, Space, Input, Select, Card, Typography, Modal, Form, InputNumber, message } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography
const { Option } = Select

function Materials() {
  const [materials, setMaterials] = useState([
    {
      id: 1,
      name: '办公纸张',
      category: '办公用品',
      unit: '包',
      price: 25.00,
      stock: 50,
      supplier: '办公用品公司A',
      status: '正常',
      createTime: '2025-05-01',
    },
    {
      id: 2,
      name: '打印墨盒',
      category: '办公用品',
      unit: '个',
      price: 120.00,
      stock: 15,
      supplier: '办公用品公司B',
      status: '库存不足',
      createTime: '2025-05-02',
    },
    {
      id: 3,
      name: '清洁用品',
      category: '日用品',
      unit: '套',
      price: 45.00,
      stock: 30,
      supplier: '清洁用品公司',
      status: '正常',
      createTime: '2025-05-03',
    },
  ])

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingMaterial, setEditingMaterial] = useState(null)
  const [form] = Form.useForm()

  const columns = [
    {
      title: '耗材名称',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      filters: [
        { text: '办公用品', value: '办公用品' },
        { text: '日用品', value: '日用品' },
      ],
      onFilter: (value, record) => record.category === value,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      render: (price) => `¥${price.toFixed(2)}`,
      sorter: (a, b) => a.price - b.price,
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
      sorter: (a, b) => a.stock - b.stock,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <span style={{ 
          color: status === '正常' ? '#52c41a' : '#ff4d4f',
          fontWeight: 500 
        }}>
          {status}
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: (a, b) => new Date(a.createTime) - new Date(b.createTime),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ]

  const handleAdd = () => {
    setEditingMaterial(null)
    form.resetFields()
    setIsModalVisible(true)
  }

  const handleEdit = (material) => {
    setEditingMaterial(material)
    form.setFieldsValue(material)
    setIsModalVisible(true)
  }

  const handleDelete = (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个耗材吗？',
      onOk() {
        setMaterials(materials.filter(item => item.id !== id))
        message.success('删除成功')
      },
    })
  }

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      
      if (editingMaterial) {
        // 编辑
        setMaterials(materials.map(item => 
          item.id === editingMaterial.id 
            ? { ...item, ...values }
            : item
        ))
        message.success('更新成功')
      } else {
        // 新增
        const newMaterial = {
          id: Date.now(),
          ...values,
          status: values.stock > 10 ? '正常' : '库存不足',
          createTime: new Date().toISOString().split('T')[0],
        }
        setMaterials([...materials, newMaterial])
        message.success('添加成功')
      }
      
      setIsModalVisible(false)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">耗材管理</Title>
        <Paragraph className="page-description">
          管理企业耗材库存、采购和消耗记录
        </Paragraph>
      </div>

      <Card>
        <div className="table-toolbar">
          <div className="table-toolbar-left">
            <Input
              placeholder="搜索耗材名称"
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
            />
            <Select placeholder="选择分类" style={{ width: 120 }}>
              <Option value="">全部</Option>
              <Option value="办公用品">办公用品</Option>
              <Option value="日用品">日用品</Option>
            </Select>
          </div>
          <div className="table-toolbar-right">
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加耗材
            </Button>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={materials}
          rowKey="id"
          pagination={{
            total: materials.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingMaterial ? '编辑耗材' : '添加耗材'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => setIsModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            category: '办公用品',
            unit: '个',
          }}
        >
          <Form.Item
            name="name"
            label="耗材名称"
            rules={[{ required: true, message: '请输入耗材名称' }]}
          >
            <Input placeholder="请输入耗材名称" />
          </Form.Item>

          <Form.Item
            name="category"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              <Option value="办公用品">办公用品</Option>
              <Option value="日用品">日用品</Option>
              <Option value="设备配件">设备配件</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="unit"
            label="单位"
            rules={[{ required: true, message: '请输入单位' }]}
          >
            <Input placeholder="如：个、包、套等" />
          </Form.Item>

          <Form.Item
            name="price"
            label="单价"
            rules={[{ required: true, message: '请输入单价' }]}
          >
            <InputNumber
              placeholder="请输入单价"
              min={0}
              precision={2}
              style={{ width: '100%' }}
              addonBefore="¥"
            />
          </Form.Item>

          <Form.Item
            name="stock"
            label="库存数量"
            rules={[{ required: true, message: '请输入库存数量' }]}
          >
            <InputNumber
              placeholder="请输入库存数量"
              min={0}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="supplier"
            label="供应商"
            rules={[{ required: true, message: '请输入供应商' }]}
          >
            <Input placeholder="请输入供应商名称" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Materials
