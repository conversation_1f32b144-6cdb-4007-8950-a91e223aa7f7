import React from 'react'
import { Typography, Card } from 'antd'

const { Title, Paragraph } = Typography

function Settings() {
  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">基础设置</Title>
        <Paragraph className="page-description">
          管理客户、供应商、产品、员工等基础信息
        </Paragraph>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Title level={4}>功能开发中...</Title>
          <Paragraph>基础设置功能正在开发中，敬请期待。</Paragraph>
        </div>
      </Card>
    </div>
  )
}

export default Settings
