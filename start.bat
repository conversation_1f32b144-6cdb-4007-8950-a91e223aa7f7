@echo off
echo ========================================
echo    企业财务管理系统 - 快速启动
echo ========================================
echo.

echo [1/2] 正在启动后端服务...
start "ERP后端服务" cmd /k "cd backend & node src/app.js"

echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

echo [2/2] 打开系统测试页面...
start "" "test-frontend.html"

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================
echo 📊 后端API服务: http://localhost:3001
echo 🌐 系统测试页面: test-frontend.html
echo 📋 API健康检查: http://localhost:3001/api/health
echo.
echo 💡 提示：
echo - 测试页面可以验证所有API功能
echo - 后端服务窗口请保持打开
echo - 如需完整前端，请运行: cd frontend && npm run dev
echo.
pause
