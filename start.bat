@echo off
setlocal enabledelayedexpansion
chcp 936 >nul 2>&1
cls

echo ========================================
echo    企业财务管理系统 - 完整版启动
echo ========================================
echo.

echo [1/3] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ 错误：未找到Node.js
    echo   请先安装Node.js或使用HTML版本
    echo   推荐运行: 简单启动.bat
    goto end
)
echo ✓ Node.js环境正常

echo [2/3] 启动后端服务...
cd /d "%~dp0"
if exist "backend\src\app.js" (
    start "ERP后端服务" cmd /k "cd /d \"%~dp0backend\" && node src\app.js"
    echo ✓ 后端服务启动中...
    timeout /t 5 /nobreak >nul
) else (
    echo ✗ 错误：找不到后端文件
    echo   请检查backend目录是否存在
    goto end
)

echo [3/3] 打开系统测试页面...
if exist "test-frontend.html" (
    start "" "%~dp0test-frontend.html" >nul 2>&1
    echo ✓ 测试页面已打开
) else (
    echo ✗ 警告：找不到测试页面
    echo   请检查test-frontend.html是否存在
)

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================
echo 📊 后端API服务: http://localhost:3001
echo 🌐 系统测试页面: test-frontend.html
echo 📋 API健康检查: http://localhost:3001/api/health
echo.
echo 💡 提示：
echo - 测试页面可以验证所有API功能
echo - 后端服务窗口请保持打开
echo - 如需完整前端，请运行: cd frontend && npm run dev
echo - 如遇问题，推荐使用: 简单启动.bat
echo.

:end
pause
