<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业财务管理系统 - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .header h1 {
            color: #1890ff;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #666;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .api-test {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .api-test h2 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .result {
            background: #f6f6f6;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            color: #52c41a;
        }
        
        .error {
            color: #ff4d4f;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .feature-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card h3 {
            color: #1890ff;
            margin-bottom: 10px;
        }
        
        .feature-card ul {
            list-style: none;
        }
        
        .feature-card li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-card li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 企业财务管理系统</h1>
            <p>基于您的2025年5月账单表格开发的现代化财务管理系统</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>总收入</h3>
                <div class="value">¥125,600</div>
            </div>
            <div class="stat-card">
                <h3>总支出</h3>
                <div class="value">¥89,400</div>
            </div>
            <div class="stat-card">
                <h3>净利润</h3>
                <div class="value">¥36,200</div>
            </div>
            <div class="stat-card">
                <h3>利润率</h3>
                <div class="value">28.8%</div>
            </div>
        </div>
        
        <div class="api-test">
            <h2>🔧 API 测试</h2>
            <p>测试后端API接口是否正常工作：</p>
            <br>
            <button class="btn" onclick="testHealth()">健康检查</button>
            <button class="btn" onclick="testMaterials()">获取耗材列表</button>
            <button class="btn" onclick="testEmployees()">获取员工列表</button>
            <button class="btn" onclick="testCustomers()">获取客户列表</button>
            <button class="btn" onclick="testDashboard()">获取仪表板数据</button>
            <div id="result" class="result" style="display: none;"></div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <h3>📦 耗材管理</h3>
                <ul>
                    <li>✅ 物料库存管理</li>
                    <li>✅ 采购记录</li>
                    <li>✅ 消耗统计</li>
                    <li>✅ 供应商管理</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>👥 员工支出</h3>
                <ul>
                    <li>🚧 费用申请</li>
                    <li>🚧 审批流程</li>
                    <li>🚧 报销管理</li>
                    <li>🚧 统计分析</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🤝 客户结算</h3>
                <ul>
                    <li>🚧 账单管理</li>
                    <li>🚧 收款记录</li>
                    <li>🚧 应收账款</li>
                    <li>🚧 客户信息</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>📐 平方结算</h3>
                <ul>
                    <li>🚧 面积计费</li>
                    <li>🚧 项目管理</li>
                    <li>🚧 单价设置</li>
                    <li>🚧 结算统计</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>💳 付款管理</h3>
                <ul>
                    <li>🚧 付款申请</li>
                    <li>🚧 审批流程</li>
                    <li>🚧 付款记录</li>
                    <li>🚧 资金统计</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>📊 财务统计</h3>
                <ul>
                    <li>🚧 收支分析</li>
                    <li>🚧 利润统计</li>
                    <li>🚧 财务报表</li>
                    <li>🚧 趋势分析</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }
        
        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`错误: ${error.message}`, true);
            }
        }
        
        async function testMaterials() {
            try {
                const response = await fetch(`${API_BASE}/materials`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`错误: ${error.message}`, true);
            }
        }
        
        async function testEmployees() {
            try {
                const response = await fetch(`${API_BASE}/employees`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`错误: ${error.message}`, true);
            }
        }
        
        async function testCustomers() {
            try {
                const response = await fetch(`${API_BASE}/customers`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`错误: ${error.message}`, true);
            }
        }
        
        async function testDashboard() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/stats`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`错误: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
