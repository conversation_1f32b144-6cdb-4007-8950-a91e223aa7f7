@echo off
echo ========================================
echo    企业财务管理系统 - 简化版启动
echo ========================================
echo.

echo 🚀 正在启动财务管理系统...
echo.

echo 📋 系统说明：
echo - 这是一个纯HTML版本的财务管理系统
echo - 不需要安装Node.js或其他依赖
echo - 数据保存在浏览器本地存储中
echo - 包含完整的耗材管理功能
echo.

echo 🌐 正在打开系统...
start "" "erp-system.html"

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================
echo 📊 系统界面: erp-system.html
echo 💾 数据存储: 浏览器本地存储
echo 🔧 测试页面: test-frontend.html
echo.
echo 💡 使用说明：
echo 1. 系统已在浏览器中打开
echo 2. 点击左侧菜单切换功能模块
echo 3. 耗材管理功能完全可用
echo 4. 数据会自动保存在浏览器中
echo.
echo ⚠️  注意：
echo - 如需Node.js版本，请先安装Node.js
echo - 清除浏览器数据会丢失所有记录
echo - 建议定期导出重要数据
echo.
pause
