@echo off
setlocal enabledelayedexpansion
chcp 936 >nul 2>&1
cls

echo ==========================================
echo        系统环境检测工具
echo ==========================================
echo.
echo 当前目录: %CD%
echo.

echo [1/4] 检查系统文件...
cd /d "%~dp0"

if exist "erp-system.html" (
    echo ✓ erp-system.html 存在
) else (
    echo ✗ erp-system.html 不存在
)

if exist "test-frontend.html" (
    echo ✓ test-frontend.html 存在
) else (
    echo ✗ test-frontend.html 不存在
)

if exist "backend\src\app.js" (
    echo ✓ backend\src\app.js 存在
) else (
    echo ✗ backend\src\app.js 不存在
)

echo.
echo [2/4] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Node.js 可用
    for /f "tokens=*" %%i in ('node --version 2^>nul') do echo   版本: %%i
) else (
    echo ✗ Node.js 不可用
    echo   建议使用HTML版本
)

echo.
echo [3/4] 检查浏览器支持...
echo ✓ 系统支持HTML5应用

echo.
echo [4/4] 启动HTML版本系统...
if exist "erp-system.html" (
    start "" "%~dp0erp-system.html" >nul 2>&1
    echo ✓ 系统已在浏览器中打开
) else (
    echo ✗ 无法启动：缺少系统文件
)

echo.
echo ==========================================
echo 检测完成！
echo ==========================================
echo.
pause
