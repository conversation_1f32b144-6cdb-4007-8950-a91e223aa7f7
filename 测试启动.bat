@echo off
echo 测试启动脚本
echo 当前目录: %CD%
echo.

echo 检查文件是否存在:
if exist "erp-system.html" (
    echo ✓ erp-system.html 存在
) else (
    echo ✗ erp-system.html 不存在
)

if exist "test-frontend.html" (
    echo ✓ test-frontend.html 存在
) else (
    echo ✗ test-frontend.html 不存在
)

if exist "backend\src\app.js" (
    echo ✓ backend\src\app.js 存在
) else (
    echo ✗ backend\src\app.js 不存在
)

echo.
echo 测试Node.js:
node --version 2>nul
if %errorlevel% equ 0 (
    echo ✓ Node.js 可用
) else (
    echo ✗ Node.js 不可用
)

echo.
echo 现在打开HTML版本...
start "" "erp-system.html"

echo.
pause
