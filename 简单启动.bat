@echo off
setlocal enabledelayedexpansion
chcp 936 >nul 2>&1
cls

echo.
echo ==========================================
echo        企业财务管理系统启动器
echo ==========================================
echo.
echo 请选择启动方式：
echo.
echo [1] HTML版本 - 无需Node.js，立即可用
echo [2] 完整版本 - 需要Node.js，功能更强
echo [3] 仅打开测试页面
echo [4] 退出
echo.
set /p choice=请输入选择 (1-4):

if "%choice%"=="1" goto html_version
if "%choice%"=="2" goto full_version
if "%choice%"=="3" goto test_page
if "%choice%"=="4" goto exit
goto invalid

:html_version
echo.
echo [1/1] 正在启动HTML版本...
cd /d "%~dp0"
if exist "erp-system.html" (
    start "" "%~dp0erp-system.html" >nul 2>&1
    echo ✓ HTML版本已成功启动！
    echo ✓ 系统界面已在浏览器中打开
) else (
    echo ✗ 错误：找不到 erp-system.html 文件
    echo   请确保文件在当前目录中
)
goto end

:full_version
echo.
echo [1/3] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ 错误：未找到Node.js
    echo   请先安装Node.js后再使用完整版本
    echo   推荐使用HTML版本（选项1）
    goto end
)
echo ✓ Node.js环境正常

echo [2/3] 启动后端服务...
cd /d "%~dp0"
if exist "backend\src\app.js" (
    start "ERP后端服务" cmd /c "cd /d \"%~dp0backend\" && node src\app.js && pause"
    echo ✓ 后端服务启动中...
    timeout /t 3 /nobreak >nul
) else (
    echo ✗ 错误：找不到后端文件
    echo   使用HTML版本代替...
    goto html_version
)

echo [3/3] 打开测试页面...
if exist "test-frontend.html" (
    start "" "%~dp0test-frontend.html" >nul 2>&1
    echo ✓ 完整版本已启动！
    echo ✓ 后端服务：http://localhost:3001
    echo ✓ 测试页面已打开
) else (
    echo ✗ 警告：找不到测试页面，使用HTML版本
    start "" "%~dp0erp-system.html" >nul 2>&1
)
goto end

:test_page
echo.
echo [1/1] 正在打开测试页面...
cd /d "%~dp0"
if exist "test-frontend.html" (
    start "" "%~dp0test-frontend.html" >nul 2>&1
    echo ✓ 测试页面已打开！
) else (
    echo ✗ 错误：找不到 test-frontend.html
    echo   使用HTML版本代替...
    start "" "%~dp0erp-system.html" >nul 2>&1
)
goto end

:invalid
echo.
echo ✗ 无效选择，请重新运行脚本
goto end

:exit
echo.
echo 退出启动器
goto end

:end
echo.
echo ==========================================
echo 启动完成！
echo ==========================================
echo.
echo 💡 提示：
echo   - 如果页面未打开，请手动双击 erp-system.html
echo   - 系统数据保存在浏览器本地存储中
echo   - 关闭此窗口不影响系统运行
echo.
pause
