@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo        企业财务管理系统启动器
echo ==========================================
echo.
echo 请选择启动方式：
echo.
echo [1] HTML版本 - 无需Node.js，立即可用
echo [2] 完整版本 - 需要Node.js，功能更强
echo [3] 仅打开测试页面
echo [4] 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto html_version
if "%choice%"=="2" goto full_version  
if "%choice%"=="3" goto test_page
if "%choice%"=="4" goto exit
goto invalid

:html_version
echo.
echo 正在启动HTML版本...
start "" "erp-system.html"
echo HTML版本已启动！
goto end

:full_version
echo.
echo 正在启动完整版本...
echo 启动后端服务...
cd /d "%~dp0"
start "ERP后端服务" cmd /c "cd backend & node src/app.js & pause"
timeout /t 3 >nul
echo 打开测试页面...
start "" "test-frontend.html"
echo 完整版本已启动！
goto end

:test_page
echo.
echo 正在打开测试页面...
start "" "test-frontend.html"
echo 测试页面已打开！
goto end

:invalid
echo.
echo 无效选择，请重新运行脚本。
goto end

:exit
echo.
echo 退出启动器。
goto end

:end
echo.
echo ==========================================
echo 启动完成！
echo ==========================================
echo.
pause
